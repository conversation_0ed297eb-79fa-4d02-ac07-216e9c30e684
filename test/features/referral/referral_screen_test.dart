import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mockito/mockito.dart';

import 'package:banachef/features/referral/cubit/referral_cubit.dart';
import 'package:banachef/features/referral/cubit/referral_state.dart';
import 'package:banachef/features/referral/services/referral_service.dart';
import 'package:banachef/features/referral/models/referral_response.dart';
import 'package:banachef/features/auth/services/token_service.dart';
import 'package:banachef/core/services/logger/logger.dart';

// Mock services
class MockReferralService extends Mock implements ReferralService {
  @override
  Future<ReferralResponse> applyReferralCode(String referralCode) =>
      super.noSuchMethod(
        Invocation.method(#applyReferralCode, [referralCode]),
        returnValue: Future.value(const ReferralResponse(
          success: false,
          message: 'Mock response',
        )),
      );
}

class MockTokenService extends Mock implements TokenService {}

void main() {
  // Initialize logger once for all tests
  setUpAll(() {
    AppLogger.initialize(false);
  });

  group('ReferralCubit', () {
    late MockReferralService mockReferralService;
    late MockTokenService mockTokenService;
    late ReferralCubit referralCubit;

    setUp(() {
      mockReferralService = MockReferralService();
      mockTokenService = MockTokenService();
      referralCubit = ReferralCubit(mockReferralService, mockTokenService);
    });

    tearDown(() {
      referralCubit.close();
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: Scaffold(
          body: BlocProvider<ReferralCubit>(
            create: (context) => referralCubit,
            child: BlocBuilder<ReferralCubit, ReferralState>(
              builder: (context, state) {
                if (state is ReferralLoading) {
                  return const CircularProgressIndicator();
                } else if (state is ReferralSuccess) {
                  return const Text('Success');
                } else if (state is ReferralError) {
                  return Text(state.message);
                }
                return const Text('Initial');
              },
            ),
          ),
        ),
      );
    }

    test('should start with initial state', () {
      expect(referralCubit.state, isA<ReferralInitial>());
    });

    test('should emit error when applying empty code', () async {
      // Act
      await referralCubit.applyReferralCode('');

      // Assert
      expect(referralCubit.state, isA<ReferralError>());
      final errorState = referralCubit.state as ReferralError;
      expect(errorState.message, 'Vui lòng nhập mã giới thiệu');
      expect(errorState.errorCode, 'EMPTY_CODE');
    });

    test('should emit error when applying whitespace code', () async {
      // Act
      await referralCubit.applyReferralCode('   ');

      // Assert
      expect(referralCubit.state, isA<ReferralError>());
      final errorState = referralCubit.state as ReferralError;
      expect(errorState.message, 'Vui lòng nhập mã giới thiệu');
    });

    test('should apply referral code successfully', () async {
      // Arrange
      const mockResponse = ReferralResponse(
        success: true,
        message: 'Thành công! Bạn đã nhận được ưu đãi giảm 5\$ cho lần mua đầu tiên.',
        discountAmount: 5.0,
        discountType: 'fixed',
      );
      when(mockReferralService.applyReferralCode('TEST123'))
          .thenAnswer((_) async => mockResponse);

      // Act
      await referralCubit.applyReferralCode('TEST123');

      // Assert
      expect(referralCubit.state, isA<ReferralSuccess>());
      final successState = referralCubit.state as ReferralSuccess;
      expect(successState.data, mockResponse);
      expect(successState.message, mockResponse.message);
    });

    test('should reset to initial state', () {
      // Arrange
      referralCubit.emit(const ReferralError(message: 'Test error'));

      // Act
      referralCubit.reset();

      // Assert
      expect(referralCubit.state, isA<ReferralInitial>());
    });

    testWidgets('should show loading state when applying code', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Emit loading state
      referralCubit.emit(const ReferralLoading(loadingMessage: 'Đang xử lý...'));
      await tester.pump();

      // Check if loading indicator is shown
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should show success state when code applied successfully', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Emit success state
      referralCubit.emit(ReferralSuccess(
        data: const ReferralResponse(
          success: true,
          message: 'Thành công! Bạn đã nhận được ưu đãi giảm 5\$ cho lần mua đầu tiên.',
        ),
        message: 'Thành công!',
      ));
      await tester.pump();

      // Check if success text is shown
      expect(find.text('Success'), findsOneWidget);
    });

    testWidgets('should show error state when code application fails', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Emit error state
      referralCubit.emit(const ReferralError(
        message: 'Mã không hợp lệ. Vui lòng kiểm tra lại.',
      ));
      await tester.pump();

      // Check if error message is shown
      expect(find.text('Mã không hợp lệ. Vui lòng kiểm tra lại.'), findsOneWidget);
    });
  });
}
