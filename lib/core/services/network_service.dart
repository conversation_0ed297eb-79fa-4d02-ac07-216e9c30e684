import 'package:injectable/injectable.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

abstract class NetworkService {
  Future<bool> get isConnected;
  Stream<bool> get onConnectivityChanged;
}

@Singleton(as: NetworkService)
class NetworkServiceImpl implements NetworkService {
  final InternetConnectionChecker _connectionChecker;

  NetworkServiceImpl(this._connectionChecker);

  @override
  Future<bool> get isConnected => _connectionChecker.hasConnection;

  @override
  Stream<bool> get onConnectivityChanged => 
      _connectionChecker.onStatusChange.map(
        (status) => status == InternetConnectionStatus.connected,
      );
}
