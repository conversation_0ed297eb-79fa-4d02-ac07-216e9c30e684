import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';

abstract class StorageService {
  // SharedPreferences methods for app settings and preferences
  Future<bool> setBool(String key, bool value);
  Future<bool> setString(String key, String value);
  Future<bool> setInt(String key, int value);
  Future<bool> setDouble(String key, double value);
  Future<bool> setStringList(String key, List<String> value);

  bool? getBool(String key);
  String? getString(String key);
  int? getInt(String key);
  double? getDouble(String key);
  List<String>? getStringList(String key);

  Future<bool> remove(String key);
  Future<bool> clear();

  // App-specific preference methods
  Future<void> saveThemeMode(String themeMode);
  Future<String?> getThemeMode();
  Future<void> saveLanguage(String language);
  Future<String?> getLanguage();
  Future<void> saveOnboardingCompleted(bool completed);
  Future<bool> isOnboardingCompleted();
  Future<void> saveNotificationSettings(Map<String, bool> settings);
  Future<Map<String, bool>> getNotificationSettings();
}

@Singleton(as: StorageService)
class StorageServiceImpl implements StorageService {
  final SharedPreferences _prefs;

  StorageServiceImpl(this._prefs);

  // SharedPreferences implementations
  @override
  Future<bool> setBool(String key, bool value) => _prefs.setBool(key, value);

  @override
  Future<bool> setString(String key, String value) =>
      _prefs.setString(key, value);

  @override
  Future<bool> setInt(String key, int value) => _prefs.setInt(key, value);

  @override
  Future<bool> setDouble(String key, double value) =>
      _prefs.setDouble(key, value);

  @override
  Future<bool> setStringList(String key, List<String> value) =>
      _prefs.setStringList(key, value);

  @override
  bool? getBool(String key) => _prefs.getBool(key);

  @override
  String? getString(String key) => _prefs.getString(key);

  @override
  int? getInt(String key) => _prefs.getInt(key);

  @override
  double? getDouble(String key) => _prefs.getDouble(key);

  @override
  List<String>? getStringList(String key) => _prefs.getStringList(key);

  @override
  Future<bool> remove(String key) => _prefs.remove(key);

  @override
  Future<bool> clear() => _prefs.clear();

  // App-specific preference implementations
  @override
  Future<void> saveThemeMode(String themeMode) =>
      setString('theme_mode', themeMode);

  @override
  Future<String?> getThemeMode() async => getString('theme_mode');

  @override
  Future<void> saveLanguage(String language) => setString('language', language);

  @override
  Future<String?> getLanguage() async => getString('language');

  @override
  Future<void> saveOnboardingCompleted(bool completed) =>
      setBool('onboarding_completed', completed);

  @override
  Future<bool> isOnboardingCompleted() async =>
      getBool('onboarding_completed') ?? false;

  @override
  Future<void> saveNotificationSettings(Map<String, bool> settings) async {
    for (final entry in settings.entries) {
      await setBool('notification_${entry.key}', entry.value);
    }
  }

  @override
  Future<Map<String, bool>> getNotificationSettings() async {
    return {
      'push_notifications': getBool('notification_push_notifications') ?? true,
      'email_notifications':
          getBool('notification_email_notifications') ?? true,
      'recipe_recommendations':
          getBool('notification_recipe_recommendations') ?? true,
      'meal_plan_reminders':
          getBool('notification_meal_plan_reminders') ?? true,
    };
  }
}
