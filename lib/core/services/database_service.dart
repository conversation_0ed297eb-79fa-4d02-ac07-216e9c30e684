import 'package:injectable/injectable.dart';
import '../database/app_database.dart';

abstract class DatabaseService {
  AppDatabase get database;
  Future<void> initialize();
  Future<void> close();
  Future<void> clearAllData();
}

@Singleton(as: DatabaseService)
class DatabaseServiceImpl implements DatabaseService {
  AppDatabase? _database;

  @override
  AppDatabase get database {
    if (_database == null) {
      throw StateError('Database not initialized. Call initialize() first.');
    }
    return _database!;
  }

  @override
  Future<void> initialize() async {
    if (_database == null) {
      _database = AppDatabase();
      
      // Ensure database is properly initialized
      await _database!.customSelect('SELECT 1').get();
    }
  }

  @override
  Future<void> close() async {
    await _database?.close();
    _database = null;
  }

  @override
  Future<void> clearAllData() async {
    if (_database != null) {
      // Delete all data from all tables
      await _database!.delete(_database!.imageScanHistories).go();
      await _database!.delete(_database!.localIngredients).go();
      await _database!.delete(_database!.mealPlans).go();
      await _database!.delete(_database!.localRecipes).go();
      await _database!.delete(_database!.localUsers).go();
      await _database!.delete(_database!.localUserProfiles).go();
      await _database!.delete(_database!.shoppingListItems).go();
      await _database!.delete(_database!.userTemporaryStates).go();
      await _database!.delete(_database!.localRecipeIngredients).go();
      
    }
  }
}
