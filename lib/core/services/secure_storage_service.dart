import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:injectable/injectable.dart';

abstract class SecureStorageService {
  Future<void> write(String key, String value);
  Future<String?> read(String key);
  Future<void> delete(String key);
  Future<void> deleteAll();
  Future<Map<String, String>> readAll();
  Future<bool> containsKey(String key);
  
  // Auth specific methods
  Future<void> saveAccessToken(String token);
  Future<void> saveRefreshToken(String token);
  Future<String?> getAccessToken();
  Future<String?> getRefreshToken();
  Future<void> clearAuthTokens();
  
  // User data methods
  Future<void> saveUserData(String userData);
  Future<String?> getUserData();
  Future<void> clearUserData();
  
  // Biometric/PIN methods
  Future<void> saveBiometricEnabled(bool enabled);
  Future<bool> isBiometricEnabled();
  Future<void> savePinCode(String pinCode);
  Future<String?> getPinCode();
  Future<void> clearSecuritySettings();
}

@Singleton(as: SecureStorageService)
class SecureStorageServiceImpl implements SecureStorageService {
  static const _accessTokenKey = 'access_token';
  static const _refreshTokenKey = 'refresh_token';
  static const _userDataKey = 'user_data';
  static const _biometricEnabledKey = 'biometric_enabled';
  static const _pinCodeKey = 'pin_code';

  final FlutterSecureStorage _secureStorage;

  SecureStorageServiceImpl(this._secureStorage);

  @override
  Future<void> write(String key, String value) async {
    await _secureStorage.write(key: key, value: value);
  }

  @override
  Future<String?> read(String key) async {
    return await _secureStorage.read(key: key);
  }

  @override
  Future<void> delete(String key) async {
    await _secureStorage.delete(key: key);
  }

  @override
  Future<void> deleteAll() async {
    await _secureStorage.deleteAll();
  }

  @override
  Future<Map<String, String>> readAll() async {
    return await _secureStorage.readAll();
  }

  @override
  Future<bool> containsKey(String key) async {
    return await _secureStorage.containsKey(key: key);
  }

  // Auth specific implementations
  @override
  Future<void> saveAccessToken(String token) async {
    await write(_accessTokenKey, token);
  }

  @override
  Future<void> saveRefreshToken(String token) async {
    await write(_refreshTokenKey, token);
  }

  @override
  Future<String?> getAccessToken() async {
    return await read(_accessTokenKey);
  }

  @override
  Future<String?> getRefreshToken() async {
    return await read(_refreshTokenKey);
  }

  @override
  Future<void> clearAuthTokens() async {
    await delete(_accessTokenKey);
    await delete(_refreshTokenKey);
  }

  // User data implementations
  @override
  Future<void> saveUserData(String userData) async {
    await write(_userDataKey, userData);
  }

  @override
  Future<String?> getUserData() async {
    return await read(_userDataKey);
  }

  @override
  Future<void> clearUserData() async {
    await delete(_userDataKey);
  }

  // Biometric/PIN implementations
  @override
  Future<void> saveBiometricEnabled(bool enabled) async {
    await write(_biometricEnabledKey, enabled.toString());
  }

  @override
  Future<bool> isBiometricEnabled() async {
    final value = await read(_biometricEnabledKey);
    return value?.toLowerCase() == 'true';
  }

  @override
  Future<void> savePinCode(String pinCode) async {
    await write(_pinCodeKey, pinCode);
  }

  @override
  Future<String?> getPinCode() async {
    return await read(_pinCodeKey);
  }

  @override
  Future<void> clearSecuritySettings() async {
    await delete(_biometricEnabledKey);
    await delete(_pinCodeKey);
  }
}
