import 'dart:async';

import 'base_cubit.dart';
import 'base_state.dart';

/// Common cubit patterns for frequently used scenarios

/// Simple data cubit for basic CRUD operations
abstract class DataCubit<T> extends BaseCubit<BaseState> {
  DataCubit() : super(const GenericInitialState());

  /// Load data method - must be implemented by subclasses
  Future<T> loadData();

  /// Create data method - can be overridden by subclasses
  Future<T> createData(T data) async {
    throw UnimplementedError('Create operation not implemented');
  }

  /// Update data method - can be overridden by subclasses
  Future<T> updateData(T data) async {
    throw UnimplementedError('Update operation not implemented');
  }

  /// Delete data method - can be overridden by subclasses
  Future<void> deleteData(String id) async {
    throw UnimplementedError('Delete operation not implemented');
  }

  /// Load data with error handling
  Future<void> load() async {
    await executeWithErrorHandling(
      () => loadData(),
      loadingMessage: 'Loading data...',
      successMessage: 'Data loaded successfully',
    );
  }

  /// Create data with error handling
  Future<void> create(T data) async {
    await executeWithErrorHandling(
      () => createData(data),
      loadingMessage: 'Creating data...',
      successMessage: 'Data created successfully',
    );
  }

  /// Update data with error handling
  Future<void> update(T data) async {
    await executeWithErrorHandling(
      () => updateData(data),
      loadingMessage: 'Updating data...',
      successMessage: 'Data updated successfully',
    );
  }

  /// Delete data with error handling
  Future<void> delete(String id) async {
    await executeWithErrorHandling(
      () => deleteData(id),
      loadingMessage: 'Deleting data...',
      successMessage: 'Data deleted successfully',
    );
  }

  @override
  Future<void> refresh() async {
    await load();
  }

  @override
  Future<void> retry() async {
    await load();
  }
}

/// List cubit for managing lists of data
abstract class ListCubit<T> extends BaseCubit<BaseState> {
  ListCubit() : super(const GenericInitialState());

  /// Load list method - must be implemented by subclasses
  Future<List<T>> loadList();

  /// Add item method - can be overridden by subclasses
  Future<T> addItem(T item) async {
    throw UnimplementedError('Add operation not implemented');
  }

  /// Remove item method - can be overridden by subclasses
  Future<void> removeItem(String id) async {
    throw UnimplementedError('Remove operation not implemented');
  }

  /// Update item method - can be overridden by subclasses
  Future<T> updateItem(T item) async {
    throw UnimplementedError('Update operation not implemented');
  }

  /// Load list with error handling
  Future<void> loadItems() async {
    final result = await executeWithErrorHandling(
      () => loadList(),
      loadingMessage: 'Loading items...',
    );

    if (result != null) {
      if (result.isEmpty) {
        emit(const EmptyState(emptyMessage: 'No items found') as BaseState);
      } else {
        emitSuccess(data: result, message: 'Items loaded successfully');
      }
    }
  }

  /// Add item with error handling
  Future<void> add(T item) async {
    await executeWithErrorHandling(
      () => addItem(item),
      loadingMessage: 'Adding item...',
      successMessage: 'Item added successfully',
    );
  }

  /// Remove item with error handling
  Future<void> remove(String id) async {
    await executeWithErrorHandling(
      () => removeItem(id),
      loadingMessage: 'Removing item...',
      successMessage: 'Item removed successfully',
    );
  }

  /// Update item with error handling
  Future<void> update(T item) async {
    await executeWithErrorHandling(
      () => updateItem(item),
      loadingMessage: 'Updating item...',
      successMessage: 'Item updated successfully',
    );
  }

  @override
  Future<void> refresh() async {
    await loadItems();
  }

  @override
  Future<void> retry() async {
    await loadItems();
  }
}

/// Paginated list cubit for managing paginated data
abstract class PaginatedListCubit<T> extends BaseCubit<BaseState> {
  PaginatedListCubit() : super(const GenericInitialState());

  int _currentPage = 1;
  bool _hasMoreData = true;
  final List<T> _items = [];

  /// Load page method - must be implemented by subclasses
  Future<PaginatedResult<T>> loadPage(int page);

  /// Get current items
  List<T> get items => List.unmodifiable(_items);

  /// Get current page
  int get currentPage => _currentPage;

  /// Check if has more data
  bool get hasMoreData => _hasMoreData;

  /// Load first page
  Future<void> loadFirstPage() async {
    _currentPage = 1;
    _items.clear();
    _hasMoreData = true;

    final result = await executeWithErrorHandling(
      () => loadPage(_currentPage),
      loadingMessage: 'Loading data...',
    );

    if (result != null) {
      _items.addAll(result.items);
      _hasMoreData = result.hasMore;

      if (_items.isEmpty) {
        emit(const EmptyState(emptyMessage: 'No items found') as BaseState);
      } else {
        emitSuccess(data: _items, message: 'Data loaded successfully');
      }
    }
  }

  /// Load next page
  Future<void> loadNextPage() async {
    if (!_hasMoreData) return;

    _currentPage++;

    final result = await executeWithErrorHandling(
      () => loadPage(_currentPage),
      loadingMessage: 'Loading more data...',
    );

    if (result != null) {
      _items.addAll(result.items);
      _hasMoreData = result.hasMore;
      emitSuccess(data: _items, message: 'More data loaded');
    } else {
      _currentPage--; // Revert page increment on error
    }
  }

  @override
  Future<void> refresh() async {
    await loadFirstPage();
  }

  @override
  Future<void> retry() async {
    await loadFirstPage();
  }

  @override
  void reset() {
    _currentPage = 1;
    _hasMoreData = true;
    _items.clear();
    emit(const GenericInitialState() as BaseState);
  }
}

/// Result class for paginated data
class PaginatedResult<T> {
  final List<T> items;
  final bool hasMore;
  final int totalCount;

  const PaginatedResult({
    required this.items,
    required this.hasMore,
    this.totalCount = 0,
  });
}

/// Form cubit for managing form state
abstract class FormCubit<T> extends BaseCubit<BaseState> {
  FormCubit() : super(const GenericInitialState());

  /// Submit form method - must be implemented by subclasses
  Future<T> submitForm(Map<String, dynamic> formData);

  /// Validate form method - can be overridden by subclasses
  Map<String, String> validateForm(Map<String, dynamic> formData) {
    return {}; // Return empty map if no validation errors
  }

  /// Submit form with validation and error handling
  Future<void> submit(Map<String, dynamic> formData) async {
    // Validate form first
    final validationErrors = validateForm(formData);
    if (validationErrors.isNotEmpty) {
      emitError(
        message: 'Validation failed',
        originalError: validationErrors,
      );
      return;
    }

    // Submit form
    await executeWithErrorHandling(
      () => submitForm(formData),
      loadingMessage: 'Submitting form...',
      successMessage: 'Form submitted successfully',
    );
  }

  @override
  Future<void> retry() async {
    // Form retry should be handled by the UI
    // This is just a placeholder
  }
}

/// Toggle cubit for simple boolean state management
class ToggleCubit extends BaseCubit<BaseState> {
  ToggleCubit({bool initialValue = false})
      : super(GenericSuccessState<bool>(data: initialValue));

  /// Get current value
  bool get value {
    final currentState = state;
    if (currentState is BaseSuccessState<bool>) {
      return currentState.data ?? false;
    }
    return false;
  }

  /// Toggle the value
  void toggle() {
    emitSuccess(data: !value);
  }

  /// Set specific value
  void setValue(bool value) {
    emitSuccess(data: value);
  }

  @override
  void reset() {
    emitSuccess(data: false);
  }
}
