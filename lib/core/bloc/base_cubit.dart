import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';

import 'base_state.dart';
import '../errors/failures.dart';
import '../services/logger/logger.dart';

/// Base abstract class for all cubits in the application
///
/// Provides common functionality like error handling, logging,
/// and standard state management patterns for simpler state management
/// compared to full BLoC pattern
abstract class BaseCubit<State extends BaseState> extends Cubit<State> {
  /// Logger instance for this cubit
  late final Logger _logger;

  /// Constructor that sets up logging
  BaseCubit(super.initialState) {
    _logger = AppLogger.log;
    _logStateChanges();
  }

  /// Log state changes for debugging
  void _logStateChanges() {
    stream.listen((state) {
      _logger.d('Cubit state transition: ${state.stateName}');

      if (state.isError) {
        final errorState = state as BaseErrorState;
        _logger.e('Cubit error state: ${errorState.message}');
      }
    });
  }

  /// Handle errors in a consistent way across all cubits
  void handleError(
    dynamic error, {
    StackTrace? stackTrace,
  }) {
    String errorMessage;
    String? errorCode;

    if (error is Failure) {
      errorMessage = error.message;
      errorCode = error.statusCode?.toString();
    } else if (error is Exception) {
      errorMessage = error.toString();
    } else {
      errorMessage = 'An unexpected error occurred';
    }

    _logger.e(
      'Cubit error: $errorMessage',
      error: error,
      stackTrace: stackTrace,
    );

    // Emit error state if possible
    if (hasErrorState()) {
      emitError(
        message: errorMessage,
        errorCode: errorCode,
        originalError: error,
        stackTrace: stackTrace,
      );
    }
  }

  /// Check if this cubit supports error states
  bool hasErrorState() => true;

  /// Create an error state - subclasses should override this
  BaseErrorState createErrorState({
    required String message,
    String? errorCode,
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    return GenericErrorState(
      message: message,
      errorCode: errorCode,
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  /// Create a loading state - subclasses should override this
  BaseLoadingState createLoadingState({String? message}) {
    return GenericLoadingState(loadingMessage: message);
  }

  /// Create a success state - subclasses should override this
  BaseSuccessState<T> createSuccessState<T>({T? data, String? message}) {
    return GenericSuccessState<T>(data: data, message: message);
  }

  /// Emit loading state safely
  void emitLoading({String? message}) {
    if (hasLoadingState()) {
      emit(createLoadingState(message: message) as State);
    }
  }

  /// Emit success state safely
  void emitSuccess<T>({T? data, String? message}) {
    if (hasSuccessState()) {
      emit(createSuccessState<T>(data: data, message: message) as State);
    }
  }

  /// Emit error state safely
  void emitError({
    required String message,
    String? errorCode,
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    if (hasErrorState()) {
      emit(
        createErrorState(
          message: message,
          errorCode: errorCode,
          originalError: originalError,
          stackTrace: stackTrace,
        ) as State,
      );
    }
  }

  /// Check if this cubit supports loading states
  bool hasLoadingState() => true;

  /// Check if this cubit supports success states
  bool hasSuccessState() => true;

  /// Utility method to execute async operations with error handling
  Future<T?> executeWithErrorHandling<T>(
    Future<T> Function() operation, {
    String? loadingMessage,
    String? successMessage,
  }) async {
    try {
      emitLoading(message: loadingMessage);
      final result = await operation();

      if (successMessage != null) {
        emitSuccess(data: result, message: successMessage);
      }

      return result;
    } catch (error, stackTrace) {
      handleError(error, stackTrace: stackTrace);
      return null;
    }
  }

  /// Utility method to execute sync operations with error handling
  T? executeWithErrorHandlingSync<T>(
    T Function() operation, {
    String? successMessage,
  }) {
    try {
      final result = operation();

      if (successMessage != null) {
        emitSuccess(data: result, message: successMessage);
      }

      return result;
    } catch (error, stackTrace) {
      handleError(error, stackTrace: stackTrace);
      return null;
    }
  }

  /// Reset to initial state - subclasses should override if needed
  void reset() {
    // Subclasses should implement this to reset to their initial state
    _logger.d('Reset called on ${runtimeType.toString()}');
  }

  /// Refresh data - subclasses should override if needed
  Future<void> refresh() async {
    // Subclasses should implement this to refresh their data
    _logger.d('Refresh called on ${runtimeType.toString()}');
  }

  /// Retry last operation - subclasses should override if needed
  Future<void> retry() async {
    // Subclasses should implement this to retry their last operation
    _logger.d('Retry called on ${runtimeType.toString()}');
  }

  @override
  void onChange(Change<State> change) {
    super.onChange(change);
    _logger.d(
      'Cubit change: ${change.currentState.stateName} -> ${change.nextState.stateName}',
    );
  }

  @override
  void onError(Object error, StackTrace stackTrace) {
    super.onError(error, stackTrace);
    _logger.e(
      'Cubit error in $runtimeType',
      error: error,
      stackTrace: stackTrace,
    );
  }

  @override
  Future<void> close() {
    _logger.d('Closing cubit: ${runtimeType.toString()}');
    return super.close();
  }
}
