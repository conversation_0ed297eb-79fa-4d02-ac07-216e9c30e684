import 'package:equatable/equatable.dart';

/// Base abstract class for all states in the application
/// 
/// All states should extend this class to ensure consistency
/// and provide common functionality across the application
abstract class BaseState extends Equatable {
  const BaseState();

  @override
  List<Object?> get props => [];

  /// Optional timestamp for when the state was created
  DateTime get timestamp => DateTime.now();

  /// Optional state identifier for debugging purposes
  String get stateName => runtimeType.toString();

  /// Check if this is a loading state
  bool get isLoading => this is BaseLoadingState;

  /// Check if this is an error state
  bool get isError => this is BaseErrorState;

  /// Check if this is a success state
  bool get isSuccess => this is BaseSuccessState;

  /// Check if this is an initial state
  bool get isInitial => this is BaseInitialState;

  @override
  String toString() => '$stateName { timestamp: $timestamp }';
}

/// Base initial state
abstract class BaseInitialState extends BaseState {
  const BaseInitialState();

  @override
  String toString() => 'BaseInitialState';
}

/// Base loading state
abstract class BaseLoadingState extends BaseState {
  const BaseLoadingState();

  @override
  String toString() => 'BaseLoadingState';
}

/// Base success state
abstract class BaseSuccessState<T> extends BaseState {
  final T? data;
  final String? message;

  const BaseSuccessState({this.data, this.message});

  @override
  List<Object?> get props => [data, message];

  @override
  String toString() => 'BaseSuccessState { data: $data, message: $message }';
}

/// Base error state
abstract class BaseErrorState extends BaseState {
  final String message;
  final String? errorCode;
  final dynamic originalError;
  final StackTrace? stackTrace;

  const BaseErrorState({
    required this.message,
    this.errorCode,
    this.originalError,
    this.stackTrace,
  });

  @override
  List<Object?> get props => [message, errorCode, originalError];

  @override
  String toString() => 'BaseErrorState { message: $message, errorCode: $errorCode }';
}

/// Generic states that can be used across different blocs
class GenericInitialState extends BaseInitialState {
  const GenericInitialState();
}

class GenericLoadingState extends BaseLoadingState {
  final String? loadingMessage;

  const GenericLoadingState({this.loadingMessage});

  @override
  List<Object?> get props => [loadingMessage];

  @override
  String toString() => 'GenericLoadingState { message: $loadingMessage }';
}

class GenericSuccessState<T> extends BaseSuccessState<T> {
  const GenericSuccessState({super.data, super.message});

  @override
  String toString() => 'GenericSuccessState { data: $data, message: $message }';
}

class GenericErrorState extends BaseErrorState {
  const GenericErrorState({
    required super.message,
    super.errorCode,
    super.originalError,
    super.stackTrace,
  });

  @override
  String toString() => 'GenericErrorState { message: $message, errorCode: $errorCode }';
}

/// Empty state for when there's no data
class EmptyState extends BaseState {
  final String? emptyMessage;

  const EmptyState({this.emptyMessage});

  @override
  List<Object?> get props => [emptyMessage];

  @override
  String toString() => 'EmptyState { message: $emptyMessage }';
}
