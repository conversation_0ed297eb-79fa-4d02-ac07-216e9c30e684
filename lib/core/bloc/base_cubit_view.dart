import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'base_cubit.dart';
import 'base_state.dart';
import 'cubit_actions.dart'; // Import the new interfaces
import '../../shared/widgets/loading_widget.dart';
import '../../shared/widgets/error_widget.dart' as custom_widgets;
import '../../shared/widgets/empty_widget.dart';

/// Base widget for views that use cubit pattern
///
/// Provides common UI patterns like loading, error, and empty states
/// with consistent styling and behavior across the application.
/// Simpler than BaseView as it doesn't need event handling.
abstract class BaseCubitView<C extends BaseCubit<BaseState>>
    extends StatelessWidget {
  const BaseCubitView({super.key});

  /// Create the cubit instance - must be implemented by subclasses
  C createCubit(BuildContext context);

  /// Build the main content when state is successful
  Widget buildContent(BuildContext context, BaseState state);

  /// Build loading widget - can be overridden by subclasses
  Widget buildLoading(BuildContext context, BaseLoadingState state) {
    return LoadingWidget(
      message: state is GenericLoadingState ? state.loadingMessage : null,
    );
  }

  /// Build error widget - can be overridden by subclasses
  Widget buildError(BuildContext context, BaseErrorState state) {
    return custom_widgets.ErrorWidget(
      message: state.message,
      onRetry: () => _handleRetry(context),
    );
  }

  /// Build empty widget - can be overridden by subclasses
  Widget buildEmpty(BuildContext context, EmptyState state) {
    return EmptyWidget(message: state.emptyMessage);
  }

  /// Handle retry action
  void _handleRetry(BuildContext context) {
    final cubit = BlocProvider.of<C>(context);
    if (cubit is Retryable) {
      (cubit as Retryable).retry();
    } else if (cubit is Refreshable) {
      (cubit as Refreshable).refresh();
    }
    // If neither interface is implemented, do nothing.
  }

  /// Build the scaffold structure - can be overridden by subclasses
  Widget buildScaffold(
    BuildContext context, {
    required Widget body,
    PreferredSizeWidget? appBar,
    Widget? floatingActionButton,
    Widget? bottomNavigationBar,
    Widget? drawer,
  }) {
    return Scaffold(
      appBar: appBar,
      body: body,
      floatingActionButton: floatingActionButton,
      bottomNavigationBar: bottomNavigationBar,
      drawer: drawer,
    );
  }

  /// Get app bar - can be overridden by subclasses
  PreferredSizeWidget? getAppBar(BuildContext context) => null;

  /// Get floating action button - can be overridden by subclasses
  Widget? getFloatingActionButton(BuildContext context) => null;

  /// Get bottom navigation bar - can be overridden by subclasses
  Widget? getBottomNavigationBar(BuildContext context) => null;

  /// Get drawer - can be overridden by subclasses
  Widget? getDrawer(BuildContext context) => null;

  /// Handle state changes - can be overridden by subclasses
  void onStateChanged(BuildContext context, BaseState state) {}

  /// Handle specific state types - can be overridden by subclasses
  void onLoadingState(BuildContext context, BaseLoadingState state) {}
  void onErrorState(BuildContext context, BaseErrorState state) {}
  void onSuccessState(BuildContext context, BaseSuccessState state) {}

  /// Determines whether the [BlocConsumer] builder should run in response to state changes.
  ///
  /// Subclasses can override this method to implement custom logic for when to rebuild
  /// the widget tree based on the previous and current state.
  /// Defaults to true, causing a rebuild for every state change.
  ///
  /// Example:
  /// ```dart
  /// @override
  /// bool buildWhen(BaseState previous, BaseState current) {
  ///   // Only rebuild if the state type changes or if they are different instances
  ///   return previous.runtimeType != current.runtimeType || previous != current;
  /// }
  /// ```
  bool buildWhen(BaseState previous, BaseState current) {
    return true;
  }

  /// Determines whether the [BlocConsumer] listener should run in response to state changes.
  ///
  /// Subclasses can override this method to implement custom logic for when to invoke
  /// the listener based on the previous and current state.
  /// Defaults to true, causing the listener to be called for every state change.
  ///
  /// Example:
  /// ```dart
  /// @override
  /// bool listenWhen(BaseState previous, BaseState current) {
  ///   // Only listen if a specific condition is met, e.g., an error occurs
  ///   return current is BaseErrorState && previous is! BaseErrorState;
  /// }
  /// ```
  bool listenWhen(BaseState previous, BaseState current) {
    return true;
  }

  /// Builds the body of the view based on the current state.
  /// This is used by the BlocConsumer's builder.
  Widget _buildStateContent(BuildContext context, BaseState state) {
    if (state.isLoading) {
      return buildLoading(context, state as BaseLoadingState);
    } else if (state.isError) {
      return buildError(context, state as BaseErrorState);
    } else if (state is EmptyState) {
      return buildEmpty(context, state);
    } else {
      return buildContent(context, state);
    }
  }

  /// Builds the BlocConsumer widget.
  /// This is shared between BaseCubitView and LoadingBaseCubitView.
  Widget _buildBlocConsumer(BuildContext context) {
    return BlocConsumer<C, BaseState>(
      buildWhen: buildWhen,
      listenWhen: listenWhen,
      listener: (context, state) {
        onStateChanged(context, state);

        if (state.isLoading) {
          onLoadingState(context, state as BaseLoadingState);
        } else if (state.isError) {
          onErrorState(context, state as BaseErrorState);
        } else if (state.isSuccess) {
          onSuccessState(context, state as BaseSuccessState);
        }
      },
      builder: (context, state) {
        final bodyContent = _buildStateContent(context, state);
        return buildScaffold(
          context,
          body: bodyContent,
          appBar: getAppBar(context),
          floatingActionButton: getFloatingActionButton(context),
          bottomNavigationBar: getBottomNavigationBar(context),
          drawer: getDrawer(context),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<C>(
      create: createCubit,
      child: _buildBlocConsumer(context),
    );
  }
}

/// Simplified base cubit view for simple screens
abstract class SimpleBaseCubitView<C extends BaseCubit<BaseState>>
    extends BaseCubitView<C> {
  const SimpleBaseCubitView({super.key});

  @override
  Widget buildScaffold(
    BuildContext context, {
    required Widget body,
    PreferredSizeWidget? appBar,
    Widget? floatingActionButton,
    Widget? bottomNavigationBar,
    Widget? drawer,
  }) {
    // SimpleBaseCubitView typically doesn't want the BaseCubitView's Scaffold.
    // It returns the body directly.
    return body;
  }
}

/// Base cubit view with automatic initial loading
abstract class LoadingBaseCubitView<C extends BaseCubit<BaseState>>
    extends BaseCubitView<C> {
  const LoadingBaseCubitView({super.key});

  /// Method to trigger initial load - subclasses should override
  void triggerInitialLoad(C cubit) {
    // Default implementation tries to call refresh if the cubit is Refreshable
    if (cubit is Refreshable) {
      (cubit as Refreshable).refresh();
    }
    // If the cubit is not Refreshable, do nothing by default.
    // Subclasses can override this method for custom initial load logic.
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<C>(
      create: (context) {
        final cubit = createCubit(context);
        // Trigger initial load
        triggerInitialLoad(cubit);
        return cubit;
      },
      child: _buildBlocConsumer(context), // Use the shared BlocConsumer builder
    );
  }
}
