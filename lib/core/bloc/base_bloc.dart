import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';

import 'base_event.dart';
import 'base_state.dart';
import '../errors/failures.dart';
import '../services/logger/logger.dart';

/// Base abstract class for all blocs in the application
///
/// Provides common functionality like error handling, logging,
/// and standard event handling patterns
abstract class BaseBloc<Event extends BaseEvent, State extends BaseState>
    extends Bloc<Event, State> {
  /// Logger instance for this bloc
  late final Logger _logger;

  /// Constructor that sets up logging and common event handlers
  BaseBloc(super.initialState) {
    _logger = AppLogger.log;
    _setupCommonEventHandlers();
    _logStateChanges();
  }

  /// Setup common event handlers that all blocs should have
  void _setupCommonEventHandlers() {
    // Handle common events if they are part of the Event type
    on<Event>((event, emit) async {
      _logger.d('Processing event: ${event.eventName}');

      try {
        await handleEvent(event, emit);
      } catch (error, stackTrace) {
        _logger.e(
          'Error processing event: ${event.eventName}',
          error: error,
          stackTrace: stackTrace,
        );
        await handleError(error, stackTrace, emit);
      }
    });
  }

  /// Log state changes for debugging
  void _logStateChanges() {
    stream.listen((state) {
      _logger.d('State transition: ${state.stateName}');

      if (state.isError) {
        final errorState = state as BaseErrorState;
        _logger.e('Error state: ${errorState.message}');
      }
    });
  }

  /// Abstract method that subclasses must implement to handle events
  Future<void> handleEvent(Event event, Emitter<State> emit);

  /// Handle errors in a consistent way across all blocs
  Future<void> handleError(
    dynamic error,
    StackTrace stackTrace,
    Emitter<State> emit,
  ) async {
    String errorMessage;
    String? errorCode;

    if (error is Failure) {
      errorMessage = error.message;
      errorCode = error.statusCode?.toString();
    } else if (error is Exception) {
      errorMessage = error.toString();
    } else {
      errorMessage = 'An unexpected error occurred';
    }

    _logger.e(
      'Bloc error: $errorMessage',
      error: error,
      stackTrace: stackTrace,
    );

    // Emit error state if possible
    if (hasErrorState()) {
      emit(
        createErrorState(
              message: errorMessage,
              errorCode: errorCode,
              originalError: error,
              stackTrace: stackTrace,
            )
            as State,
      );
    }
  }

  /// Check if this bloc supports error states
  bool hasErrorState() => true;

  /// Create an error state - subclasses should override this
  BaseErrorState createErrorState({
    required String message,
    String? errorCode,
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    return GenericErrorState(
      message: message,
      errorCode: errorCode,
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  /// Create a loading state - subclasses should override this
  BaseLoadingState createLoadingState({String? message}) {
    return GenericLoadingState(loadingMessage: message);
  }

  /// Create a success state - subclasses should override this
  BaseSuccessState<T> createSuccessState<T>({T? data, String? message}) {
    return GenericSuccessState<T>(data: data, message: message);
  }

  /// Emit loading state safely
  void emitLoading(Emitter<State> emit, {String? message}) {
    if (hasLoadingState()) {
      emit(createLoadingState(message: message) as State);
    }
  }

  /// Emit success state safely
  void emitSuccess<T>(Emitter<State> emit, {T? data, String? message}) {
    if (hasSuccessState()) {
      emit(createSuccessState<T>(data: data, message: message) as State);
    }
  }

  /// Emit error state safely
  void emitError(
    Emitter<State> emit, {
    required String message,
    String? errorCode,
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    if (hasErrorState()) {
      emit(
        createErrorState(
              message: message,
              errorCode: errorCode,
              originalError: originalError,
              stackTrace: stackTrace,
            )
            as State,
      );
    }
  }

  /// Check if this bloc supports loading states
  bool hasLoadingState() => true;

  /// Check if this bloc supports success states
  bool hasSuccessState() => true;

  /// Utility method to execute async operations with error handling
  Future<T?> executeWithErrorHandling<T>(
    Future<T> Function() operation,
    Emitter<State> emit, {
    String? loadingMessage,
    String? successMessage,
  }) async {
    try {
      emitLoading(emit, message: loadingMessage);
      final result = await operation();

      if (successMessage != null) {
        emitSuccess(emit, data: result, message: successMessage);
      }

      return result;
    } catch (error, stackTrace) {
      await handleError(error, stackTrace, emit);
      return null;
    }
  }

  @override
  void onTransition(Transition<Event, State> transition) {
    super.onTransition(transition);
    _logger.d(
      'Transition: ${transition.currentState.stateName} -> ${transition.nextState.stateName}',
    );
  }

  @override
  void onError(Object error, StackTrace stackTrace) {
    super.onError(error, stackTrace);
    _logger.e(
      'Bloc error in $runtimeType',
      error: error,
      stackTrace: stackTrace,
    );
  }

  @override
  Future<void> close() {
    _logger.d('Closing bloc: ${runtimeType.toString()}');
    return super.close();
  }
}
