# Base BLoC & Cubit Architecture

This directory contains the base classes for implementing both BLoC and Cubit patterns consistently across the application.

## Overview

The base architecture provides:
- **<PERSON><PERSON><PERSON><PERSON> Pattern**: Event-driven state management for complex scenarios
- **Cubit Pattern**: Simple state management for basic use cases
- **Consistent error handling** across all state management
- **Automatic logging** for debugging and monitoring
- **Standard state patterns** (loading, success, error, empty)
- **Type safety** with generic implementations
- **Reusable UI components** for common states

## Files Structure

```
lib/core/bloc/
├── base_event.dart          # Base event classes
├── base_state.dart          # Base state classes
├── base_bloc.dart           # Base bloc implementation
├── base_view.dart           # Base view widgets for BLoC
├── base_cubit.dart          # Base cubit implementation
├── base_cubit_view.dart     # Base view widgets for Cubit
├── cubit_patterns.dart      # Common cubit patterns
├── bloc_exports.dart        # Single import point for all
├── cubit_example.dart       # Cubit usage examples
├── CUBIT_GUIDE.md          # Cubit documentation
└── README.md               # This documentation
```

## Quick Start

### 1. Import the base classes

```dart
import 'package:banachef/core/bloc/bloc_exports.dart';
```

## 🎯 BLoC vs Cubit - When to Use

### ✅ Use BLoC When:
- **Complex business logic** with multiple events
- **Event-driven architecture** is needed
- **Event replay/debugging** is important
- **Global state** shared across many screens

### ✅ Use Cubit When:
- **Simple state management** (toggle, counter, form)
- **Direct method calls** are sufficient
- **Quick prototyping** is needed
- **Local state** for 1-2 screens

> 📖 **Detailed Cubit Guide**: See `CUBIT_GUIDE.md` for complete Cubit documentation

### 2. Define your events

```dart
abstract class MyFeatureEvent extends BaseEvent {
  const MyFeatureEvent();
}

class LoadDataEvent extends MyFeatureEvent {
  const LoadDataEvent();
}

class UpdateDataEvent extends MyFeatureEvent {
  final String data;
  
  const UpdateDataEvent(this.data);
  
  @override
  List<Object?> get props => [data];
}
```

### 3. Define your states

```dart
abstract class MyFeatureState extends BaseState {
  const MyFeatureState();
}

class MyFeatureInitialState extends BaseInitialState {
  const MyFeatureInitialState();
}

class MyFeatureLoadingState extends BaseLoadingState {
  const MyFeatureLoadingState();
}

class MyFeatureSuccessState extends BaseSuccessState<MyData> {
  const MyFeatureSuccessState({super.data, super.message});
}

class MyFeatureErrorState extends BaseErrorState {
  const MyFeatureErrorState({
    required super.message,
    super.errorCode,
    super.originalError,
    super.stackTrace,
  });
}
```

### 4. Implement your bloc

```dart
@injectable
class MyFeatureBloc extends BaseBloc<MyFeatureEvent, MyFeatureState> {
  final MyRepository _repository;

  MyFeatureBloc(this._repository) : super(const MyFeatureInitialState());

  @override
  Future<void> handleEvent(MyFeatureEvent event, Emitter<MyFeatureState> emit) async {
    if (event is LoadDataEvent) {
      await _handleLoadData(event, emit);
    } else if (event is UpdateDataEvent) {
      await _handleUpdateData(event, emit);
    }
  }

  Future<void> _handleLoadData(LoadDataEvent event, Emitter<MyFeatureState> emit) async {
    await executeWithErrorHandling(
      () => _repository.loadData(),
      emit,
      loadingMessage: 'Loading data...',
      successMessage: 'Data loaded successfully',
    );
  }

  // Override to create custom states
  @override
  BaseErrorState createErrorState({
    required String message,
    String? errorCode,
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    return MyFeatureErrorState(
      message: message,
      errorCode: errorCode,
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }
}
```

### 5. Create your view

```dart
class MyFeatureView extends BaseView<MyFeatureBloc> {
  const MyFeatureView({super.key});

  @override
  MyFeatureBloc createBloc(BuildContext context) {
    return getIt<MyFeatureBloc>();
  }

  @override
  Widget buildContent(BuildContext context, BaseState state) {
    if (state is MyFeatureSuccessState) {
      return MySuccessWidget(data: state.data);
    }
    
    return const MyInitialWidget();
  }

  @override
  PreferredSizeWidget? getAppBar(BuildContext context) {
    return AppBar(title: const Text('My Feature'));
  }

  @override
  void onErrorState(BuildContext context, BaseErrorState state) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(state.message)),
    );
  }
}
```

## Features

### Automatic Error Handling

The base bloc automatically catches and handles errors:

```dart
// This will automatically catch errors and emit error state
await executeWithErrorHandling(
  () => riskyOperation(),
  emit,
  loadingMessage: 'Processing...',
  successMessage: 'Success!',
);
```

### Built-in Logging

All blocs automatically log:
- Event processing
- State transitions
- Errors with stack traces
- Bloc lifecycle events

### Standard State Patterns

Common states are provided out of the box:
- `BaseInitialState` - Initial state
- `BaseLoadingState` - Loading operations
- `BaseSuccessState<T>` - Successful operations with data
- `BaseErrorState` - Error states with details
- `EmptyState` - No data available

### Common Events

Standard events for common operations:
- `InitialLoadEvent` - Trigger initial data loading
- `RefreshEvent` - Refresh current data
- `RetryEvent` - Retry failed operations
- `ResetEvent` - Reset to initial state

### View Helpers

Base view classes provide:
- Automatic state handling
- Loading, error, and empty state widgets
- Consistent UI patterns
- Event handling helpers

## Best Practices

### 1. Use executeWithErrorHandling for async operations

```dart
// Good
await executeWithErrorHandling(
  () => _repository.getData(),
  emit,
  loadingMessage: 'Loading...',
);

// Avoid manual error handling
try {
  emit(LoadingState());
  final data = await _repository.getData();
  emit(SuccessState(data));
} catch (e) {
  emit(ErrorState(e.toString()));
}
```

### 2. Override state creation methods for custom states

```dart
@override
BaseErrorState createErrorState({required String message, ...}) {
  return MyCustomErrorState(message: message, ...);
}
```

### 3. Use type-safe state checking

```dart
// Good
if (state.isLoading) { ... }
if (state.isError) { ... }

// Instead of
if (state is LoadingState) { ... }
```

### 4. Implement proper props in events and states

```dart
class MyEvent extends BaseEvent {
  final String data;
  
  const MyEvent(this.data);
  
  @override
  List<Object?> get props => [data]; // Important for equality
}
```

## Testing

The base classes are designed to be easily testable:

```dart
void main() {
  group('MyFeatureBloc', () {
    late MyFeatureBloc bloc;
    
    setUp(() {
      bloc = MyFeatureBloc(mockRepository);
    });
    
    blocTest<MyFeatureBloc, MyFeatureState>(
      'emits success state when data loads successfully',
      build: () => bloc,
      act: (bloc) => bloc.add(const LoadDataEvent()),
      expect: () => [
        isA<MyFeatureLoadingState>(),
        isA<MyFeatureSuccessState>(),
      ],
    );
  });
}
```

## Migration Guide

To migrate existing blocs to use the base classes:

1. Change your bloc to extend `BaseBloc`
2. Move event handling logic to `handleEvent` method
3. Use `executeWithErrorHandling` for async operations
4. Update your events to extend `BaseEvent`
5. Update your states to extend `BaseState`
6. Update your views to extend `BaseView`

See `example_usage.dart` for a complete working example.
