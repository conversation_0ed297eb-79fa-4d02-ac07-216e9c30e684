# 🎯 BanaChef Cubit Architecture Guide

Hệ thống Cubit cho BanaChef App - một giải pháp state management đơn giản hơn BLoC cho các use cases không cần event handling phức tạp.

## 📋 Tổng Quan

Cubit architecture cung cấp:
- **State management đơn giản** hơn BLoC (không cần events)
- **Automatic error handling** và logging
- **Consistent patterns** cho các use cases phổ biến
- **Type safety** với generic implementations
- **Reusable UI components** cho common states
- **Tương thích hoàn toàn** với BLoC architecture hiện tại

## 🏗️ Cấu Trúc Files

```
lib/core/bloc/
├── base_cubit.dart          # Base cubit implementation
├── base_cubit_view.dart     # Base view widgets cho cubit
├── cubit_patterns.dart      # Common cubit patterns
├── CUBIT_GUIDE.md          # Documentation này
└── bloc_exports.dart       # Updated với cubit exports
```

## 🎯 Khi Nào Sử Dụng Cubit vs BLoC

### ✅ Sử Dụng Cubit Khi:
- **Simple state management**: Toggle, form validation, basic CRUD
- **Direct method calls**: Không cần event-driven architecture
- **Quick prototyping**: Cần implement nhanh
- **Local state**: State chỉ dùng trong 1-2 screens

### ✅ Sử Dụng BLoC Khi:
- **Complex business logic**: Cần event handling phức tạp
- **Event-driven**: Cần track và replay events
- **Global state**: State được share across nhiều screens
- **Advanced patterns**: Cần middleware, event transformation

## 🚀 Quick Start

### 1. Import Cubit Classes

```dart
import 'package:banachef/core/bloc/bloc_exports.dart';
```

### 2. Tạo Simple Cubit

```dart
class CounterCubit extends BaseCubit<BaseState> {
  CounterCubit() : super(const GenericSuccessState<int>(data: 0));

  int get count {
    final currentState = state;
    if (currentState is BaseSuccessState<int>) {
      return currentState.data ?? 0;
    }
    return 0;
  }

  void increment() {
    emitSuccess(data: count + 1);
  }

  void decrement() {
    emitSuccess(data: count - 1);
  }

  @override
  void reset() {
    emitSuccess(data: 0);
  }
}
```

### 3. Tạo View

```dart
class CounterView extends BaseCubitView<CounterCubit> {
  const CounterView({super.key});

  @override
  CounterCubit createCubit(BuildContext context) {
    return CounterCubit();
  }

  @override
  Widget buildContent(BuildContext context, BaseState state) {
    final cubit = BlocProvider.of<CounterCubit>(context);
    
    return Column(
      children: [
        Text('Count: ${cubit.count}'),
        Row(
          children: [
            ElevatedButton(
              onPressed: cubit.increment,
              child: Text('+'),
            ),
            ElevatedButton(
              onPressed: cubit.decrement,
              child: Text('-'),
            ),
          ],
        ),
      ],
    );
  }

  @override
  PreferredSizeWidget? getAppBar(BuildContext context) {
    return AppBar(title: Text('Counter'));
  }
}
```

## 🎨 Cubit Patterns

### 1. DataCubit - Basic CRUD Operations

```dart
class RecipeCubit extends DataCubit<Recipe> {
  final RecipeRepository _repository;

  RecipeCubit(this._repository);

  @override
  Future<Recipe> loadData() async {
    return await _repository.getRecipe(recipeId);
  }

  @override
  Future<Recipe> updateData(Recipe recipe) async {
    return await _repository.updateRecipe(recipe);
  }
}

// Usage
class RecipeView extends LoadingBaseCubitView<RecipeCubit> {
  @override
  RecipeCubit createCubit(BuildContext context) {
    return getIt<RecipeCubit>();
  }

  @override
  void triggerInitialLoad(RecipeCubit cubit) {
    cubit.load(); // Automatically loads data
  }
}
```

### 2. ListCubit - Managing Lists

```dart
class RecipeListCubit extends ListCubit<Recipe> {
  final RecipeRepository _repository;

  RecipeListCubit(this._repository);

  @override
  Future<List<Recipe>> loadList() async {
    return await _repository.getAllRecipes();
  }

  @override
  Future<Recipe> addItem(Recipe recipe) async {
    return await _repository.createRecipe(recipe);
  }

  @override
  Future<void> removeItem(String id) async {
    await _repository.deleteRecipe(id);
  }
}
```

### 3. PaginatedListCubit - Paginated Data

```dart
class RecipeSearchCubit extends PaginatedListCubit<Recipe> {
  final RecipeRepository _repository;
  String _searchQuery = '';

  RecipeSearchCubit(this._repository);

  @override
  Future<PaginatedResult<Recipe>> loadPage(int page) async {
    final result = await _repository.searchRecipes(
      query: _searchQuery,
      page: page,
      limit: 20,
    );

    return PaginatedResult(
      items: result.recipes,
      hasMore: result.hasNextPage,
      totalCount: result.totalCount,
    );
  }

  void search(String query) {
    _searchQuery = query;
    loadFirstPage();
  }
}
```

### 4. FormCubit - Form Management

```dart
class RecipeFormCubit extends FormCubit<Recipe> {
  final RecipeRepository _repository;

  RecipeFormCubit(this._repository);

  @override
  Map<String, String> validateForm(Map<String, dynamic> formData) {
    final errors = <String, String>{};

    if (formData['name']?.isEmpty ?? true) {
      errors['name'] = 'Recipe name is required';
    }

    if (formData['ingredients']?.isEmpty ?? true) {
      errors['ingredients'] = 'At least one ingredient is required';
    }

    return errors;
  }

  @override
  Future<Recipe> submitForm(Map<String, dynamic> formData) async {
    final recipe = Recipe.fromMap(formData);
    return await _repository.createRecipe(recipe);
  }
}
```

### 5. ToggleCubit - Simple Boolean State

```dart
// Built-in pattern, ready to use
class SettingsView extends BaseCubitView<ToggleCubit> {
  @override
  ToggleCubit createCubit(BuildContext context) {
    return ToggleCubit(initialValue: false);
  }

  @override
  Widget buildContent(BuildContext context, BaseState state) {
    final cubit = BlocProvider.of<ToggleCubit>(context);
    
    return Switch(
      value: cubit.value,
      onChanged: (value) => cubit.setValue(value),
    );
  }
}
```

## 🎯 Base Cubit Features

### Automatic Error Handling

```dart
class MyCubit extends BaseCubit<BaseState> {
  Future<void> loadData() async {
    // Automatic loading state, error handling, and logging
    await executeWithErrorHandling(
      () => repository.getData(),
      loadingMessage: 'Loading data...',
      successMessage: 'Data loaded successfully',
    );
  }
}
```

### Built-in Methods

```dart
// All cubits inherit these methods:
cubit.reset();        // Reset to initial state
cubit.refresh();      // Refresh data
cubit.retry();        // Retry last operation
cubit.emitLoading();  // Emit loading state
cubit.emitSuccess();  // Emit success state
cubit.emitError();    // Emit error state
```

## 🎨 View Types

### 1. BaseCubitView - Standard View

```dart
class MyView extends BaseCubitView<MyCubit> {
  // Standard view with scaffold
}
```

### 2. SimpleBaseCubitView - No Scaffold

```dart
class MySimpleView extends SimpleBaseCubitView<MyCubit> {
  // Returns body directly, no scaffold
}
```

### 3. LoadingBaseCubitView - Auto Loading

```dart
class MyLoadingView extends LoadingBaseCubitView<MyCubit> {
  @override
  void triggerInitialLoad(MyCubit cubit) {
    cubit.loadData(); // Called automatically
  }
}
```

## ✅ Best Practices

### 1. **Sử dụng patterns có sẵn**
```dart
// ✅ Good - Sử dụng DataCubit cho CRUD
class RecipeCubit extends DataCubit<Recipe> { ... }

// ❌ Avoid - Tự implement từ đầu
class RecipeCubit extends BaseCubit<BaseState> { ... }
```

### 2. **Dependency injection**
```dart
// ✅ Good - Inject dependencies
class RecipeCubit extends DataCubit<Recipe> {
  final RecipeRepository _repository;
  RecipeCubit(this._repository);
}

// Register in DI
@injectable
class RecipeCubit extends DataCubit<Recipe> { ... }
```

### 3. **Error handling**
```dart
// ✅ Good - Sử dụng executeWithErrorHandling
await executeWithErrorHandling(
  () => repository.getData(),
  loadingMessage: 'Loading...',
);

// ❌ Avoid - Manual error handling
try {
  emitLoading();
  final data = await repository.getData();
  emitSuccess(data: data);
} catch (e) {
  emitError(message: e.toString());
}
```

### 4. **State checking**
```dart
// ✅ Good - Sử dụng built-in checks
if (state.isLoading) { ... }
if (state.isError) { ... }

// ❌ Avoid - Manual type checking
if (state is BaseLoadingState) { ... }
```

## 🔄 Migration từ BLoC sang Cubit

### 1. Thay đổi base class
```dart
// Before
class MyBloc extends BaseBloc<MyEvent, MyState> { ... }

// After
class MyCubit extends BaseCubit<BaseState> { ... }
```

### 2. Thay đổi methods
```dart
// Before
void add(MyEvent event) { ... }

// After
void myMethod() { ... }
```

### 3. Thay đổi view
```dart
// Before
class MyView extends BaseView<MyBloc> { ... }

// After
class MyView extends BaseCubitView<MyCubit> { ... }
```

## 🧪 Testing

```dart
void main() {
  group('MyCubit', () {
    late MyCubit cubit;
    
    setUp(() {
      cubit = MyCubit(mockRepository);
    });
    
    blocTest<MyCubit, BaseState>(
      'emits success state when data loads',
      build: () => cubit,
      act: (cubit) => cubit.loadData(),
      expect: () => [
        isA<BaseLoadingState>(),
        isA<BaseSuccessState>(),
      ],
    );
  });
}
```

---

> 📖 **Tóm tắt**: Cubit cung cấp state management đơn giản hơn BLoC cho các use cases cơ bản, với patterns có sẵn và error handling tự động.
