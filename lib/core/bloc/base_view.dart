import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'base_bloc.dart';
import 'base_event.dart';
import 'base_state.dart';
import '../../shared/widgets/loading_widget.dart';
import '../../shared/widgets/error_widget.dart' as custom_widgets;
import '../../shared/widgets/empty_widget.dart';

/// Base widget for views that use bloc pattern
///
/// Provides common UI patterns like loading, error, and empty states
/// with consistent styling and behavior across the application
abstract class BaseView<B extends BaseBloc<BaseEvent, BaseState>>
    extends StatelessWidget {
  const BaseView({super.key});

  /// Create the bloc instance - must be implemented by subclasses
  B createBloc(BuildContext context);

  /// Build the main content when state is successful
  Widget buildContent(BuildContext context, BaseState state);

  /// Build loading widget - can be overridden by subclasses
  Widget buildLoading(BuildContext context, BaseLoadingState state) {
    return LoadingWidget(
      message: state is GenericLoadingState ? state.loadingMessage : null,
    );
  }

  /// Build error widget - can be overridden by subclasses
  Widget buildError(BuildContext context, BaseErrorState state) {
    return custom_widgets.ErrorWidget(
      message: state.message,
      onRetry: () => _handleRetry(context),
    );
  }

  /// Build empty widget - can be overridden by subclasses
  Widget buildEmpty(BuildContext context, EmptyState state) {
    return EmptyWidget(message: state.emptyMessage);
  }

  /// Handle retry action
  void _handleRetry(BuildContext context) {
    final bloc = BlocProvider.of<B>(context);
    // Try to add retry event if supported
    try {
      bloc.add(const RetryEvent());
    } catch (e) {
      // If RetryEvent is not supported, try refresh
      try {
        bloc.add(const RefreshEvent());
      } catch (e) {
        // If neither is supported, do nothing
      }
    }
  }

  /// Build the scaffold structure - can be overridden by subclasses
  Widget buildScaffold(
    BuildContext context, {
    required Widget body,
    PreferredSizeWidget? appBar,
    Widget? floatingActionButton,
    Widget? bottomNavigationBar,
    Widget? drawer,
  }) {
    return Scaffold(
      appBar: appBar,
      body: body,
      floatingActionButton: floatingActionButton,
      bottomNavigationBar: bottomNavigationBar,
      drawer: drawer,
    );
  }

  /// Get app bar - can be overridden by subclasses
  PreferredSizeWidget? getAppBar(BuildContext context) => null;

  /// Get floating action button - can be overridden by subclasses
  Widget? getFloatingActionButton(BuildContext context) => null;

  /// Get bottom navigation bar - can be overridden by subclasses
  Widget? getBottomNavigationBar(BuildContext context) => null;

  /// Get drawer - can be overridden by subclasses
  Widget? getDrawer(BuildContext context) => null;

  /// Handle state changes - can be overridden by subclasses
  void onStateChanged(BuildContext context, BaseState state) {}

  /// Handle specific state types - can be overridden by subclasses
  void onLoadingState(BuildContext context, BaseLoadingState state) {}
  void onErrorState(BuildContext context, BaseErrorState state) {}
  void onSuccessState(BuildContext context, BaseSuccessState state) {}

  /// Builds the body of the view based on the current state.
  /// This is used by the BlocConsumer's builder.
  Widget _buildStateContent(BuildContext context, BaseState state) {
    if (state.isLoading) {
      return buildLoading(context, state as BaseLoadingState);
    } else if (state.isError) {
      return buildError(context, state as BaseErrorState);
    } else if (state is EmptyState) {
      return buildEmpty(context, state);
    } else {
      return buildContent(context, state);
    }
  }

  /// Builds the BlocConsumer widget.
  /// This is shared between BaseView and LoadingBaseView.
  Widget _buildBlocConsumer(BuildContext context) {
    return BlocConsumer<B, BaseState>(
      listener: (context, state) {
        onStateChanged(context, state);

        if (state.isLoading) {
          onLoadingState(context, state as BaseLoadingState);
        } else if (state.isError) {
          onErrorState(context, state as BaseErrorState);
        } else if (state.isSuccess) {
          onSuccessState(context, state as BaseSuccessState);
        }
      },
      builder: (context, state) {
        final bodyContent = _buildStateContent(context, state);
        return buildScaffold(
          context,
          body: bodyContent,
          appBar: getAppBar(context),
          floatingActionButton: getFloatingActionButton(context),
          bottomNavigationBar: getBottomNavigationBar(context),
          drawer: getDrawer(context),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<B>(
      create: createBloc,
      child: _buildBlocConsumer(context),
    );
  }
}

/// Simplified base view for simple screens
abstract class SimpleBaseView<B extends BaseBloc<BaseEvent, BaseState>>
    extends BaseView<B> {
  const SimpleBaseView({super.key});

  @override
  Widget buildScaffold(
    BuildContext context, {
    required Widget body,
    PreferredSizeWidget? appBar,
    Widget? floatingActionButton,
    Widget? bottomNavigationBar,
    Widget? drawer,
  }) {
    // SimpleBaseView typically doesn't want the BaseView's Scaffold.
    // It returns the body directly.
    return body;
  }
}

/// Base view with automatic initial loading
abstract class LoadingBaseView<B extends BaseBloc<BaseEvent, BaseState>>
    extends BaseView<B> {
  const LoadingBaseView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<B>(
      create: (context) {
        final bloc = createBloc(context);
        // Trigger initial load if supported
        try {
          bloc.add(const InitialLoadEvent());
        } catch (e) {
          // If InitialLoadEvent is not supported, do nothing
        }
        return bloc;
      },
      child: _buildBlocConsumer(context), // Use the shared BlocConsumer builder
    );
  }
}
