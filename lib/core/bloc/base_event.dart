import 'package:equatable/equatable.dart';

/// Base abstract class for all events in the application
/// 
/// All events should extend this class to ensure consistency
/// and provide common functionality across the application
abstract class BaseEvent extends Equatable {
  const BaseEvent();

  @override
  List<Object?> get props => [];

  /// Optional timestamp for when the event was created
  DateTime get timestamp => DateTime.now();

  /// Optional event identifier for debugging purposes
  String get eventName => runtimeType.toString();

  @override
  String toString() => '$eventName { timestamp: $timestamp }';
}

/// Common events that can be used across different blocs
abstract class CommonEvent extends BaseEvent {
  const CommonEvent();
}

/// Event to trigger initial data loading
class InitialLoadEvent extends CommonEvent {
  const InitialLoadEvent();

  @override
  String toString() => 'InitialLoadEvent';
}

/// Event to trigger data refresh
class RefreshEvent extends CommonEvent {
  const RefreshEvent();

  @override
  String toString() => 'RefreshEvent';
}

/// Event to retry failed operations
class RetryEvent extends CommonEvent {
  const RetryEvent();

  @override
  String toString() => 'RetryEvent';
}

/// Event to reset the bloc to initial state
class ResetEvent extends CommonEvent {
  const ResetEvent();

  @override
  String toString() => 'ResetEvent';
}
