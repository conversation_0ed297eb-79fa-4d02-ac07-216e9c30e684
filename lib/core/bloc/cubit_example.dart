/// Example implementations showing how to use the Cubit architecture
///
/// This file demonstrates various cubit patterns and their usage
/// in real-world scenarios for the BanaChef app
library;

import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';

import 'bloc_exports.dart';

// ============================================================================
// Example 1: Simple Counter Cubit
// ============================================================================

class CounterCubit extends BaseCubit<BaseState> {
  CounterCubit() : super(const GenericSuccessState<int>(data: 0));

  int get count {
    final currentState = state;
    if (currentState is BaseSuccessState<int>) {
      return currentState.data ?? 0;
    }
    return 0;
  }

  void increment() {
    emitSuccess(data: count + 1);
  }

  void decrement() {
    emitSuccess(data: count - 1);
  }

  @override
  void reset() {
    emitSuccess(data: 0);
  }
}

class CounterView extends BaseCubitView<CounterCubit> {
  const CounterView({super.key});

  @override
  CounterCubit createCubit(BuildContext context) {
    return CounterCubit();
  }

  @override
  Widget buildContent(BuildContext context, BaseState state) {
    final cubit = BlocProvider.of<CounterCubit>(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Count: ${cubit.count}',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                onPressed: cubit.decrement,
                child: const Icon(Icons.remove),
              ),
              const SizedBox(width: 20),
              ElevatedButton(
                onPressed: cubit.increment,
                child: const Icon(Icons.add),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            onPressed: cubit.reset,
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  @override
  PreferredSizeWidget? getAppBar(BuildContext context) {
    return AppBar(title: const Text('Counter Example'));
  }
}

// ============================================================================
// Example 2: Recipe Data Cubit (CRUD Operations)
// ============================================================================

// Mock Recipe model
class Recipe {
  final String id;
  final String name;
  final String description;
  final List<String> ingredients;

  const Recipe({
    required this.id,
    required this.name,
    required this.description,
    required this.ingredients,
  });

  Recipe copyWith({
    String? id,
    String? name,
    String? description,
    List<String>? ingredients,
  }) {
    return Recipe(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      ingredients: ingredients ?? this.ingredients,
    );
  }
}

// Mock Repository
abstract class RecipeRepository {
  Future<Recipe> getRecipe(String id);
  Future<Recipe> updateRecipe(Recipe recipe);
  Future<void> deleteRecipe(String id);
}

@injectable
class RecipeCubit extends DataCubit<Recipe> {
  final RecipeRepository _repository;
  final String recipeId;

  RecipeCubit(this._repository, @factoryParam this.recipeId);

  @override
  Future<Recipe> loadData() async {
    return await _repository.getRecipe(recipeId);
  }

  @override
  Future<Recipe> updateData(Recipe data) async {
    return await _repository.updateRecipe(data);
  }

  @override
  Future<void> deleteData(String id) async {
    await _repository.deleteRecipe(id);
  }

  // Custom method specific to recipe
  Future<void> updateRecipeName(String newName) async {
    final currentState = state;
    if (currentState is BaseSuccessState<Recipe>) {
      final currentRecipe = currentState.data;
      if (currentRecipe != null) {
        final updatedRecipe = currentRecipe.copyWith(name: newName);
        await update(updatedRecipe);
      }
    }
  }
}

class RecipeDetailView extends LoadingBaseCubitView<RecipeCubit> {
  final String recipeId;

  const RecipeDetailView({super.key, required this.recipeId});

  @override
  RecipeCubit createCubit(BuildContext context) {
    // In real app, use getIt<RecipeCubit>(param1: recipeId)
    throw UnimplementedError('Use dependency injection');
  }

  @override
  void triggerInitialLoad(RecipeCubit cubit) {
    cubit.load();
  }

  @override
  Widget buildContent(BuildContext context, BaseState state) {
    if (state is BaseSuccessState<Recipe>) {
      final recipe = state.data!;
      final cubit = BlocProvider.of<RecipeCubit>(context);

      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              recipe.name,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 16),
            Text(
              recipe.description,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 16),
            Text(
              'Ingredients:',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            ...recipe.ingredients.map(
              (ingredient) => Padding(
                padding: const EdgeInsets.only(left: 16, top: 4),
                child: Text('• $ingredient'),
              ),
            ),
            const SizedBox(height: 32),
            Row(
              children: [
                ElevatedButton(
                  onPressed: () => _showEditDialog(context, cubit, recipe),
                  child: const Text('Edit'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () => cubit.delete(recipe.id),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                  ),
                  child: const Text('Delete'),
                ),
              ],
            ),
          ],
        ),
      );
    }

    return const SizedBox.shrink();
  }

  void _showEditDialog(BuildContext context, RecipeCubit cubit, Recipe recipe) {
    final nameController = TextEditingController(text: recipe.name);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Recipe'),
        content: TextField(
          controller: nameController,
          decoration: const InputDecoration(labelText: 'Recipe Name'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              cubit.updateRecipeName(nameController.text);
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  @override
  PreferredSizeWidget? getAppBar(BuildContext context) {
    return AppBar(title: const Text('Recipe Details'));
  }

  @override
  void onErrorState(BuildContext context, BaseErrorState state) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(state.message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  void onSuccessState(BuildContext context, BaseSuccessState state) {
    if (state.message != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message!),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}

// ============================================================================
// Example 3: Settings Toggle Cubit
// ============================================================================

class SettingsView extends BaseCubitView<ToggleCubit> {
  const SettingsView({super.key});

  @override
  ToggleCubit createCubit(BuildContext context) {
    return ToggleCubit(initialValue: false);
  }

  @override
  Widget buildContent(BuildContext context, BaseState state) {
    final cubit = BlocProvider.of<ToggleCubit>(context);
    
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          SwitchListTile(
            title: const Text('Dark Mode'),
            subtitle: const Text('Enable dark theme'),
            value: cubit.value,
            onChanged: (value) => cubit.setValue(value),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: cubit.toggle,
            child: const Text('Toggle'),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: cubit.reset,
            child: const Text('Reset to Default'),
          ),
        ],
      ),
    );
  }

  @override
  PreferredSizeWidget? getAppBar(BuildContext context) {
    return AppBar(title: const Text('Settings'));
  }
}

// ============================================================================
// Example 4: Form Cubit
// ============================================================================

class ContactFormCubit extends FormCubit<Map<String, dynamic>> {
  ContactFormCubit();

  @override
  Map<String, String> validateForm(Map<String, dynamic> formData) {
    final errors = <String, String>{};

    final name = formData['name'] as String?;
    if (name?.isEmpty ?? true) {
      errors['name'] = 'Name is required';
    }

    final email = formData['email'] as String?;
    if (email?.isEmpty ?? true) {
      errors['email'] = 'Email is required';
    } else if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email!)) {
      errors['email'] = 'Invalid email format';
    }

    return errors;
  }

  @override
  Future<Map<String, dynamic>> submitForm(Map<String, dynamic> formData) async {
    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));
    
    // Simulate success response
    return {
      'success': true,
      'message': 'Contact form submitted successfully',
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
    };
  }
}

class ContactFormView extends BaseCubitView<ContactFormCubit> {
  const ContactFormView({super.key});

  @override
  ContactFormCubit createCubit(BuildContext context) {
    return ContactFormCubit();
  }

  @override
  Widget buildContent(BuildContext context, BaseState state) {
    final cubit = BlocProvider.of<ContactFormCubit>(context);
    final nameController = TextEditingController();
    final emailController = TextEditingController();
    final messageController = TextEditingController();

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          TextField(
            controller: nameController,
            decoration: const InputDecoration(
              labelText: 'Name',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: emailController,
            decoration: const InputDecoration(
              labelText: 'Email',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: messageController,
            maxLines: 4,
            decoration: const InputDecoration(
              labelText: 'Message',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 24),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: state.isLoading ? null : () {
                cubit.submit({
                  'name': nameController.text,
                  'email': emailController.text,
                  'message': messageController.text,
                });
              },
              child: state.isLoading 
                ? const CircularProgressIndicator()
                : const Text('Submit'),
            ),
          ),
        ],
      ),
    );
  }

  @override
  PreferredSizeWidget? getAppBar(BuildContext context) {
    return AppBar(title: const Text('Contact Form'));
  }

  @override
  void onErrorState(BuildContext context, BaseErrorState state) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(state.message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  void onSuccessState(BuildContext context, BaseSuccessState state) {
    if (state.message != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.message!),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}
