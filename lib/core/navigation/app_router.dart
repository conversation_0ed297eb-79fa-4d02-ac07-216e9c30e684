import 'package:auto_route/auto_route.dart';
import 'package:banachef/features/auth/presentation/pages/login_page.dart';
import 'package:banachef/features/auth/presentation/pages/register_page.dart';
import 'package:banachef/features/dashboard/presentation/pages/dashboard_page.dart';
import 'package:banachef/features/discovery/presentation/pages/discovery_page.dart';
import 'package:banachef/features/image_scan/presentation/pages/image_scan_page.dart';
import 'package:banachef/features/meal_plan/presentation/pages/meal_plan_page.dart';
import 'package:banachef/features/onboarding/view/onboarding_view.dart';
import 'package:banachef/features/profile/presentation/pages/profile_page.dart';
import 'package:banachef/features/referral/view/referral_code_screen.dart';

import '../../features/splash/presentation/pages/splash_page.dart';

part 'app_router.gr.dart';

@AutoRouterConfig()
class AppRouter extends RootStackRouter {
  @override
  List<AutoRoute> get routes => [
    // Splash
    AutoRoute(page: SplashRoute.page, path: '/splash', initial: true),

    // Onboarding
    AutoRoute(page: OnboardingViewRoute.page, path: '/onboarding'),

    // Referral
    AutoRoute(page: ReferralCodeRoute.page, path: '/referral'),

    // Auth routes
    AutoRoute(page: LoginRoute.page, path: '/login'),
    AutoRoute(page: RegisterRoute.page, path: '/register'),

    // Main app routes
    AutoRoute(page: DashboardRoute.page, path: '/dashboard'),
    AutoRoute(page: DiscoveryRoute.page, path: '/discovery'),
    AutoRoute(page: ImageScanRoute.page, path: '/image-scan'),
    AutoRoute(page: MealPlanRoute.page, path: '/meal-plan'),
    AutoRoute(page: ProfileRoute.page, path: '/profile'),
  ];
}
