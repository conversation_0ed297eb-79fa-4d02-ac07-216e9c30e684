import 'package:dio/dio.dart';
import '../../config/app_config.dart';
import '../../di/injection_container.dart';
import '../../services/secure_storage_service.dart';

class AuthInterceptor extends Interceptor {
  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    final secureStorage = getIt<SecureStorageService>();
    final token = await secureStorage.getAccessToken();

    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }

    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      // Token expired, try to refresh
      final refreshed = await _refreshToken();
      if (refreshed) {
        // Retry the original request
        final response = await _retry(err.requestOptions);
        handler.resolve(response);
        return;
      } else {
        // Refresh failed, redirect to login
        await _clearTokens();
        // TODO: Navigate to login screen
      }
    }

    handler.next(err);
  }

  Future<bool> _refreshToken() async {
    try {
      final secureStorage = getIt<SecureStorageService>();
      final refreshToken = await secureStorage.getRefreshToken();

      if (refreshToken == null) return false;

      final dio = Dio();
      final response = await dio.post(
        '${AppConfig.baseUrl}/auth/refresh',
        data: {'refresh_token': refreshToken},
      );

      if (response.statusCode == 200) {
        final newAccessToken = response.data['access_token'];
        final newRefreshToken = response.data['refresh_token'];

        await secureStorage.saveAccessToken(newAccessToken);
        await secureStorage.saveRefreshToken(newRefreshToken);

        return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  Future<Response> _retry(RequestOptions requestOptions) async {
    final secureStorage = getIt<SecureStorageService>();
    final token = await secureStorage.getAccessToken();

    if (token != null) {
      requestOptions.headers['Authorization'] = 'Bearer $token';
    }

    final dio = Dio();
    return dio.fetch(requestOptions);
  }

  Future<void> _clearTokens() async {
    final secureStorage = getIt<SecureStorageService>();
    await secureStorage.clearAuthTokens();
    await secureStorage.clearUserData();
  }
}
