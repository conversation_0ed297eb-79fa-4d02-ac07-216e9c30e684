import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

class LoggingInterceptor extends Interceptor {
  final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 5,
      lineLength: 50,
      colors: true,
      printEmojis: true,
      printTime: true,
    ),
  );

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    _logger.i('''
🚀 REQUEST
Method: ${options.method}
URL: ${options.uri}
Headers: ${options.headers}
Data: ${options.data}
''');
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    _logger.i('''
✅ RESPONSE
Status Code: ${response.statusCode}
URL: ${response.requestOptions.uri}
Data: ${response.data}
''');
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    _logger.e('''
❌ ERROR
Type: ${err.type}
Message: ${err.message}
URL: ${err.requestOptions.uri}
Status Code: ${err.response?.statusCode}
Response Data: ${err.response?.data}
''');
    handler.next(err);
  }
}
