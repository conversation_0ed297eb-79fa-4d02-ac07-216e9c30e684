

import 'package:flutter/material.dart';
import 'package:banachef/core/theme/app_colors.dart';

/// Lớp định nghĩa Theme cho toàn bộ ứng dụng, bao gồm cả Light và Dark mode.
class AppTheme {
  // Private constructor để không cho phép tạo instance từ bên ngoài.
  AppTheme._();

  // --- <PERSON><PERSON><PERSON> nghĩa TextTheme chung ---
  static const String _fontFamilyHeading = 'Nunito';
  static const String _fontFamilyBody = 'Inter';

  // TextTheme cho chế độ sáng
  static final TextTheme _lightTextTheme = _createTextTheme(AppColors.lightText);

  // TextTheme cho chế độ tối
  static final TextTheme _darkTextTheme = _createTextTheme(AppColors.darkText);

  // Hàm helper để tạo TextTheme với màu sắc cụ thể
  static TextTheme _createTextTheme(Color textColor) {
    return const TextTheme(
      // Dùng cho Tiêu đề lớn (Tên màn hình)
      displayLarge: TextStyle(fontFamily: _fontFamilyHeading, fontWeight: FontWeight.bold, fontSize: 28),
      displayMedium: TextStyle(fontFamily: _fontFamilyHeading, fontWeight: FontWeight.bold, fontSize: 24),
      // Dùng cho tiêu đề các section
      headlineMedium: TextStyle(fontFamily: _fontFamilyHeading, fontWeight: FontWeight.bold, fontSize: 20),
      // Dùng cho tên công thức trong card
      titleLarge: TextStyle(fontFamily: _fontFamilyBody, fontWeight: FontWeight.w600, fontSize: 18),
      // Dùng cho văn bản chính, mô tả
      bodyLarge: TextStyle(fontFamily: _fontFamilyBody, fontSize: 16, height: 1.5),
      bodyMedium: TextStyle(fontFamily: _fontFamilyBody, fontSize: 14, height: 1.5),
      // Dùng cho các nút bấm
      labelLarge: TextStyle(fontFamily: _fontFamilyBody, fontWeight: FontWeight.bold, fontSize: 16),
    ).apply(
      bodyColor: textColor,
      displayColor: textColor,
    );
  }

  // --- Theme cho Chế độ Sáng ---
  static final ThemeData lightTheme = ThemeData(
    brightness: Brightness.light,
    fontFamily: _fontFamilyBody,
    colorScheme: const ColorScheme.light(
      primary: AppColors.primary,
      secondary: AppColors.accent,
      background: AppColors.lightBackground,
      surface: AppColors.lightSurface,
      onPrimary: AppColors.lightText,
      onSecondary: Colors.white,
      onBackground: AppColors.lightText,
      onSurface: AppColors.lightText,
      error: AppColors.error,
      onError: Colors.white,
    ),
    scaffoldBackgroundColor: AppColors.lightBackground,
    textTheme: _lightTextTheme,
    appBarTheme: AppBarTheme(
      backgroundColor: AppColors.lightBackground,
      foregroundColor: AppColors.lightText,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: _lightTextTheme.headlineMedium,
    ),
    cardTheme: CardTheme(
      color: AppColors.lightSurface,
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: const BorderSide(color: AppColors.lightBorder, width: 1),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.lightText,
        textStyle: _lightTextTheme.labelLarge,
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        elevation: 2,
      ),
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: AppColors.lightSurface,
      selectedItemColor: AppColors.primary,
      unselectedItemColor: AppColors.lightSecondaryText,
      type: BottomNavigationBarType.fixed,
      elevation: 8,
    ),
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.lightText
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: AppColors.lightSurface,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: AppColors.lightBorder),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: AppColors.lightBorder),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: AppColors.primary, width: 2),
      ),
    ),
  );

  // --- Theme cho Chế độ Tối ---
  static final ThemeData darkTheme = ThemeData(
    brightness: Brightness.dark,
    fontFamily: _fontFamilyBody,
    colorScheme: const ColorScheme.dark(
      primary: AppColors.primary,
      secondary: AppColors.accent,
      background: AppColors.darkBackground,
      surface: AppColors.darkSurface,
      onPrimary: AppColors.lightText, // Vẫn là text tối trên nền vàng
      onSecondary: Colors.white,
      onBackground: AppColors.darkText,
      onSurface: AppColors.darkText,
      error: AppColors.error,
      onError: Colors.white,
    ),
    scaffoldBackgroundColor: AppColors.darkBackground,
    textTheme: _darkTextTheme,
    appBarTheme: AppBarTheme(
      backgroundColor: AppColors.darkBackground,
      foregroundColor: AppColors.darkText,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: _darkTextTheme.headlineMedium,
    ),
    cardTheme: CardTheme(
      color: AppColors.darkSurface,
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: const BorderSide(color: AppColors.darkBorder, width: 1),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.lightText,
        textStyle: _lightTextTheme.labelLarge, // Text trên nút luôn là màu sáng
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        elevation: 2,
      ),
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: AppColors.darkSurface,
      selectedItemColor: AppColors.primary,
      unselectedItemColor: AppColors.darkSecondaryText,
      type: BottomNavigationBarType.fixed,
      elevation: 8,
    ),
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.lightText
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: AppColors.darkSurface,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: AppColors.darkBorder),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: AppColors.darkBorder),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: AppColors.primary, width: 2),
      ),
    ),
  );
}