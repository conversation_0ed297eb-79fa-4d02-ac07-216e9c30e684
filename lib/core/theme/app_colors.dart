// lib/theme/app_colors.dart

import 'package:flutter/material.dart';

/// Lớp chứa tất cả các màu sắc được sử dụng trong ứng dụng BanaChef AI.
/// Việc định nghĩa màu sắc ở đây giúp đảm bảo tính nhất quán
/// và dễ dàng cập nhật trong tương lai.
class AppColors {
  // Bảng màu chính
  static const Color primary = Color(0xFFFFD700); // Vàng Chuối Chín
  static const Color accent = Color(0xFF6ECB63);  // Xanh Lá <PERSON>

  // Màu cho Chế độ Sáng (Light Mode)
  static const Color lightBackground = Color(0xFFFDFDF9); // Trắng Kem
  static const Color lightSurface = Color(0xFFFFFFFF); // Trắng cho thẻ (card)
  static const Color lightText = Color(0xFF36454F);      // Xám Than
  static const Color lightSecondaryText = Color(0xFF616161); // Xám phụ - Cải thiện contrast (4.6:1)
  static const Color lightBorder = Color(0xFFEAEAEA);      // Xám Nhạt cho viền

  // Màu bổ sung cho onboarding
  static const Color onboardingGradientStart = Color(0xFFF0FFF0); // Xanh lá rất nhạt
  static const Color disabled = Color(0xFFEEEEEE); // Màu disabled đơn giản
  static const Color selectionBackground = Color(0x1A6ECB63); // Accent với opacity 0.1 (1A = 26/255 ≈ 0.1)

  // Màu cho Chế độ Tối (Dark Mode)
  static const Color darkBackground = Color(0xFF2C2C2C);  // Xám Đen
  static const Color darkSurface = Color(0xFF3A3A3A);   // Xám Tối cho thẻ (card)
  static const Color darkText = Color(0xFFF5F5DC);       // Trắng Ngà
  static const Color darkSecondaryText = Color(0xFFBDBDBD); // Xám phụ
  static const Color darkBorder = Color(0xFF424242);       // Viền cho Dark Mode

  // Màu sắc khác
  static const Color error = Color(0xFFB00020);
}