import 'dart:io';
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

import 'tables/local_users_table.dart';
import 'tables/local_recipes_table.dart';
import 'tables/meal_plans_table.dart';
import 'tables/image_scan_histories_table.dart';
import 'tables/user_temporary_states_table.dart';

part 'app_database.g.dart';

@DriftDatabase(
  tables: [
    LocalUsers,
    LocalUserProfiles,
    LocalRecipes,
    LocalIngredients,
    LocalRecipeIngredients,
    MealPlans,
    ShoppingListItems,
    ImageScanHistories,
    UserTemporaryStates,
  ],
)
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 2;

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) async {
        await m.createAll();
      },
      onUpgrade: (Migrator m, int from, int to) async {
        // Handle database migrations here
        if (from < 2) {
          // Migration for version 2: Add referral_code column to LocalUsers
          await m.addColumn(localUsers, localUsers.referralCode);
        }
      },
    );
  }
}
LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'banachef.db'));
    return NativeDatabase.createInBackground(file);
  });
}
