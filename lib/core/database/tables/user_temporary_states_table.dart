import 'package:drift/drift.dart';

/// Bảng UserTemporaryStates - Lưu trữ trạng thái/nhu cầu tạm thời của người dùng
/// Logic ứng dụng sẽ đảm bảo chỉ có một trạng thái được isActive
@DataClassName('UserTemporaryState')
class UserTemporaryStates extends Table {
  /// Khóa chính tự tăng (chỉ có ý nghĩa ở local)
  IntColumn get id => integer().autoIncrement()();
  
  /// state_id từ server để đồng bộ (có thể null)
  TextColumn get stateIdFromServer => text().nullable()();
  
  /// Tham chiếu đến LocalUsers.id
  TextColumn get userId => text()();
  
  /// Thẻ mô tả trạng thái (ví dụ: 'feeling_sick')
  TextColumn get stateTag => text()();
  
  /// Mô tả chi tiết thêm (c<PERSON> thể null)
  TextColumn get stateDescription => text().nullable()();
  
  /// Thời điểm trạng thái bắt đầu áp dụng
  DateTimeColumn get appliesFrom => dateTime()();
  
  /// Thời điểm trạng thái kết thúc (có thể null)
  DateTimeColumn get appliesUntil => dateTime().nullable()();
  
  /// Đánh dấu trạng thái đang hoạt động
  BoolColumn get isActive => boolean().withDefault(const Constant(false))();
  
  /// Trạng thái đồng bộ: 'synced', 'needs_create', v.v.
  TextColumn get syncStatus => text().withDefault(const Constant('needs_create'))();
}
