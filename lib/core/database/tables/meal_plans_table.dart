import 'package:drift/drift.dart';

/// Bảng MealPlans - L<PERSON><PERSON> trình bữa ăn cụ thể của người dùng
@DataClassName('MealPlan')
class MealPlans extends Table {
  /// Khóa chính tự tăng (chỉ có ý nghĩa ở local)
  IntColumn get id => integer().autoIncrement()();

  /// Ngày của bữa ăn
  DateTimeColumn get date => dateTime()();

  /// Ví dụ: 'Bữa sáng', 'Bữa trưa', 'Bữa tối'
  TextColumn get mealType => text()();

  /// Tham chiếu đến LocalRecipes.id
  TextColumn get recipeId => text()();

  /// Trạng thái đồng bộ: 'synced', 'needs_create', v.v.
  TextColumn get syncStatus => text().withDefault(const Constant('needs_create'))();
}

/// Bảng ShoppingListItems - <PERSON><PERSON><PERSON> mục trong danh sách mua sắm, đ<PERSON><PERSON><PERSON> tạo tự động từ MealPlan
@DataClassName('ShoppingListItem')
class ShoppingListItems extends Table {
  /// Khóa chính tự tăng
  IntColumn get id => integer().autoIncrement()();

  /// Tham chiếu đến LocalIngredients.id
  TextColumn get ingredientId => text()();

  /// Tổng số lượng cần mua (đã được gom lại)
  RealColumn get totalQuantity => real()();

  /// Trạng thái đã mua hay chưa
  BoolColumn get isPurchased => boolean().withDefault(const Constant(false))();
}
