import 'package:drift/drift.dart';

/// Bảng ImageScanHistories - <PERSON><PERSON><PERSON> lại siêu dữ liệu (metadata) của mỗi lần quét ảnh
@DataClassName('ImageScanHistory')
class ImageScanHistories extends Table {
  /// Kh<PERSON>a chính. Chính là scan_id từ server
  TextColumn get id => text()();
  
  /// Loại quét: 'cooked_meal' (F2a) hoặc 'ingredients' (F2b)
  TextColumn get scanType => text()();
  
  /// Thời gian người dùng thực hiện quét
  DateTimeColumn get requestTimestamp => dateTime()();
  
  /// Đường dẫn file ảnh đã cache hoặc URL gốc
  TextColumn get cachedImageUrl => text()();
  
  /// Chuỗi JSON chứa kết quả người dùng đã xác nhận/sửa
  TextColumn get userConfirmedResultJson => text().nullable()();
  
  /// recipe_id đã được liên kết (có thể null)
  TextColumn get linkedRecipeId => text().nullable()();

  @override
  Set<Column> get primaryKey => {id};
}
