import 'package:drift/drift.dart';

/// Bảng LocalUsers - Chỉ lưu một dòng duy nhất cho người dùng đang đăng nhập trên thiết bị
@DataClassName('LocalUser')
class LocalUsers extends Table {
  /// Khóa chính. Chính là user_id từ server
  TextColumn get id => text()();
  
  /// Email của người dùng
  TextColumn get email => text()();
  
  /// Tên hiển thị (có thể null)
  TextColumn get displayName => text().nullable()();
  
  /// URL ảnh đại diện (có thể null)
  TextColumn get photoUrl => text().nullable()();

  /// Mã giới thiệu của người dùng (có thể null)
  TextColumn get referralCode => text().nullable()();

  /// Thời gian đồng bộ cuối cùng
  DateTimeColumn get lastSyncedAt => dateTime()();

  @override
  Set<Column> get primaryKey => {id};
}

/// Bảng LocalUserProfiles - <PERSON>ản sao đầy đủ của hồ sơ người dùng để truy cập offline
@DataClassName('LocalUserProfile')
class LocalUserProfiles extends Table {
  // Khóa & Thời gian
  /// Khóa chính. Tham chiếu đến LocalUsers.id
  TextColumn get userId => text()();
  
  /// Thời gian hồ sơ được cập nhật lần cuối
  DateTimeColumn get updatedAt => dateTime()();

  // Thông Tin Sức Khỏe & Thể Chất
  /// Ngày tháng năm sinh
  DateTimeColumn get dateOfBirth => dateTime().nullable()();
  
  /// Giới tính (ví dụ: 'nam', 'nữ', 'khác')
  TextColumn get gender => text().nullable()();
  
  /// Cân nặng (kg)
  RealColumn get weightKg => real().nullable()();
  
  /// Chiều cao (cm)
  RealColumn get heightCm => real().nullable()();
  
  /// Mức độ hoạt động (ví dụ: 'ít vận động')
  TextColumn get activityLevel => text().nullable()();
  
  /// Lưu chuỗi JSON của mảng mục tiêu sức khỏe
  TextColumn get healthGoals => text().nullable()();
  
  /// Lưu chuỗi JSON của mảng dị ứng thực phẩm
  TextColumn get foodAllergies => text().nullable()();
  
  /// Lưu chuỗi JSON của mảng tình trạng bệnh lý
  TextColumn get medicalConditions => text().nullable()();
  
  /// Phụ nữ mang thai hoặc cho con bú
  BoolColumn get isPregnantOrBreastfeeding => boolean().withDefault(const Constant(false))();

  // Thông Tin Khẩu Vị & Sở Thích
  /// Mức độ ăn cay ưa thích (ví dụ: 'không cay')
  TextColumn get spiceLevelPreference => text().nullable()();
  
  /// Lưu chuỗi JSON mảng thực phẩm/nguyên liệu yêu thích
  TextColumn get favoriteFoodsIngredients => text().nullable()();
  
  /// Lưu chuỗi JSON mảng thực phẩm/nguyên liệu không thích
  TextColumn get dislikedFoodsIngredients => text().nullable()();
  
  /// Lưu chuỗi JSON mảng sở thích về kết cấu món ăn
  TextColumn get texturePreferences => text().nullable()();

  // Thông Tin Văn Hóa & Lối Sống
  /// Quốc gia/Vùng miền sinh sống
  TextColumn get countryRegionOfResidence => text().nullable()();
  
  /// Lưu chuỗi JSON mảng các nền ẩm thực yêu thích
  TextColumn get preferredCuisines => text().nullable()();
  
  /// Lối sống ăn uống (ví dụ: 'vegan', 'keto')
  TextColumn get eatingLifestyle => text().nullable()();
  
  /// Lưu chuỗi JSON mảng các bữa ăn thường ngày
  TextColumn get selectedMealTypes => text().nullable()();

  // Thông Tin Kỹ Năng & Điều Kiện Nấu Nướng
  /// Trình độ nấu ăn (ví dụ: 'mới bắt đầu')
  TextColumn get cookingSkillLevel => text().nullable()();
  
  /// Thời gian trung bình có thể dành cho việc nấu ăn
  IntColumn get avgTimeForMainMealPrepCookMinutes => integer().nullable()();
  
  /// Lưu chuỗi JSON mảng thiết bị nhà bếp có sẵn
  TextColumn get availableKitchenEquipment => text().nullable()();

  @override
  Set<Column> get primaryKey => {userId};
}
