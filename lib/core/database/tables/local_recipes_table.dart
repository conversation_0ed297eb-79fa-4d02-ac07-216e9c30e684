import 'package:drift/drift.dart';

/// Bảng LocalRecipes - <PERSON><PERSON><PERSON> chi tiết các công thức cần thiết cho việc sử dụng offline
@DataClassName('LocalRecipe')
class LocalRecipes extends Table {
  // Thông tin cơ bản
  /// Kh<PERSON>a chính. Chính là recipe_id từ server
  TextColumn get id => text()();
  
  /// Tên công thức
  TextColumn get name => text()();
  
  /// Mô tả ngắn về công thức (có thể null)
  TextColumn get description => text().nullable()();
  
  /// Hướng dẫn nấu ăn chi tiết
  TextColumn get instructions => text()();
  
  /// URL hình ảnh minh họa (có thể null)
  TextColumn get imageUrl => text().nullable()();
  
  /// Nguồn gốc công thức (ví dụ: 'ai_gemini')
  TextColumn get source => text()();
  
  /// <PERSON><PERSON>h dấu công thức yêu thích
  <PERSON>ol<PERSON>olumn get isFavorite => boolean().withDefault(const Constant(false))();

  // Thời gian & Khẩu phần
  /// Thời gian chuẩn bị (phút, có thể null)
  IntColumn get prepTimeMinutes => integer().nullable()();
  
  /// Thời gian nấu (phút, có thể null)
  IntColumn get cookTimeMinutes => integer().nullable()();
  
  /// Số khẩu phần ăn dự kiến của công thức
  IntColumn get servings => integer()();

  // Dinh dưỡng cơ bản (mỗi khẩu phần)
  /// Lượng calo ước tính (có thể null)
  IntColumn get caloriesPerServing => integer().nullable()();
  
  /// Lượng protein (g) ước tính (có thể null)
  RealColumn get proteinGPerServing => real().nullable()();
  
  /// Lượng carb (g) ước tính (có thể null)
  RealColumn get carbsGPerServing => real().nullable()();
  
  /// Lượng chất béo (g) ước tính (có thể null)
  RealColumn get fatGPerServing => real().nullable()();

  // Dinh dưỡng & Thông tin chi tiết (JSON)
  /// Tóm tắt/đánh giá dinh dưỡng tổng thể (có thể null)
  TextColumn get totalDishNutrientSummary => text().nullable()();
  
  /// Tác dụng của món ăn, các lưu ý đặc biệt (có thể null)
  TextColumn get dishEffectsAndNotes => text().nullable()();
  
  /// Lưu chuỗi JSON các chỉ số sức khỏe tim mạch
  TextColumn get totalDishHeartHealthIndicatorsJson => text().nullable()();
  
  /// Lưu chuỗi JSON thông tin vitamin
  TextColumn get totalDishVitaminsJson => text().nullable()();
  
  /// Lưu chuỗi JSON thông tin khoáng chất
  TextColumn get totalDishMineralsJson => text().nullable()();

  @override
  Set<Column> get primaryKey => {id};
}

/// Bảng LocalIngredients - Bảng master chứa thông tin về các nguyên liệu
@DataClassName('LocalIngredient')
class LocalIngredients extends Table {
  /// Khóa chính. Chính là ingredient_id từ server
  TextColumn get id => text()();
  
  /// Tên nguyên liệu, là duy nhất
  TextColumn get name => text().unique()();
  
  /// Đơn vị mặc định (ví dụ: 'g', 'ml')
  TextColumn get defaultUnit => text()();

  @override
  Set<Column> get primaryKey => {id};
}

/// Bảng LocalRecipeIngredients - Liên kết công thức và nguyên liệu
@DataClassName('LocalRecipeIngredient')
class LocalRecipeIngredients extends Table {
  /// Phần của Khóa chính phức hợp. Tham chiếu LocalRecipes.id
  TextColumn get recipeId => text()();
  
  /// Phần của Khóa chính phức hợp. Tham chiếu LocalIngredients.id
  TextColumn get ingredientId => text()();
  
  /// Số lượng cần thiết
  RealColumn get quantity => real()();
  
  /// Đơn vị tính của số lượng
  TextColumn get unit => text()();

  @override
  Set<Column> get primaryKey => {recipeId, ingredientId};
}
