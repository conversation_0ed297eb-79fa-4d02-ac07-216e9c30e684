// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_database.dart';

// ignore_for_file: type=lint
class $LocalUsersTable extends LocalUsers
    with TableInfo<$LocalUsersTable, LocalUser> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $LocalUsersTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
    'id',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _emailMeta = const VerificationMeta('email');
  @override
  late final GeneratedColumn<String> email = GeneratedColumn<String>(
    'email',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _displayNameMeta = const VerificationMeta(
    'displayName',
  );
  @override
  late final GeneratedColumn<String> displayName = GeneratedColumn<String>(
    'display_name',
    aliasedName,
    true,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _photoUrlMeta = const VerificationMeta(
    'photoUrl',
  );
  @override
  late final GeneratedColumn<String> photoUrl = GeneratedColumn<String>(
    'photo_url',
    aliasedName,
    true,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _referralCodeMeta = const VerificationMeta(
    'referralCode',
  );
  @override
  late final GeneratedColumn<String> referralCode = GeneratedColumn<String>(
    'referral_code',
    aliasedName,
    true,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _lastSyncedAtMeta = const VerificationMeta(
    'lastSyncedAt',
  );
  @override
  late final GeneratedColumn<DateTime> lastSyncedAt = GeneratedColumn<DateTime>(
    'last_synced_at',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: true,
  );
  @override
  List<GeneratedColumn> get $columns => [
    id,
    email,
    displayName,
    photoUrl,
    referralCode,
    lastSyncedAt,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'local_users';
  @override
  VerificationContext validateIntegrity(
    Insertable<LocalUser> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('email')) {
      context.handle(
        _emailMeta,
        email.isAcceptableOrUnknown(data['email']!, _emailMeta),
      );
    } else if (isInserting) {
      context.missing(_emailMeta);
    }
    if (data.containsKey('display_name')) {
      context.handle(
        _displayNameMeta,
        displayName.isAcceptableOrUnknown(
          data['display_name']!,
          _displayNameMeta,
        ),
      );
    }
    if (data.containsKey('photo_url')) {
      context.handle(
        _photoUrlMeta,
        photoUrl.isAcceptableOrUnknown(data['photo_url']!, _photoUrlMeta),
      );
    }
    if (data.containsKey('referral_code')) {
      context.handle(
        _referralCodeMeta,
        referralCode.isAcceptableOrUnknown(
          data['referral_code']!,
          _referralCodeMeta,
        ),
      );
    }
    if (data.containsKey('last_synced_at')) {
      context.handle(
        _lastSyncedAtMeta,
        lastSyncedAt.isAcceptableOrUnknown(
          data['last_synced_at']!,
          _lastSyncedAtMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_lastSyncedAtMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  LocalUser map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return LocalUser(
      id:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}id'],
          )!,
      email:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}email'],
          )!,
      displayName: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}display_name'],
      ),
      photoUrl: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}photo_url'],
      ),
      referralCode: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}referral_code'],
      ),
      lastSyncedAt:
          attachedDatabase.typeMapping.read(
            DriftSqlType.dateTime,
            data['${effectivePrefix}last_synced_at'],
          )!,
    );
  }

  @override
  $LocalUsersTable createAlias(String alias) {
    return $LocalUsersTable(attachedDatabase, alias);
  }
}

class LocalUser extends DataClass implements Insertable<LocalUser> {
  /// Khóa chính. Chính là user_id từ server
  final String id;

  /// Email của người dùng
  final String email;

  /// Tên hiển thị (có thể null)
  final String? displayName;

  /// URL ảnh đại diện (có thể null)
  final String? photoUrl;

  /// Mã giới thiệu của người dùng (có thể null)
  final String? referralCode;

  /// Thời gian đồng bộ cuối cùng
  final DateTime lastSyncedAt;
  const LocalUser({
    required this.id,
    required this.email,
    this.displayName,
    this.photoUrl,
    this.referralCode,
    required this.lastSyncedAt,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['email'] = Variable<String>(email);
    if (!nullToAbsent || displayName != null) {
      map['display_name'] = Variable<String>(displayName);
    }
    if (!nullToAbsent || photoUrl != null) {
      map['photo_url'] = Variable<String>(photoUrl);
    }
    if (!nullToAbsent || referralCode != null) {
      map['referral_code'] = Variable<String>(referralCode);
    }
    map['last_synced_at'] = Variable<DateTime>(lastSyncedAt);
    return map;
  }

  LocalUsersCompanion toCompanion(bool nullToAbsent) {
    return LocalUsersCompanion(
      id: Value(id),
      email: Value(email),
      displayName:
          displayName == null && nullToAbsent
              ? const Value.absent()
              : Value(displayName),
      photoUrl:
          photoUrl == null && nullToAbsent
              ? const Value.absent()
              : Value(photoUrl),
      referralCode:
          referralCode == null && nullToAbsent
              ? const Value.absent()
              : Value(referralCode),
      lastSyncedAt: Value(lastSyncedAt),
    );
  }

  factory LocalUser.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return LocalUser(
      id: serializer.fromJson<String>(json['id']),
      email: serializer.fromJson<String>(json['email']),
      displayName: serializer.fromJson<String?>(json['displayName']),
      photoUrl: serializer.fromJson<String?>(json['photoUrl']),
      referralCode: serializer.fromJson<String?>(json['referralCode']),
      lastSyncedAt: serializer.fromJson<DateTime>(json['lastSyncedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'email': serializer.toJson<String>(email),
      'displayName': serializer.toJson<String?>(displayName),
      'photoUrl': serializer.toJson<String?>(photoUrl),
      'referralCode': serializer.toJson<String?>(referralCode),
      'lastSyncedAt': serializer.toJson<DateTime>(lastSyncedAt),
    };
  }

  LocalUser copyWith({
    String? id,
    String? email,
    Value<String?> displayName = const Value.absent(),
    Value<String?> photoUrl = const Value.absent(),
    Value<String?> referralCode = const Value.absent(),
    DateTime? lastSyncedAt,
  }) => LocalUser(
    id: id ?? this.id,
    email: email ?? this.email,
    displayName: displayName.present ? displayName.value : this.displayName,
    photoUrl: photoUrl.present ? photoUrl.value : this.photoUrl,
    referralCode: referralCode.present ? referralCode.value : this.referralCode,
    lastSyncedAt: lastSyncedAt ?? this.lastSyncedAt,
  );
  LocalUser copyWithCompanion(LocalUsersCompanion data) {
    return LocalUser(
      id: data.id.present ? data.id.value : this.id,
      email: data.email.present ? data.email.value : this.email,
      displayName:
          data.displayName.present ? data.displayName.value : this.displayName,
      photoUrl: data.photoUrl.present ? data.photoUrl.value : this.photoUrl,
      referralCode:
          data.referralCode.present
              ? data.referralCode.value
              : this.referralCode,
      lastSyncedAt:
          data.lastSyncedAt.present
              ? data.lastSyncedAt.value
              : this.lastSyncedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('LocalUser(')
          ..write('id: $id, ')
          ..write('email: $email, ')
          ..write('displayName: $displayName, ')
          ..write('photoUrl: $photoUrl, ')
          ..write('referralCode: $referralCode, ')
          ..write('lastSyncedAt: $lastSyncedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode =>
      Object.hash(id, email, displayName, photoUrl, referralCode, lastSyncedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is LocalUser &&
          other.id == this.id &&
          other.email == this.email &&
          other.displayName == this.displayName &&
          other.photoUrl == this.photoUrl &&
          other.referralCode == this.referralCode &&
          other.lastSyncedAt == this.lastSyncedAt);
}

class LocalUsersCompanion extends UpdateCompanion<LocalUser> {
  final Value<String> id;
  final Value<String> email;
  final Value<String?> displayName;
  final Value<String?> photoUrl;
  final Value<String?> referralCode;
  final Value<DateTime> lastSyncedAt;
  final Value<int> rowid;
  const LocalUsersCompanion({
    this.id = const Value.absent(),
    this.email = const Value.absent(),
    this.displayName = const Value.absent(),
    this.photoUrl = const Value.absent(),
    this.referralCode = const Value.absent(),
    this.lastSyncedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  LocalUsersCompanion.insert({
    required String id,
    required String email,
    this.displayName = const Value.absent(),
    this.photoUrl = const Value.absent(),
    this.referralCode = const Value.absent(),
    required DateTime lastSyncedAt,
    this.rowid = const Value.absent(),
  }) : id = Value(id),
       email = Value(email),
       lastSyncedAt = Value(lastSyncedAt);
  static Insertable<LocalUser> custom({
    Expression<String>? id,
    Expression<String>? email,
    Expression<String>? displayName,
    Expression<String>? photoUrl,
    Expression<String>? referralCode,
    Expression<DateTime>? lastSyncedAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (email != null) 'email': email,
      if (displayName != null) 'display_name': displayName,
      if (photoUrl != null) 'photo_url': photoUrl,
      if (referralCode != null) 'referral_code': referralCode,
      if (lastSyncedAt != null) 'last_synced_at': lastSyncedAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  LocalUsersCompanion copyWith({
    Value<String>? id,
    Value<String>? email,
    Value<String?>? displayName,
    Value<String?>? photoUrl,
    Value<String?>? referralCode,
    Value<DateTime>? lastSyncedAt,
    Value<int>? rowid,
  }) {
    return LocalUsersCompanion(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoUrl: photoUrl ?? this.photoUrl,
      referralCode: referralCode ?? this.referralCode,
      lastSyncedAt: lastSyncedAt ?? this.lastSyncedAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (email.present) {
      map['email'] = Variable<String>(email.value);
    }
    if (displayName.present) {
      map['display_name'] = Variable<String>(displayName.value);
    }
    if (photoUrl.present) {
      map['photo_url'] = Variable<String>(photoUrl.value);
    }
    if (referralCode.present) {
      map['referral_code'] = Variable<String>(referralCode.value);
    }
    if (lastSyncedAt.present) {
      map['last_synced_at'] = Variable<DateTime>(lastSyncedAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('LocalUsersCompanion(')
          ..write('id: $id, ')
          ..write('email: $email, ')
          ..write('displayName: $displayName, ')
          ..write('photoUrl: $photoUrl, ')
          ..write('referralCode: $referralCode, ')
          ..write('lastSyncedAt: $lastSyncedAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $LocalUserProfilesTable extends LocalUserProfiles
    with TableInfo<$LocalUserProfilesTable, LocalUserProfile> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $LocalUserProfilesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _userIdMeta = const VerificationMeta('userId');
  @override
  late final GeneratedColumn<String> userId = GeneratedColumn<String>(
    'user_id',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _updatedAtMeta = const VerificationMeta(
    'updatedAt',
  );
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
    'updated_at',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _dateOfBirthMeta = const VerificationMeta(
    'dateOfBirth',
  );
  @override
  late final GeneratedColumn<DateTime> dateOfBirth = GeneratedColumn<DateTime>(
    'date_of_birth',
    aliasedName,
    true,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _genderMeta = const VerificationMeta('gender');
  @override
  late final GeneratedColumn<String> gender = GeneratedColumn<String>(
    'gender',
    aliasedName,
    true,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _weightKgMeta = const VerificationMeta(
    'weightKg',
  );
  @override
  late final GeneratedColumn<double> weightKg = GeneratedColumn<double>(
    'weight_kg',
    aliasedName,
    true,
    type: DriftSqlType.double,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _heightCmMeta = const VerificationMeta(
    'heightCm',
  );
  @override
  late final GeneratedColumn<double> heightCm = GeneratedColumn<double>(
    'height_cm',
    aliasedName,
    true,
    type: DriftSqlType.double,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _activityLevelMeta = const VerificationMeta(
    'activityLevel',
  );
  @override
  late final GeneratedColumn<String> activityLevel = GeneratedColumn<String>(
    'activity_level',
    aliasedName,
    true,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _healthGoalsMeta = const VerificationMeta(
    'healthGoals',
  );
  @override
  late final GeneratedColumn<String> healthGoals = GeneratedColumn<String>(
    'health_goals',
    aliasedName,
    true,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _foodAllergiesMeta = const VerificationMeta(
    'foodAllergies',
  );
  @override
  late final GeneratedColumn<String> foodAllergies = GeneratedColumn<String>(
    'food_allergies',
    aliasedName,
    true,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _medicalConditionsMeta = const VerificationMeta(
    'medicalConditions',
  );
  @override
  late final GeneratedColumn<String> medicalConditions =
      GeneratedColumn<String>(
        'medical_conditions',
        aliasedName,
        true,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
      );
  static const VerificationMeta _isPregnantOrBreastfeedingMeta =
      const VerificationMeta('isPregnantOrBreastfeeding');
  @override
  late final GeneratedColumn<bool> isPregnantOrBreastfeeding =
      GeneratedColumn<bool>(
        'is_pregnant_or_breastfeeding',
        aliasedName,
        false,
        type: DriftSqlType.bool,
        requiredDuringInsert: false,
        defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("is_pregnant_or_breastfeeding" IN (0, 1))',
        ),
        defaultValue: const Constant(false),
      );
  static const VerificationMeta _spiceLevelPreferenceMeta =
      const VerificationMeta('spiceLevelPreference');
  @override
  late final GeneratedColumn<String> spiceLevelPreference =
      GeneratedColumn<String>(
        'spice_level_preference',
        aliasedName,
        true,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
      );
  static const VerificationMeta _favoriteFoodsIngredientsMeta =
      const VerificationMeta('favoriteFoodsIngredients');
  @override
  late final GeneratedColumn<String> favoriteFoodsIngredients =
      GeneratedColumn<String>(
        'favorite_foods_ingredients',
        aliasedName,
        true,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
      );
  static const VerificationMeta _dislikedFoodsIngredientsMeta =
      const VerificationMeta('dislikedFoodsIngredients');
  @override
  late final GeneratedColumn<String> dislikedFoodsIngredients =
      GeneratedColumn<String>(
        'disliked_foods_ingredients',
        aliasedName,
        true,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
      );
  static const VerificationMeta _texturePreferencesMeta =
      const VerificationMeta('texturePreferences');
  @override
  late final GeneratedColumn<String> texturePreferences =
      GeneratedColumn<String>(
        'texture_preferences',
        aliasedName,
        true,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
      );
  static const VerificationMeta _countryRegionOfResidenceMeta =
      const VerificationMeta('countryRegionOfResidence');
  @override
  late final GeneratedColumn<String> countryRegionOfResidence =
      GeneratedColumn<String>(
        'country_region_of_residence',
        aliasedName,
        true,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
      );
  static const VerificationMeta _preferredCuisinesMeta = const VerificationMeta(
    'preferredCuisines',
  );
  @override
  late final GeneratedColumn<String> preferredCuisines =
      GeneratedColumn<String>(
        'preferred_cuisines',
        aliasedName,
        true,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
      );
  static const VerificationMeta _eatingLifestyleMeta = const VerificationMeta(
    'eatingLifestyle',
  );
  @override
  late final GeneratedColumn<String> eatingLifestyle = GeneratedColumn<String>(
    'eating_lifestyle',
    aliasedName,
    true,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _selectedMealTypesMeta = const VerificationMeta(
    'selectedMealTypes',
  );
  @override
  late final GeneratedColumn<String> selectedMealTypes =
      GeneratedColumn<String>(
        'selected_meal_types',
        aliasedName,
        true,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
      );
  static const VerificationMeta _cookingSkillLevelMeta = const VerificationMeta(
    'cookingSkillLevel',
  );
  @override
  late final GeneratedColumn<String> cookingSkillLevel =
      GeneratedColumn<String>(
        'cooking_skill_level',
        aliasedName,
        true,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
      );
  static const VerificationMeta _avgTimeForMainMealPrepCookMinutesMeta =
      const VerificationMeta('avgTimeForMainMealPrepCookMinutes');
  @override
  late final GeneratedColumn<int> avgTimeForMainMealPrepCookMinutes =
      GeneratedColumn<int>(
        'avg_time_for_main_meal_prep_cook_minutes',
        aliasedName,
        true,
        type: DriftSqlType.int,
        requiredDuringInsert: false,
      );
  static const VerificationMeta _availableKitchenEquipmentMeta =
      const VerificationMeta('availableKitchenEquipment');
  @override
  late final GeneratedColumn<String> availableKitchenEquipment =
      GeneratedColumn<String>(
        'available_kitchen_equipment',
        aliasedName,
        true,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
      );
  @override
  List<GeneratedColumn> get $columns => [
    userId,
    updatedAt,
    dateOfBirth,
    gender,
    weightKg,
    heightCm,
    activityLevel,
    healthGoals,
    foodAllergies,
    medicalConditions,
    isPregnantOrBreastfeeding,
    spiceLevelPreference,
    favoriteFoodsIngredients,
    dislikedFoodsIngredients,
    texturePreferences,
    countryRegionOfResidence,
    preferredCuisines,
    eatingLifestyle,
    selectedMealTypes,
    cookingSkillLevel,
    avgTimeForMainMealPrepCookMinutes,
    availableKitchenEquipment,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'local_user_profiles';
  @override
  VerificationContext validateIntegrity(
    Insertable<LocalUserProfile> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('user_id')) {
      context.handle(
        _userIdMeta,
        userId.isAcceptableOrUnknown(data['user_id']!, _userIdMeta),
      );
    } else if (isInserting) {
      context.missing(_userIdMeta);
    }
    if (data.containsKey('updated_at')) {
      context.handle(
        _updatedAtMeta,
        updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta),
      );
    } else if (isInserting) {
      context.missing(_updatedAtMeta);
    }
    if (data.containsKey('date_of_birth')) {
      context.handle(
        _dateOfBirthMeta,
        dateOfBirth.isAcceptableOrUnknown(
          data['date_of_birth']!,
          _dateOfBirthMeta,
        ),
      );
    }
    if (data.containsKey('gender')) {
      context.handle(
        _genderMeta,
        gender.isAcceptableOrUnknown(data['gender']!, _genderMeta),
      );
    }
    if (data.containsKey('weight_kg')) {
      context.handle(
        _weightKgMeta,
        weightKg.isAcceptableOrUnknown(data['weight_kg']!, _weightKgMeta),
      );
    }
    if (data.containsKey('height_cm')) {
      context.handle(
        _heightCmMeta,
        heightCm.isAcceptableOrUnknown(data['height_cm']!, _heightCmMeta),
      );
    }
    if (data.containsKey('activity_level')) {
      context.handle(
        _activityLevelMeta,
        activityLevel.isAcceptableOrUnknown(
          data['activity_level']!,
          _activityLevelMeta,
        ),
      );
    }
    if (data.containsKey('health_goals')) {
      context.handle(
        _healthGoalsMeta,
        healthGoals.isAcceptableOrUnknown(
          data['health_goals']!,
          _healthGoalsMeta,
        ),
      );
    }
    if (data.containsKey('food_allergies')) {
      context.handle(
        _foodAllergiesMeta,
        foodAllergies.isAcceptableOrUnknown(
          data['food_allergies']!,
          _foodAllergiesMeta,
        ),
      );
    }
    if (data.containsKey('medical_conditions')) {
      context.handle(
        _medicalConditionsMeta,
        medicalConditions.isAcceptableOrUnknown(
          data['medical_conditions']!,
          _medicalConditionsMeta,
        ),
      );
    }
    if (data.containsKey('is_pregnant_or_breastfeeding')) {
      context.handle(
        _isPregnantOrBreastfeedingMeta,
        isPregnantOrBreastfeeding.isAcceptableOrUnknown(
          data['is_pregnant_or_breastfeeding']!,
          _isPregnantOrBreastfeedingMeta,
        ),
      );
    }
    if (data.containsKey('spice_level_preference')) {
      context.handle(
        _spiceLevelPreferenceMeta,
        spiceLevelPreference.isAcceptableOrUnknown(
          data['spice_level_preference']!,
          _spiceLevelPreferenceMeta,
        ),
      );
    }
    if (data.containsKey('favorite_foods_ingredients')) {
      context.handle(
        _favoriteFoodsIngredientsMeta,
        favoriteFoodsIngredients.isAcceptableOrUnknown(
          data['favorite_foods_ingredients']!,
          _favoriteFoodsIngredientsMeta,
        ),
      );
    }
    if (data.containsKey('disliked_foods_ingredients')) {
      context.handle(
        _dislikedFoodsIngredientsMeta,
        dislikedFoodsIngredients.isAcceptableOrUnknown(
          data['disliked_foods_ingredients']!,
          _dislikedFoodsIngredientsMeta,
        ),
      );
    }
    if (data.containsKey('texture_preferences')) {
      context.handle(
        _texturePreferencesMeta,
        texturePreferences.isAcceptableOrUnknown(
          data['texture_preferences']!,
          _texturePreferencesMeta,
        ),
      );
    }
    if (data.containsKey('country_region_of_residence')) {
      context.handle(
        _countryRegionOfResidenceMeta,
        countryRegionOfResidence.isAcceptableOrUnknown(
          data['country_region_of_residence']!,
          _countryRegionOfResidenceMeta,
        ),
      );
    }
    if (data.containsKey('preferred_cuisines')) {
      context.handle(
        _preferredCuisinesMeta,
        preferredCuisines.isAcceptableOrUnknown(
          data['preferred_cuisines']!,
          _preferredCuisinesMeta,
        ),
      );
    }
    if (data.containsKey('eating_lifestyle')) {
      context.handle(
        _eatingLifestyleMeta,
        eatingLifestyle.isAcceptableOrUnknown(
          data['eating_lifestyle']!,
          _eatingLifestyleMeta,
        ),
      );
    }
    if (data.containsKey('selected_meal_types')) {
      context.handle(
        _selectedMealTypesMeta,
        selectedMealTypes.isAcceptableOrUnknown(
          data['selected_meal_types']!,
          _selectedMealTypesMeta,
        ),
      );
    }
    if (data.containsKey('cooking_skill_level')) {
      context.handle(
        _cookingSkillLevelMeta,
        cookingSkillLevel.isAcceptableOrUnknown(
          data['cooking_skill_level']!,
          _cookingSkillLevelMeta,
        ),
      );
    }
    if (data.containsKey('avg_time_for_main_meal_prep_cook_minutes')) {
      context.handle(
        _avgTimeForMainMealPrepCookMinutesMeta,
        avgTimeForMainMealPrepCookMinutes.isAcceptableOrUnknown(
          data['avg_time_for_main_meal_prep_cook_minutes']!,
          _avgTimeForMainMealPrepCookMinutesMeta,
        ),
      );
    }
    if (data.containsKey('available_kitchen_equipment')) {
      context.handle(
        _availableKitchenEquipmentMeta,
        availableKitchenEquipment.isAcceptableOrUnknown(
          data['available_kitchen_equipment']!,
          _availableKitchenEquipmentMeta,
        ),
      );
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {userId};
  @override
  LocalUserProfile map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return LocalUserProfile(
      userId:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}user_id'],
          )!,
      updatedAt:
          attachedDatabase.typeMapping.read(
            DriftSqlType.dateTime,
            data['${effectivePrefix}updated_at'],
          )!,
      dateOfBirth: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}date_of_birth'],
      ),
      gender: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}gender'],
      ),
      weightKg: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}weight_kg'],
      ),
      heightCm: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}height_cm'],
      ),
      activityLevel: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}activity_level'],
      ),
      healthGoals: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}health_goals'],
      ),
      foodAllergies: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}food_allergies'],
      ),
      medicalConditions: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}medical_conditions'],
      ),
      isPregnantOrBreastfeeding:
          attachedDatabase.typeMapping.read(
            DriftSqlType.bool,
            data['${effectivePrefix}is_pregnant_or_breastfeeding'],
          )!,
      spiceLevelPreference: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}spice_level_preference'],
      ),
      favoriteFoodsIngredients: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}favorite_foods_ingredients'],
      ),
      dislikedFoodsIngredients: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}disliked_foods_ingredients'],
      ),
      texturePreferences: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}texture_preferences'],
      ),
      countryRegionOfResidence: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}country_region_of_residence'],
      ),
      preferredCuisines: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}preferred_cuisines'],
      ),
      eatingLifestyle: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}eating_lifestyle'],
      ),
      selectedMealTypes: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}selected_meal_types'],
      ),
      cookingSkillLevel: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}cooking_skill_level'],
      ),
      avgTimeForMainMealPrepCookMinutes: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}avg_time_for_main_meal_prep_cook_minutes'],
      ),
      availableKitchenEquipment: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}available_kitchen_equipment'],
      ),
    );
  }

  @override
  $LocalUserProfilesTable createAlias(String alias) {
    return $LocalUserProfilesTable(attachedDatabase, alias);
  }
}

class LocalUserProfile extends DataClass
    implements Insertable<LocalUserProfile> {
  /// Khóa chính. Tham chiếu đến LocalUsers.id
  final String userId;

  /// Thời gian hồ sơ được cập nhật lần cuối
  final DateTime updatedAt;

  /// Ngày tháng năm sinh
  final DateTime? dateOfBirth;

  /// Giới tính (ví dụ: 'nam', 'nữ', 'khác')
  final String? gender;

  /// Cân nặng (kg)
  final double? weightKg;

  /// Chiều cao (cm)
  final double? heightCm;

  /// Mức độ hoạt động (ví dụ: 'ít vận động')
  final String? activityLevel;

  /// Lưu chuỗi JSON của mảng mục tiêu sức khỏe
  final String? healthGoals;

  /// Lưu chuỗi JSON của mảng dị ứng thực phẩm
  final String? foodAllergies;

  /// Lưu chuỗi JSON của mảng tình trạng bệnh lý
  final String? medicalConditions;

  /// Phụ nữ mang thai hoặc cho con bú
  final bool isPregnantOrBreastfeeding;

  /// Mức độ ăn cay ưa thích (ví dụ: 'không cay')
  final String? spiceLevelPreference;

  /// Lưu chuỗi JSON mảng thực phẩm/nguyên liệu yêu thích
  final String? favoriteFoodsIngredients;

  /// Lưu chuỗi JSON mảng thực phẩm/nguyên liệu không thích
  final String? dislikedFoodsIngredients;

  /// Lưu chuỗi JSON mảng sở thích về kết cấu món ăn
  final String? texturePreferences;

  /// Quốc gia/Vùng miền sinh sống
  final String? countryRegionOfResidence;

  /// Lưu chuỗi JSON mảng các nền ẩm thực yêu thích
  final String? preferredCuisines;

  /// Lối sống ăn uống (ví dụ: 'vegan', 'keto')
  final String? eatingLifestyle;

  /// Lưu chuỗi JSON mảng các bữa ăn thường ngày
  final String? selectedMealTypes;

  /// Trình độ nấu ăn (ví dụ: 'mới bắt đầu')
  final String? cookingSkillLevel;

  /// Thời gian trung bình có thể dành cho việc nấu ăn
  final int? avgTimeForMainMealPrepCookMinutes;

  /// Lưu chuỗi JSON mảng thiết bị nhà bếp có sẵn
  final String? availableKitchenEquipment;
  const LocalUserProfile({
    required this.userId,
    required this.updatedAt,
    this.dateOfBirth,
    this.gender,
    this.weightKg,
    this.heightCm,
    this.activityLevel,
    this.healthGoals,
    this.foodAllergies,
    this.medicalConditions,
    required this.isPregnantOrBreastfeeding,
    this.spiceLevelPreference,
    this.favoriteFoodsIngredients,
    this.dislikedFoodsIngredients,
    this.texturePreferences,
    this.countryRegionOfResidence,
    this.preferredCuisines,
    this.eatingLifestyle,
    this.selectedMealTypes,
    this.cookingSkillLevel,
    this.avgTimeForMainMealPrepCookMinutes,
    this.availableKitchenEquipment,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['user_id'] = Variable<String>(userId);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    if (!nullToAbsent || dateOfBirth != null) {
      map['date_of_birth'] = Variable<DateTime>(dateOfBirth);
    }
    if (!nullToAbsent || gender != null) {
      map['gender'] = Variable<String>(gender);
    }
    if (!nullToAbsent || weightKg != null) {
      map['weight_kg'] = Variable<double>(weightKg);
    }
    if (!nullToAbsent || heightCm != null) {
      map['height_cm'] = Variable<double>(heightCm);
    }
    if (!nullToAbsent || activityLevel != null) {
      map['activity_level'] = Variable<String>(activityLevel);
    }
    if (!nullToAbsent || healthGoals != null) {
      map['health_goals'] = Variable<String>(healthGoals);
    }
    if (!nullToAbsent || foodAllergies != null) {
      map['food_allergies'] = Variable<String>(foodAllergies);
    }
    if (!nullToAbsent || medicalConditions != null) {
      map['medical_conditions'] = Variable<String>(medicalConditions);
    }
    map['is_pregnant_or_breastfeeding'] = Variable<bool>(
      isPregnantOrBreastfeeding,
    );
    if (!nullToAbsent || spiceLevelPreference != null) {
      map['spice_level_preference'] = Variable<String>(spiceLevelPreference);
    }
    if (!nullToAbsent || favoriteFoodsIngredients != null) {
      map['favorite_foods_ingredients'] = Variable<String>(
        favoriteFoodsIngredients,
      );
    }
    if (!nullToAbsent || dislikedFoodsIngredients != null) {
      map['disliked_foods_ingredients'] = Variable<String>(
        dislikedFoodsIngredients,
      );
    }
    if (!nullToAbsent || texturePreferences != null) {
      map['texture_preferences'] = Variable<String>(texturePreferences);
    }
    if (!nullToAbsent || countryRegionOfResidence != null) {
      map['country_region_of_residence'] = Variable<String>(
        countryRegionOfResidence,
      );
    }
    if (!nullToAbsent || preferredCuisines != null) {
      map['preferred_cuisines'] = Variable<String>(preferredCuisines);
    }
    if (!nullToAbsent || eatingLifestyle != null) {
      map['eating_lifestyle'] = Variable<String>(eatingLifestyle);
    }
    if (!nullToAbsent || selectedMealTypes != null) {
      map['selected_meal_types'] = Variable<String>(selectedMealTypes);
    }
    if (!nullToAbsent || cookingSkillLevel != null) {
      map['cooking_skill_level'] = Variable<String>(cookingSkillLevel);
    }
    if (!nullToAbsent || avgTimeForMainMealPrepCookMinutes != null) {
      map['avg_time_for_main_meal_prep_cook_minutes'] = Variable<int>(
        avgTimeForMainMealPrepCookMinutes,
      );
    }
    if (!nullToAbsent || availableKitchenEquipment != null) {
      map['available_kitchen_equipment'] = Variable<String>(
        availableKitchenEquipment,
      );
    }
    return map;
  }

  LocalUserProfilesCompanion toCompanion(bool nullToAbsent) {
    return LocalUserProfilesCompanion(
      userId: Value(userId),
      updatedAt: Value(updatedAt),
      dateOfBirth:
          dateOfBirth == null && nullToAbsent
              ? const Value.absent()
              : Value(dateOfBirth),
      gender:
          gender == null && nullToAbsent ? const Value.absent() : Value(gender),
      weightKg:
          weightKg == null && nullToAbsent
              ? const Value.absent()
              : Value(weightKg),
      heightCm:
          heightCm == null && nullToAbsent
              ? const Value.absent()
              : Value(heightCm),
      activityLevel:
          activityLevel == null && nullToAbsent
              ? const Value.absent()
              : Value(activityLevel),
      healthGoals:
          healthGoals == null && nullToAbsent
              ? const Value.absent()
              : Value(healthGoals),
      foodAllergies:
          foodAllergies == null && nullToAbsent
              ? const Value.absent()
              : Value(foodAllergies),
      medicalConditions:
          medicalConditions == null && nullToAbsent
              ? const Value.absent()
              : Value(medicalConditions),
      isPregnantOrBreastfeeding: Value(isPregnantOrBreastfeeding),
      spiceLevelPreference:
          spiceLevelPreference == null && nullToAbsent
              ? const Value.absent()
              : Value(spiceLevelPreference),
      favoriteFoodsIngredients:
          favoriteFoodsIngredients == null && nullToAbsent
              ? const Value.absent()
              : Value(favoriteFoodsIngredients),
      dislikedFoodsIngredients:
          dislikedFoodsIngredients == null && nullToAbsent
              ? const Value.absent()
              : Value(dislikedFoodsIngredients),
      texturePreferences:
          texturePreferences == null && nullToAbsent
              ? const Value.absent()
              : Value(texturePreferences),
      countryRegionOfResidence:
          countryRegionOfResidence == null && nullToAbsent
              ? const Value.absent()
              : Value(countryRegionOfResidence),
      preferredCuisines:
          preferredCuisines == null && nullToAbsent
              ? const Value.absent()
              : Value(preferredCuisines),
      eatingLifestyle:
          eatingLifestyle == null && nullToAbsent
              ? const Value.absent()
              : Value(eatingLifestyle),
      selectedMealTypes:
          selectedMealTypes == null && nullToAbsent
              ? const Value.absent()
              : Value(selectedMealTypes),
      cookingSkillLevel:
          cookingSkillLevel == null && nullToAbsent
              ? const Value.absent()
              : Value(cookingSkillLevel),
      avgTimeForMainMealPrepCookMinutes:
          avgTimeForMainMealPrepCookMinutes == null && nullToAbsent
              ? const Value.absent()
              : Value(avgTimeForMainMealPrepCookMinutes),
      availableKitchenEquipment:
          availableKitchenEquipment == null && nullToAbsent
              ? const Value.absent()
              : Value(availableKitchenEquipment),
    );
  }

  factory LocalUserProfile.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return LocalUserProfile(
      userId: serializer.fromJson<String>(json['userId']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
      dateOfBirth: serializer.fromJson<DateTime?>(json['dateOfBirth']),
      gender: serializer.fromJson<String?>(json['gender']),
      weightKg: serializer.fromJson<double?>(json['weightKg']),
      heightCm: serializer.fromJson<double?>(json['heightCm']),
      activityLevel: serializer.fromJson<String?>(json['activityLevel']),
      healthGoals: serializer.fromJson<String?>(json['healthGoals']),
      foodAllergies: serializer.fromJson<String?>(json['foodAllergies']),
      medicalConditions: serializer.fromJson<String?>(
        json['medicalConditions'],
      ),
      isPregnantOrBreastfeeding: serializer.fromJson<bool>(
        json['isPregnantOrBreastfeeding'],
      ),
      spiceLevelPreference: serializer.fromJson<String?>(
        json['spiceLevelPreference'],
      ),
      favoriteFoodsIngredients: serializer.fromJson<String?>(
        json['favoriteFoodsIngredients'],
      ),
      dislikedFoodsIngredients: serializer.fromJson<String?>(
        json['dislikedFoodsIngredients'],
      ),
      texturePreferences: serializer.fromJson<String?>(
        json['texturePreferences'],
      ),
      countryRegionOfResidence: serializer.fromJson<String?>(
        json['countryRegionOfResidence'],
      ),
      preferredCuisines: serializer.fromJson<String?>(
        json['preferredCuisines'],
      ),
      eatingLifestyle: serializer.fromJson<String?>(json['eatingLifestyle']),
      selectedMealTypes: serializer.fromJson<String?>(
        json['selectedMealTypes'],
      ),
      cookingSkillLevel: serializer.fromJson<String?>(
        json['cookingSkillLevel'],
      ),
      avgTimeForMainMealPrepCookMinutes: serializer.fromJson<int?>(
        json['avgTimeForMainMealPrepCookMinutes'],
      ),
      availableKitchenEquipment: serializer.fromJson<String?>(
        json['availableKitchenEquipment'],
      ),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'userId': serializer.toJson<String>(userId),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
      'dateOfBirth': serializer.toJson<DateTime?>(dateOfBirth),
      'gender': serializer.toJson<String?>(gender),
      'weightKg': serializer.toJson<double?>(weightKg),
      'heightCm': serializer.toJson<double?>(heightCm),
      'activityLevel': serializer.toJson<String?>(activityLevel),
      'healthGoals': serializer.toJson<String?>(healthGoals),
      'foodAllergies': serializer.toJson<String?>(foodAllergies),
      'medicalConditions': serializer.toJson<String?>(medicalConditions),
      'isPregnantOrBreastfeeding': serializer.toJson<bool>(
        isPregnantOrBreastfeeding,
      ),
      'spiceLevelPreference': serializer.toJson<String?>(spiceLevelPreference),
      'favoriteFoodsIngredients': serializer.toJson<String?>(
        favoriteFoodsIngredients,
      ),
      'dislikedFoodsIngredients': serializer.toJson<String?>(
        dislikedFoodsIngredients,
      ),
      'texturePreferences': serializer.toJson<String?>(texturePreferences),
      'countryRegionOfResidence': serializer.toJson<String?>(
        countryRegionOfResidence,
      ),
      'preferredCuisines': serializer.toJson<String?>(preferredCuisines),
      'eatingLifestyle': serializer.toJson<String?>(eatingLifestyle),
      'selectedMealTypes': serializer.toJson<String?>(selectedMealTypes),
      'cookingSkillLevel': serializer.toJson<String?>(cookingSkillLevel),
      'avgTimeForMainMealPrepCookMinutes': serializer.toJson<int?>(
        avgTimeForMainMealPrepCookMinutes,
      ),
      'availableKitchenEquipment': serializer.toJson<String?>(
        availableKitchenEquipment,
      ),
    };
  }

  LocalUserProfile copyWith({
    String? userId,
    DateTime? updatedAt,
    Value<DateTime?> dateOfBirth = const Value.absent(),
    Value<String?> gender = const Value.absent(),
    Value<double?> weightKg = const Value.absent(),
    Value<double?> heightCm = const Value.absent(),
    Value<String?> activityLevel = const Value.absent(),
    Value<String?> healthGoals = const Value.absent(),
    Value<String?> foodAllergies = const Value.absent(),
    Value<String?> medicalConditions = const Value.absent(),
    bool? isPregnantOrBreastfeeding,
    Value<String?> spiceLevelPreference = const Value.absent(),
    Value<String?> favoriteFoodsIngredients = const Value.absent(),
    Value<String?> dislikedFoodsIngredients = const Value.absent(),
    Value<String?> texturePreferences = const Value.absent(),
    Value<String?> countryRegionOfResidence = const Value.absent(),
    Value<String?> preferredCuisines = const Value.absent(),
    Value<String?> eatingLifestyle = const Value.absent(),
    Value<String?> selectedMealTypes = const Value.absent(),
    Value<String?> cookingSkillLevel = const Value.absent(),
    Value<int?> avgTimeForMainMealPrepCookMinutes = const Value.absent(),
    Value<String?> availableKitchenEquipment = const Value.absent(),
  }) => LocalUserProfile(
    userId: userId ?? this.userId,
    updatedAt: updatedAt ?? this.updatedAt,
    dateOfBirth: dateOfBirth.present ? dateOfBirth.value : this.dateOfBirth,
    gender: gender.present ? gender.value : this.gender,
    weightKg: weightKg.present ? weightKg.value : this.weightKg,
    heightCm: heightCm.present ? heightCm.value : this.heightCm,
    activityLevel:
        activityLevel.present ? activityLevel.value : this.activityLevel,
    healthGoals: healthGoals.present ? healthGoals.value : this.healthGoals,
    foodAllergies:
        foodAllergies.present ? foodAllergies.value : this.foodAllergies,
    medicalConditions:
        medicalConditions.present
            ? medicalConditions.value
            : this.medicalConditions,
    isPregnantOrBreastfeeding:
        isPregnantOrBreastfeeding ?? this.isPregnantOrBreastfeeding,
    spiceLevelPreference:
        spiceLevelPreference.present
            ? spiceLevelPreference.value
            : this.spiceLevelPreference,
    favoriteFoodsIngredients:
        favoriteFoodsIngredients.present
            ? favoriteFoodsIngredients.value
            : this.favoriteFoodsIngredients,
    dislikedFoodsIngredients:
        dislikedFoodsIngredients.present
            ? dislikedFoodsIngredients.value
            : this.dislikedFoodsIngredients,
    texturePreferences:
        texturePreferences.present
            ? texturePreferences.value
            : this.texturePreferences,
    countryRegionOfResidence:
        countryRegionOfResidence.present
            ? countryRegionOfResidence.value
            : this.countryRegionOfResidence,
    preferredCuisines:
        preferredCuisines.present
            ? preferredCuisines.value
            : this.preferredCuisines,
    eatingLifestyle:
        eatingLifestyle.present ? eatingLifestyle.value : this.eatingLifestyle,
    selectedMealTypes:
        selectedMealTypes.present
            ? selectedMealTypes.value
            : this.selectedMealTypes,
    cookingSkillLevel:
        cookingSkillLevel.present
            ? cookingSkillLevel.value
            : this.cookingSkillLevel,
    avgTimeForMainMealPrepCookMinutes:
        avgTimeForMainMealPrepCookMinutes.present
            ? avgTimeForMainMealPrepCookMinutes.value
            : this.avgTimeForMainMealPrepCookMinutes,
    availableKitchenEquipment:
        availableKitchenEquipment.present
            ? availableKitchenEquipment.value
            : this.availableKitchenEquipment,
  );
  LocalUserProfile copyWithCompanion(LocalUserProfilesCompanion data) {
    return LocalUserProfile(
      userId: data.userId.present ? data.userId.value : this.userId,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
      dateOfBirth:
          data.dateOfBirth.present ? data.dateOfBirth.value : this.dateOfBirth,
      gender: data.gender.present ? data.gender.value : this.gender,
      weightKg: data.weightKg.present ? data.weightKg.value : this.weightKg,
      heightCm: data.heightCm.present ? data.heightCm.value : this.heightCm,
      activityLevel:
          data.activityLevel.present
              ? data.activityLevel.value
              : this.activityLevel,
      healthGoals:
          data.healthGoals.present ? data.healthGoals.value : this.healthGoals,
      foodAllergies:
          data.foodAllergies.present
              ? data.foodAllergies.value
              : this.foodAllergies,
      medicalConditions:
          data.medicalConditions.present
              ? data.medicalConditions.value
              : this.medicalConditions,
      isPregnantOrBreastfeeding:
          data.isPregnantOrBreastfeeding.present
              ? data.isPregnantOrBreastfeeding.value
              : this.isPregnantOrBreastfeeding,
      spiceLevelPreference:
          data.spiceLevelPreference.present
              ? data.spiceLevelPreference.value
              : this.spiceLevelPreference,
      favoriteFoodsIngredients:
          data.favoriteFoodsIngredients.present
              ? data.favoriteFoodsIngredients.value
              : this.favoriteFoodsIngredients,
      dislikedFoodsIngredients:
          data.dislikedFoodsIngredients.present
              ? data.dislikedFoodsIngredients.value
              : this.dislikedFoodsIngredients,
      texturePreferences:
          data.texturePreferences.present
              ? data.texturePreferences.value
              : this.texturePreferences,
      countryRegionOfResidence:
          data.countryRegionOfResidence.present
              ? data.countryRegionOfResidence.value
              : this.countryRegionOfResidence,
      preferredCuisines:
          data.preferredCuisines.present
              ? data.preferredCuisines.value
              : this.preferredCuisines,
      eatingLifestyle:
          data.eatingLifestyle.present
              ? data.eatingLifestyle.value
              : this.eatingLifestyle,
      selectedMealTypes:
          data.selectedMealTypes.present
              ? data.selectedMealTypes.value
              : this.selectedMealTypes,
      cookingSkillLevel:
          data.cookingSkillLevel.present
              ? data.cookingSkillLevel.value
              : this.cookingSkillLevel,
      avgTimeForMainMealPrepCookMinutes:
          data.avgTimeForMainMealPrepCookMinutes.present
              ? data.avgTimeForMainMealPrepCookMinutes.value
              : this.avgTimeForMainMealPrepCookMinutes,
      availableKitchenEquipment:
          data.availableKitchenEquipment.present
              ? data.availableKitchenEquipment.value
              : this.availableKitchenEquipment,
    );
  }

  @override
  String toString() {
    return (StringBuffer('LocalUserProfile(')
          ..write('userId: $userId, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('dateOfBirth: $dateOfBirth, ')
          ..write('gender: $gender, ')
          ..write('weightKg: $weightKg, ')
          ..write('heightCm: $heightCm, ')
          ..write('activityLevel: $activityLevel, ')
          ..write('healthGoals: $healthGoals, ')
          ..write('foodAllergies: $foodAllergies, ')
          ..write('medicalConditions: $medicalConditions, ')
          ..write('isPregnantOrBreastfeeding: $isPregnantOrBreastfeeding, ')
          ..write('spiceLevelPreference: $spiceLevelPreference, ')
          ..write('favoriteFoodsIngredients: $favoriteFoodsIngredients, ')
          ..write('dislikedFoodsIngredients: $dislikedFoodsIngredients, ')
          ..write('texturePreferences: $texturePreferences, ')
          ..write('countryRegionOfResidence: $countryRegionOfResidence, ')
          ..write('preferredCuisines: $preferredCuisines, ')
          ..write('eatingLifestyle: $eatingLifestyle, ')
          ..write('selectedMealTypes: $selectedMealTypes, ')
          ..write('cookingSkillLevel: $cookingSkillLevel, ')
          ..write(
            'avgTimeForMainMealPrepCookMinutes: $avgTimeForMainMealPrepCookMinutes, ',
          )
          ..write('availableKitchenEquipment: $availableKitchenEquipment')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hashAll([
    userId,
    updatedAt,
    dateOfBirth,
    gender,
    weightKg,
    heightCm,
    activityLevel,
    healthGoals,
    foodAllergies,
    medicalConditions,
    isPregnantOrBreastfeeding,
    spiceLevelPreference,
    favoriteFoodsIngredients,
    dislikedFoodsIngredients,
    texturePreferences,
    countryRegionOfResidence,
    preferredCuisines,
    eatingLifestyle,
    selectedMealTypes,
    cookingSkillLevel,
    avgTimeForMainMealPrepCookMinutes,
    availableKitchenEquipment,
  ]);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is LocalUserProfile &&
          other.userId == this.userId &&
          other.updatedAt == this.updatedAt &&
          other.dateOfBirth == this.dateOfBirth &&
          other.gender == this.gender &&
          other.weightKg == this.weightKg &&
          other.heightCm == this.heightCm &&
          other.activityLevel == this.activityLevel &&
          other.healthGoals == this.healthGoals &&
          other.foodAllergies == this.foodAllergies &&
          other.medicalConditions == this.medicalConditions &&
          other.isPregnantOrBreastfeeding == this.isPregnantOrBreastfeeding &&
          other.spiceLevelPreference == this.spiceLevelPreference &&
          other.favoriteFoodsIngredients == this.favoriteFoodsIngredients &&
          other.dislikedFoodsIngredients == this.dislikedFoodsIngredients &&
          other.texturePreferences == this.texturePreferences &&
          other.countryRegionOfResidence == this.countryRegionOfResidence &&
          other.preferredCuisines == this.preferredCuisines &&
          other.eatingLifestyle == this.eatingLifestyle &&
          other.selectedMealTypes == this.selectedMealTypes &&
          other.cookingSkillLevel == this.cookingSkillLevel &&
          other.avgTimeForMainMealPrepCookMinutes ==
              this.avgTimeForMainMealPrepCookMinutes &&
          other.availableKitchenEquipment == this.availableKitchenEquipment);
}

class LocalUserProfilesCompanion extends UpdateCompanion<LocalUserProfile> {
  final Value<String> userId;
  final Value<DateTime> updatedAt;
  final Value<DateTime?> dateOfBirth;
  final Value<String?> gender;
  final Value<double?> weightKg;
  final Value<double?> heightCm;
  final Value<String?> activityLevel;
  final Value<String?> healthGoals;
  final Value<String?> foodAllergies;
  final Value<String?> medicalConditions;
  final Value<bool> isPregnantOrBreastfeeding;
  final Value<String?> spiceLevelPreference;
  final Value<String?> favoriteFoodsIngredients;
  final Value<String?> dislikedFoodsIngredients;
  final Value<String?> texturePreferences;
  final Value<String?> countryRegionOfResidence;
  final Value<String?> preferredCuisines;
  final Value<String?> eatingLifestyle;
  final Value<String?> selectedMealTypes;
  final Value<String?> cookingSkillLevel;
  final Value<int?> avgTimeForMainMealPrepCookMinutes;
  final Value<String?> availableKitchenEquipment;
  final Value<int> rowid;
  const LocalUserProfilesCompanion({
    this.userId = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.dateOfBirth = const Value.absent(),
    this.gender = const Value.absent(),
    this.weightKg = const Value.absent(),
    this.heightCm = const Value.absent(),
    this.activityLevel = const Value.absent(),
    this.healthGoals = const Value.absent(),
    this.foodAllergies = const Value.absent(),
    this.medicalConditions = const Value.absent(),
    this.isPregnantOrBreastfeeding = const Value.absent(),
    this.spiceLevelPreference = const Value.absent(),
    this.favoriteFoodsIngredients = const Value.absent(),
    this.dislikedFoodsIngredients = const Value.absent(),
    this.texturePreferences = const Value.absent(),
    this.countryRegionOfResidence = const Value.absent(),
    this.preferredCuisines = const Value.absent(),
    this.eatingLifestyle = const Value.absent(),
    this.selectedMealTypes = const Value.absent(),
    this.cookingSkillLevel = const Value.absent(),
    this.avgTimeForMainMealPrepCookMinutes = const Value.absent(),
    this.availableKitchenEquipment = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  LocalUserProfilesCompanion.insert({
    required String userId,
    required DateTime updatedAt,
    this.dateOfBirth = const Value.absent(),
    this.gender = const Value.absent(),
    this.weightKg = const Value.absent(),
    this.heightCm = const Value.absent(),
    this.activityLevel = const Value.absent(),
    this.healthGoals = const Value.absent(),
    this.foodAllergies = const Value.absent(),
    this.medicalConditions = const Value.absent(),
    this.isPregnantOrBreastfeeding = const Value.absent(),
    this.spiceLevelPreference = const Value.absent(),
    this.favoriteFoodsIngredients = const Value.absent(),
    this.dislikedFoodsIngredients = const Value.absent(),
    this.texturePreferences = const Value.absent(),
    this.countryRegionOfResidence = const Value.absent(),
    this.preferredCuisines = const Value.absent(),
    this.eatingLifestyle = const Value.absent(),
    this.selectedMealTypes = const Value.absent(),
    this.cookingSkillLevel = const Value.absent(),
    this.avgTimeForMainMealPrepCookMinutes = const Value.absent(),
    this.availableKitchenEquipment = const Value.absent(),
    this.rowid = const Value.absent(),
  }) : userId = Value(userId),
       updatedAt = Value(updatedAt);
  static Insertable<LocalUserProfile> custom({
    Expression<String>? userId,
    Expression<DateTime>? updatedAt,
    Expression<DateTime>? dateOfBirth,
    Expression<String>? gender,
    Expression<double>? weightKg,
    Expression<double>? heightCm,
    Expression<String>? activityLevel,
    Expression<String>? healthGoals,
    Expression<String>? foodAllergies,
    Expression<String>? medicalConditions,
    Expression<bool>? isPregnantOrBreastfeeding,
    Expression<String>? spiceLevelPreference,
    Expression<String>? favoriteFoodsIngredients,
    Expression<String>? dislikedFoodsIngredients,
    Expression<String>? texturePreferences,
    Expression<String>? countryRegionOfResidence,
    Expression<String>? preferredCuisines,
    Expression<String>? eatingLifestyle,
    Expression<String>? selectedMealTypes,
    Expression<String>? cookingSkillLevel,
    Expression<int>? avgTimeForMainMealPrepCookMinutes,
    Expression<String>? availableKitchenEquipment,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (userId != null) 'user_id': userId,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (dateOfBirth != null) 'date_of_birth': dateOfBirth,
      if (gender != null) 'gender': gender,
      if (weightKg != null) 'weight_kg': weightKg,
      if (heightCm != null) 'height_cm': heightCm,
      if (activityLevel != null) 'activity_level': activityLevel,
      if (healthGoals != null) 'health_goals': healthGoals,
      if (foodAllergies != null) 'food_allergies': foodAllergies,
      if (medicalConditions != null) 'medical_conditions': medicalConditions,
      if (isPregnantOrBreastfeeding != null)
        'is_pregnant_or_breastfeeding': isPregnantOrBreastfeeding,
      if (spiceLevelPreference != null)
        'spice_level_preference': spiceLevelPreference,
      if (favoriteFoodsIngredients != null)
        'favorite_foods_ingredients': favoriteFoodsIngredients,
      if (dislikedFoodsIngredients != null)
        'disliked_foods_ingredients': dislikedFoodsIngredients,
      if (texturePreferences != null) 'texture_preferences': texturePreferences,
      if (countryRegionOfResidence != null)
        'country_region_of_residence': countryRegionOfResidence,
      if (preferredCuisines != null) 'preferred_cuisines': preferredCuisines,
      if (eatingLifestyle != null) 'eating_lifestyle': eatingLifestyle,
      if (selectedMealTypes != null) 'selected_meal_types': selectedMealTypes,
      if (cookingSkillLevel != null) 'cooking_skill_level': cookingSkillLevel,
      if (avgTimeForMainMealPrepCookMinutes != null)
        'avg_time_for_main_meal_prep_cook_minutes':
            avgTimeForMainMealPrepCookMinutes,
      if (availableKitchenEquipment != null)
        'available_kitchen_equipment': availableKitchenEquipment,
      if (rowid != null) 'rowid': rowid,
    });
  }

  LocalUserProfilesCompanion copyWith({
    Value<String>? userId,
    Value<DateTime>? updatedAt,
    Value<DateTime?>? dateOfBirth,
    Value<String?>? gender,
    Value<double?>? weightKg,
    Value<double?>? heightCm,
    Value<String?>? activityLevel,
    Value<String?>? healthGoals,
    Value<String?>? foodAllergies,
    Value<String?>? medicalConditions,
    Value<bool>? isPregnantOrBreastfeeding,
    Value<String?>? spiceLevelPreference,
    Value<String?>? favoriteFoodsIngredients,
    Value<String?>? dislikedFoodsIngredients,
    Value<String?>? texturePreferences,
    Value<String?>? countryRegionOfResidence,
    Value<String?>? preferredCuisines,
    Value<String?>? eatingLifestyle,
    Value<String?>? selectedMealTypes,
    Value<String?>? cookingSkillLevel,
    Value<int?>? avgTimeForMainMealPrepCookMinutes,
    Value<String?>? availableKitchenEquipment,
    Value<int>? rowid,
  }) {
    return LocalUserProfilesCompanion(
      userId: userId ?? this.userId,
      updatedAt: updatedAt ?? this.updatedAt,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      weightKg: weightKg ?? this.weightKg,
      heightCm: heightCm ?? this.heightCm,
      activityLevel: activityLevel ?? this.activityLevel,
      healthGoals: healthGoals ?? this.healthGoals,
      foodAllergies: foodAllergies ?? this.foodAllergies,
      medicalConditions: medicalConditions ?? this.medicalConditions,
      isPregnantOrBreastfeeding:
          isPregnantOrBreastfeeding ?? this.isPregnantOrBreastfeeding,
      spiceLevelPreference: spiceLevelPreference ?? this.spiceLevelPreference,
      favoriteFoodsIngredients:
          favoriteFoodsIngredients ?? this.favoriteFoodsIngredients,
      dislikedFoodsIngredients:
          dislikedFoodsIngredients ?? this.dislikedFoodsIngredients,
      texturePreferences: texturePreferences ?? this.texturePreferences,
      countryRegionOfResidence:
          countryRegionOfResidence ?? this.countryRegionOfResidence,
      preferredCuisines: preferredCuisines ?? this.preferredCuisines,
      eatingLifestyle: eatingLifestyle ?? this.eatingLifestyle,
      selectedMealTypes: selectedMealTypes ?? this.selectedMealTypes,
      cookingSkillLevel: cookingSkillLevel ?? this.cookingSkillLevel,
      avgTimeForMainMealPrepCookMinutes:
          avgTimeForMainMealPrepCookMinutes ??
          this.avgTimeForMainMealPrepCookMinutes,
      availableKitchenEquipment:
          availableKitchenEquipment ?? this.availableKitchenEquipment,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (userId.present) {
      map['user_id'] = Variable<String>(userId.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (dateOfBirth.present) {
      map['date_of_birth'] = Variable<DateTime>(dateOfBirth.value);
    }
    if (gender.present) {
      map['gender'] = Variable<String>(gender.value);
    }
    if (weightKg.present) {
      map['weight_kg'] = Variable<double>(weightKg.value);
    }
    if (heightCm.present) {
      map['height_cm'] = Variable<double>(heightCm.value);
    }
    if (activityLevel.present) {
      map['activity_level'] = Variable<String>(activityLevel.value);
    }
    if (healthGoals.present) {
      map['health_goals'] = Variable<String>(healthGoals.value);
    }
    if (foodAllergies.present) {
      map['food_allergies'] = Variable<String>(foodAllergies.value);
    }
    if (medicalConditions.present) {
      map['medical_conditions'] = Variable<String>(medicalConditions.value);
    }
    if (isPregnantOrBreastfeeding.present) {
      map['is_pregnant_or_breastfeeding'] = Variable<bool>(
        isPregnantOrBreastfeeding.value,
      );
    }
    if (spiceLevelPreference.present) {
      map['spice_level_preference'] = Variable<String>(
        spiceLevelPreference.value,
      );
    }
    if (favoriteFoodsIngredients.present) {
      map['favorite_foods_ingredients'] = Variable<String>(
        favoriteFoodsIngredients.value,
      );
    }
    if (dislikedFoodsIngredients.present) {
      map['disliked_foods_ingredients'] = Variable<String>(
        dislikedFoodsIngredients.value,
      );
    }
    if (texturePreferences.present) {
      map['texture_preferences'] = Variable<String>(texturePreferences.value);
    }
    if (countryRegionOfResidence.present) {
      map['country_region_of_residence'] = Variable<String>(
        countryRegionOfResidence.value,
      );
    }
    if (preferredCuisines.present) {
      map['preferred_cuisines'] = Variable<String>(preferredCuisines.value);
    }
    if (eatingLifestyle.present) {
      map['eating_lifestyle'] = Variable<String>(eatingLifestyle.value);
    }
    if (selectedMealTypes.present) {
      map['selected_meal_types'] = Variable<String>(selectedMealTypes.value);
    }
    if (cookingSkillLevel.present) {
      map['cooking_skill_level'] = Variable<String>(cookingSkillLevel.value);
    }
    if (avgTimeForMainMealPrepCookMinutes.present) {
      map['avg_time_for_main_meal_prep_cook_minutes'] = Variable<int>(
        avgTimeForMainMealPrepCookMinutes.value,
      );
    }
    if (availableKitchenEquipment.present) {
      map['available_kitchen_equipment'] = Variable<String>(
        availableKitchenEquipment.value,
      );
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('LocalUserProfilesCompanion(')
          ..write('userId: $userId, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('dateOfBirth: $dateOfBirth, ')
          ..write('gender: $gender, ')
          ..write('weightKg: $weightKg, ')
          ..write('heightCm: $heightCm, ')
          ..write('activityLevel: $activityLevel, ')
          ..write('healthGoals: $healthGoals, ')
          ..write('foodAllergies: $foodAllergies, ')
          ..write('medicalConditions: $medicalConditions, ')
          ..write('isPregnantOrBreastfeeding: $isPregnantOrBreastfeeding, ')
          ..write('spiceLevelPreference: $spiceLevelPreference, ')
          ..write('favoriteFoodsIngredients: $favoriteFoodsIngredients, ')
          ..write('dislikedFoodsIngredients: $dislikedFoodsIngredients, ')
          ..write('texturePreferences: $texturePreferences, ')
          ..write('countryRegionOfResidence: $countryRegionOfResidence, ')
          ..write('preferredCuisines: $preferredCuisines, ')
          ..write('eatingLifestyle: $eatingLifestyle, ')
          ..write('selectedMealTypes: $selectedMealTypes, ')
          ..write('cookingSkillLevel: $cookingSkillLevel, ')
          ..write(
            'avgTimeForMainMealPrepCookMinutes: $avgTimeForMainMealPrepCookMinutes, ',
          )
          ..write('availableKitchenEquipment: $availableKitchenEquipment, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $LocalRecipesTable extends LocalRecipes
    with TableInfo<$LocalRecipesTable, LocalRecipe> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $LocalRecipesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
    'id',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
    'name',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _descriptionMeta = const VerificationMeta(
    'description',
  );
  @override
  late final GeneratedColumn<String> description = GeneratedColumn<String>(
    'description',
    aliasedName,
    true,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _instructionsMeta = const VerificationMeta(
    'instructions',
  );
  @override
  late final GeneratedColumn<String> instructions = GeneratedColumn<String>(
    'instructions',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _imageUrlMeta = const VerificationMeta(
    'imageUrl',
  );
  @override
  late final GeneratedColumn<String> imageUrl = GeneratedColumn<String>(
    'image_url',
    aliasedName,
    true,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _sourceMeta = const VerificationMeta('source');
  @override
  late final GeneratedColumn<String> source = GeneratedColumn<String>(
    'source',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _isFavoriteMeta = const VerificationMeta(
    'isFavorite',
  );
  @override
  late final GeneratedColumn<bool> isFavorite = GeneratedColumn<bool>(
    'is_favorite',
    aliasedName,
    false,
    type: DriftSqlType.bool,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'CHECK ("is_favorite" IN (0, 1))',
    ),
    defaultValue: const Constant(false),
  );
  static const VerificationMeta _prepTimeMinutesMeta = const VerificationMeta(
    'prepTimeMinutes',
  );
  @override
  late final GeneratedColumn<int> prepTimeMinutes = GeneratedColumn<int>(
    'prep_time_minutes',
    aliasedName,
    true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _cookTimeMinutesMeta = const VerificationMeta(
    'cookTimeMinutes',
  );
  @override
  late final GeneratedColumn<int> cookTimeMinutes = GeneratedColumn<int>(
    'cook_time_minutes',
    aliasedName,
    true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _servingsMeta = const VerificationMeta(
    'servings',
  );
  @override
  late final GeneratedColumn<int> servings = GeneratedColumn<int>(
    'servings',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _caloriesPerServingMeta =
      const VerificationMeta('caloriesPerServing');
  @override
  late final GeneratedColumn<int> caloriesPerServing = GeneratedColumn<int>(
    'calories_per_serving',
    aliasedName,
    true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _proteinGPerServingMeta =
      const VerificationMeta('proteinGPerServing');
  @override
  late final GeneratedColumn<double> proteinGPerServing =
      GeneratedColumn<double>(
        'protein_g_per_serving',
        aliasedName,
        true,
        type: DriftSqlType.double,
        requiredDuringInsert: false,
      );
  static const VerificationMeta _carbsGPerServingMeta = const VerificationMeta(
    'carbsGPerServing',
  );
  @override
  late final GeneratedColumn<double> carbsGPerServing = GeneratedColumn<double>(
    'carbs_g_per_serving',
    aliasedName,
    true,
    type: DriftSqlType.double,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _fatGPerServingMeta = const VerificationMeta(
    'fatGPerServing',
  );
  @override
  late final GeneratedColumn<double> fatGPerServing = GeneratedColumn<double>(
    'fat_g_per_serving',
    aliasedName,
    true,
    type: DriftSqlType.double,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _totalDishNutrientSummaryMeta =
      const VerificationMeta('totalDishNutrientSummary');
  @override
  late final GeneratedColumn<String> totalDishNutrientSummary =
      GeneratedColumn<String>(
        'total_dish_nutrient_summary',
        aliasedName,
        true,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
      );
  static const VerificationMeta _dishEffectsAndNotesMeta =
      const VerificationMeta('dishEffectsAndNotes');
  @override
  late final GeneratedColumn<String> dishEffectsAndNotes =
      GeneratedColumn<String>(
        'dish_effects_and_notes',
        aliasedName,
        true,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
      );
  static const VerificationMeta _totalDishHeartHealthIndicatorsJsonMeta =
      const VerificationMeta('totalDishHeartHealthIndicatorsJson');
  @override
  late final GeneratedColumn<String> totalDishHeartHealthIndicatorsJson =
      GeneratedColumn<String>(
        'total_dish_heart_health_indicators_json',
        aliasedName,
        true,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
      );
  static const VerificationMeta _totalDishVitaminsJsonMeta =
      const VerificationMeta('totalDishVitaminsJson');
  @override
  late final GeneratedColumn<String> totalDishVitaminsJson =
      GeneratedColumn<String>(
        'total_dish_vitamins_json',
        aliasedName,
        true,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
      );
  static const VerificationMeta _totalDishMineralsJsonMeta =
      const VerificationMeta('totalDishMineralsJson');
  @override
  late final GeneratedColumn<String> totalDishMineralsJson =
      GeneratedColumn<String>(
        'total_dish_minerals_json',
        aliasedName,
        true,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
      );
  @override
  List<GeneratedColumn> get $columns => [
    id,
    name,
    description,
    instructions,
    imageUrl,
    source,
    isFavorite,
    prepTimeMinutes,
    cookTimeMinutes,
    servings,
    caloriesPerServing,
    proteinGPerServing,
    carbsGPerServing,
    fatGPerServing,
    totalDishNutrientSummary,
    dishEffectsAndNotes,
    totalDishHeartHealthIndicatorsJson,
    totalDishVitaminsJson,
    totalDishMineralsJson,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'local_recipes';
  @override
  VerificationContext validateIntegrity(
    Insertable<LocalRecipe> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('name')) {
      context.handle(
        _nameMeta,
        name.isAcceptableOrUnknown(data['name']!, _nameMeta),
      );
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('description')) {
      context.handle(
        _descriptionMeta,
        description.isAcceptableOrUnknown(
          data['description']!,
          _descriptionMeta,
        ),
      );
    }
    if (data.containsKey('instructions')) {
      context.handle(
        _instructionsMeta,
        instructions.isAcceptableOrUnknown(
          data['instructions']!,
          _instructionsMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_instructionsMeta);
    }
    if (data.containsKey('image_url')) {
      context.handle(
        _imageUrlMeta,
        imageUrl.isAcceptableOrUnknown(data['image_url']!, _imageUrlMeta),
      );
    }
    if (data.containsKey('source')) {
      context.handle(
        _sourceMeta,
        source.isAcceptableOrUnknown(data['source']!, _sourceMeta),
      );
    } else if (isInserting) {
      context.missing(_sourceMeta);
    }
    if (data.containsKey('is_favorite')) {
      context.handle(
        _isFavoriteMeta,
        isFavorite.isAcceptableOrUnknown(data['is_favorite']!, _isFavoriteMeta),
      );
    }
    if (data.containsKey('prep_time_minutes')) {
      context.handle(
        _prepTimeMinutesMeta,
        prepTimeMinutes.isAcceptableOrUnknown(
          data['prep_time_minutes']!,
          _prepTimeMinutesMeta,
        ),
      );
    }
    if (data.containsKey('cook_time_minutes')) {
      context.handle(
        _cookTimeMinutesMeta,
        cookTimeMinutes.isAcceptableOrUnknown(
          data['cook_time_minutes']!,
          _cookTimeMinutesMeta,
        ),
      );
    }
    if (data.containsKey('servings')) {
      context.handle(
        _servingsMeta,
        servings.isAcceptableOrUnknown(data['servings']!, _servingsMeta),
      );
    } else if (isInserting) {
      context.missing(_servingsMeta);
    }
    if (data.containsKey('calories_per_serving')) {
      context.handle(
        _caloriesPerServingMeta,
        caloriesPerServing.isAcceptableOrUnknown(
          data['calories_per_serving']!,
          _caloriesPerServingMeta,
        ),
      );
    }
    if (data.containsKey('protein_g_per_serving')) {
      context.handle(
        _proteinGPerServingMeta,
        proteinGPerServing.isAcceptableOrUnknown(
          data['protein_g_per_serving']!,
          _proteinGPerServingMeta,
        ),
      );
    }
    if (data.containsKey('carbs_g_per_serving')) {
      context.handle(
        _carbsGPerServingMeta,
        carbsGPerServing.isAcceptableOrUnknown(
          data['carbs_g_per_serving']!,
          _carbsGPerServingMeta,
        ),
      );
    }
    if (data.containsKey('fat_g_per_serving')) {
      context.handle(
        _fatGPerServingMeta,
        fatGPerServing.isAcceptableOrUnknown(
          data['fat_g_per_serving']!,
          _fatGPerServingMeta,
        ),
      );
    }
    if (data.containsKey('total_dish_nutrient_summary')) {
      context.handle(
        _totalDishNutrientSummaryMeta,
        totalDishNutrientSummary.isAcceptableOrUnknown(
          data['total_dish_nutrient_summary']!,
          _totalDishNutrientSummaryMeta,
        ),
      );
    }
    if (data.containsKey('dish_effects_and_notes')) {
      context.handle(
        _dishEffectsAndNotesMeta,
        dishEffectsAndNotes.isAcceptableOrUnknown(
          data['dish_effects_and_notes']!,
          _dishEffectsAndNotesMeta,
        ),
      );
    }
    if (data.containsKey('total_dish_heart_health_indicators_json')) {
      context.handle(
        _totalDishHeartHealthIndicatorsJsonMeta,
        totalDishHeartHealthIndicatorsJson.isAcceptableOrUnknown(
          data['total_dish_heart_health_indicators_json']!,
          _totalDishHeartHealthIndicatorsJsonMeta,
        ),
      );
    }
    if (data.containsKey('total_dish_vitamins_json')) {
      context.handle(
        _totalDishVitaminsJsonMeta,
        totalDishVitaminsJson.isAcceptableOrUnknown(
          data['total_dish_vitamins_json']!,
          _totalDishVitaminsJsonMeta,
        ),
      );
    }
    if (data.containsKey('total_dish_minerals_json')) {
      context.handle(
        _totalDishMineralsJsonMeta,
        totalDishMineralsJson.isAcceptableOrUnknown(
          data['total_dish_minerals_json']!,
          _totalDishMineralsJsonMeta,
        ),
      );
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  LocalRecipe map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return LocalRecipe(
      id:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}id'],
          )!,
      name:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}name'],
          )!,
      description: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}description'],
      ),
      instructions:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}instructions'],
          )!,
      imageUrl: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}image_url'],
      ),
      source:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}source'],
          )!,
      isFavorite:
          attachedDatabase.typeMapping.read(
            DriftSqlType.bool,
            data['${effectivePrefix}is_favorite'],
          )!,
      prepTimeMinutes: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}prep_time_minutes'],
      ),
      cookTimeMinutes: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}cook_time_minutes'],
      ),
      servings:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}servings'],
          )!,
      caloriesPerServing: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}calories_per_serving'],
      ),
      proteinGPerServing: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}protein_g_per_serving'],
      ),
      carbsGPerServing: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}carbs_g_per_serving'],
      ),
      fatGPerServing: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}fat_g_per_serving'],
      ),
      totalDishNutrientSummary: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}total_dish_nutrient_summary'],
      ),
      dishEffectsAndNotes: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}dish_effects_and_notes'],
      ),
      totalDishHeartHealthIndicatorsJson: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}total_dish_heart_health_indicators_json'],
      ),
      totalDishVitaminsJson: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}total_dish_vitamins_json'],
      ),
      totalDishMineralsJson: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}total_dish_minerals_json'],
      ),
    );
  }

  @override
  $LocalRecipesTable createAlias(String alias) {
    return $LocalRecipesTable(attachedDatabase, alias);
  }
}

class LocalRecipe extends DataClass implements Insertable<LocalRecipe> {
  /// Khóa chính. Chính là recipe_id từ server
  final String id;

  /// Tên công thức
  final String name;

  /// Mô tả ngắn về công thức (có thể null)
  final String? description;

  /// Hướng dẫn nấu ăn chi tiết
  final String instructions;

  /// URL hình ảnh minh họa (có thể null)
  final String? imageUrl;

  /// Nguồn gốc công thức (ví dụ: 'ai_gemini')
  final String source;

  /// Đánh dấu công thức yêu thích
  final bool isFavorite;

  /// Thời gian chuẩn bị (phút, có thể null)
  final int? prepTimeMinutes;

  /// Thời gian nấu (phút, có thể null)
  final int? cookTimeMinutes;

  /// Số khẩu phần ăn dự kiến của công thức
  final int servings;

  /// Lượng calo ước tính (có thể null)
  final int? caloriesPerServing;

  /// Lượng protein (g) ước tính (có thể null)
  final double? proteinGPerServing;

  /// Lượng carb (g) ước tính (có thể null)
  final double? carbsGPerServing;

  /// Lượng chất béo (g) ước tính (có thể null)
  final double? fatGPerServing;

  /// Tóm tắt/đánh giá dinh dưỡng tổng thể (có thể null)
  final String? totalDishNutrientSummary;

  /// Tác dụng của món ăn, các lưu ý đặc biệt (có thể null)
  final String? dishEffectsAndNotes;

  /// Lưu chuỗi JSON các chỉ số sức khỏe tim mạch
  final String? totalDishHeartHealthIndicatorsJson;

  /// Lưu chuỗi JSON thông tin vitamin
  final String? totalDishVitaminsJson;

  /// Lưu chuỗi JSON thông tin khoáng chất
  final String? totalDishMineralsJson;
  const LocalRecipe({
    required this.id,
    required this.name,
    this.description,
    required this.instructions,
    this.imageUrl,
    required this.source,
    required this.isFavorite,
    this.prepTimeMinutes,
    this.cookTimeMinutes,
    required this.servings,
    this.caloriesPerServing,
    this.proteinGPerServing,
    this.carbsGPerServing,
    this.fatGPerServing,
    this.totalDishNutrientSummary,
    this.dishEffectsAndNotes,
    this.totalDishHeartHealthIndicatorsJson,
    this.totalDishVitaminsJson,
    this.totalDishMineralsJson,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['name'] = Variable<String>(name);
    if (!nullToAbsent || description != null) {
      map['description'] = Variable<String>(description);
    }
    map['instructions'] = Variable<String>(instructions);
    if (!nullToAbsent || imageUrl != null) {
      map['image_url'] = Variable<String>(imageUrl);
    }
    map['source'] = Variable<String>(source);
    map['is_favorite'] = Variable<bool>(isFavorite);
    if (!nullToAbsent || prepTimeMinutes != null) {
      map['prep_time_minutes'] = Variable<int>(prepTimeMinutes);
    }
    if (!nullToAbsent || cookTimeMinutes != null) {
      map['cook_time_minutes'] = Variable<int>(cookTimeMinutes);
    }
    map['servings'] = Variable<int>(servings);
    if (!nullToAbsent || caloriesPerServing != null) {
      map['calories_per_serving'] = Variable<int>(caloriesPerServing);
    }
    if (!nullToAbsent || proteinGPerServing != null) {
      map['protein_g_per_serving'] = Variable<double>(proteinGPerServing);
    }
    if (!nullToAbsent || carbsGPerServing != null) {
      map['carbs_g_per_serving'] = Variable<double>(carbsGPerServing);
    }
    if (!nullToAbsent || fatGPerServing != null) {
      map['fat_g_per_serving'] = Variable<double>(fatGPerServing);
    }
    if (!nullToAbsent || totalDishNutrientSummary != null) {
      map['total_dish_nutrient_summary'] = Variable<String>(
        totalDishNutrientSummary,
      );
    }
    if (!nullToAbsent || dishEffectsAndNotes != null) {
      map['dish_effects_and_notes'] = Variable<String>(dishEffectsAndNotes);
    }
    if (!nullToAbsent || totalDishHeartHealthIndicatorsJson != null) {
      map['total_dish_heart_health_indicators_json'] = Variable<String>(
        totalDishHeartHealthIndicatorsJson,
      );
    }
    if (!nullToAbsent || totalDishVitaminsJson != null) {
      map['total_dish_vitamins_json'] = Variable<String>(totalDishVitaminsJson);
    }
    if (!nullToAbsent || totalDishMineralsJson != null) {
      map['total_dish_minerals_json'] = Variable<String>(totalDishMineralsJson);
    }
    return map;
  }

  LocalRecipesCompanion toCompanion(bool nullToAbsent) {
    return LocalRecipesCompanion(
      id: Value(id),
      name: Value(name),
      description:
          description == null && nullToAbsent
              ? const Value.absent()
              : Value(description),
      instructions: Value(instructions),
      imageUrl:
          imageUrl == null && nullToAbsent
              ? const Value.absent()
              : Value(imageUrl),
      source: Value(source),
      isFavorite: Value(isFavorite),
      prepTimeMinutes:
          prepTimeMinutes == null && nullToAbsent
              ? const Value.absent()
              : Value(prepTimeMinutes),
      cookTimeMinutes:
          cookTimeMinutes == null && nullToAbsent
              ? const Value.absent()
              : Value(cookTimeMinutes),
      servings: Value(servings),
      caloriesPerServing:
          caloriesPerServing == null && nullToAbsent
              ? const Value.absent()
              : Value(caloriesPerServing),
      proteinGPerServing:
          proteinGPerServing == null && nullToAbsent
              ? const Value.absent()
              : Value(proteinGPerServing),
      carbsGPerServing:
          carbsGPerServing == null && nullToAbsent
              ? const Value.absent()
              : Value(carbsGPerServing),
      fatGPerServing:
          fatGPerServing == null && nullToAbsent
              ? const Value.absent()
              : Value(fatGPerServing),
      totalDishNutrientSummary:
          totalDishNutrientSummary == null && nullToAbsent
              ? const Value.absent()
              : Value(totalDishNutrientSummary),
      dishEffectsAndNotes:
          dishEffectsAndNotes == null && nullToAbsent
              ? const Value.absent()
              : Value(dishEffectsAndNotes),
      totalDishHeartHealthIndicatorsJson:
          totalDishHeartHealthIndicatorsJson == null && nullToAbsent
              ? const Value.absent()
              : Value(totalDishHeartHealthIndicatorsJson),
      totalDishVitaminsJson:
          totalDishVitaminsJson == null && nullToAbsent
              ? const Value.absent()
              : Value(totalDishVitaminsJson),
      totalDishMineralsJson:
          totalDishMineralsJson == null && nullToAbsent
              ? const Value.absent()
              : Value(totalDishMineralsJson),
    );
  }

  factory LocalRecipe.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return LocalRecipe(
      id: serializer.fromJson<String>(json['id']),
      name: serializer.fromJson<String>(json['name']),
      description: serializer.fromJson<String?>(json['description']),
      instructions: serializer.fromJson<String>(json['instructions']),
      imageUrl: serializer.fromJson<String?>(json['imageUrl']),
      source: serializer.fromJson<String>(json['source']),
      isFavorite: serializer.fromJson<bool>(json['isFavorite']),
      prepTimeMinutes: serializer.fromJson<int?>(json['prepTimeMinutes']),
      cookTimeMinutes: serializer.fromJson<int?>(json['cookTimeMinutes']),
      servings: serializer.fromJson<int>(json['servings']),
      caloriesPerServing: serializer.fromJson<int?>(json['caloriesPerServing']),
      proteinGPerServing: serializer.fromJson<double?>(
        json['proteinGPerServing'],
      ),
      carbsGPerServing: serializer.fromJson<double?>(json['carbsGPerServing']),
      fatGPerServing: serializer.fromJson<double?>(json['fatGPerServing']),
      totalDishNutrientSummary: serializer.fromJson<String?>(
        json['totalDishNutrientSummary'],
      ),
      dishEffectsAndNotes: serializer.fromJson<String?>(
        json['dishEffectsAndNotes'],
      ),
      totalDishHeartHealthIndicatorsJson: serializer.fromJson<String?>(
        json['totalDishHeartHealthIndicatorsJson'],
      ),
      totalDishVitaminsJson: serializer.fromJson<String?>(
        json['totalDishVitaminsJson'],
      ),
      totalDishMineralsJson: serializer.fromJson<String?>(
        json['totalDishMineralsJson'],
      ),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'name': serializer.toJson<String>(name),
      'description': serializer.toJson<String?>(description),
      'instructions': serializer.toJson<String>(instructions),
      'imageUrl': serializer.toJson<String?>(imageUrl),
      'source': serializer.toJson<String>(source),
      'isFavorite': serializer.toJson<bool>(isFavorite),
      'prepTimeMinutes': serializer.toJson<int?>(prepTimeMinutes),
      'cookTimeMinutes': serializer.toJson<int?>(cookTimeMinutes),
      'servings': serializer.toJson<int>(servings),
      'caloriesPerServing': serializer.toJson<int?>(caloriesPerServing),
      'proteinGPerServing': serializer.toJson<double?>(proteinGPerServing),
      'carbsGPerServing': serializer.toJson<double?>(carbsGPerServing),
      'fatGPerServing': serializer.toJson<double?>(fatGPerServing),
      'totalDishNutrientSummary': serializer.toJson<String?>(
        totalDishNutrientSummary,
      ),
      'dishEffectsAndNotes': serializer.toJson<String?>(dishEffectsAndNotes),
      'totalDishHeartHealthIndicatorsJson': serializer.toJson<String?>(
        totalDishHeartHealthIndicatorsJson,
      ),
      'totalDishVitaminsJson': serializer.toJson<String?>(
        totalDishVitaminsJson,
      ),
      'totalDishMineralsJson': serializer.toJson<String?>(
        totalDishMineralsJson,
      ),
    };
  }

  LocalRecipe copyWith({
    String? id,
    String? name,
    Value<String?> description = const Value.absent(),
    String? instructions,
    Value<String?> imageUrl = const Value.absent(),
    String? source,
    bool? isFavorite,
    Value<int?> prepTimeMinutes = const Value.absent(),
    Value<int?> cookTimeMinutes = const Value.absent(),
    int? servings,
    Value<int?> caloriesPerServing = const Value.absent(),
    Value<double?> proteinGPerServing = const Value.absent(),
    Value<double?> carbsGPerServing = const Value.absent(),
    Value<double?> fatGPerServing = const Value.absent(),
    Value<String?> totalDishNutrientSummary = const Value.absent(),
    Value<String?> dishEffectsAndNotes = const Value.absent(),
    Value<String?> totalDishHeartHealthIndicatorsJson = const Value.absent(),
    Value<String?> totalDishVitaminsJson = const Value.absent(),
    Value<String?> totalDishMineralsJson = const Value.absent(),
  }) => LocalRecipe(
    id: id ?? this.id,
    name: name ?? this.name,
    description: description.present ? description.value : this.description,
    instructions: instructions ?? this.instructions,
    imageUrl: imageUrl.present ? imageUrl.value : this.imageUrl,
    source: source ?? this.source,
    isFavorite: isFavorite ?? this.isFavorite,
    prepTimeMinutes:
        prepTimeMinutes.present ? prepTimeMinutes.value : this.prepTimeMinutes,
    cookTimeMinutes:
        cookTimeMinutes.present ? cookTimeMinutes.value : this.cookTimeMinutes,
    servings: servings ?? this.servings,
    caloriesPerServing:
        caloriesPerServing.present
            ? caloriesPerServing.value
            : this.caloriesPerServing,
    proteinGPerServing:
        proteinGPerServing.present
            ? proteinGPerServing.value
            : this.proteinGPerServing,
    carbsGPerServing:
        carbsGPerServing.present
            ? carbsGPerServing.value
            : this.carbsGPerServing,
    fatGPerServing:
        fatGPerServing.present ? fatGPerServing.value : this.fatGPerServing,
    totalDishNutrientSummary:
        totalDishNutrientSummary.present
            ? totalDishNutrientSummary.value
            : this.totalDishNutrientSummary,
    dishEffectsAndNotes:
        dishEffectsAndNotes.present
            ? dishEffectsAndNotes.value
            : this.dishEffectsAndNotes,
    totalDishHeartHealthIndicatorsJson:
        totalDishHeartHealthIndicatorsJson.present
            ? totalDishHeartHealthIndicatorsJson.value
            : this.totalDishHeartHealthIndicatorsJson,
    totalDishVitaminsJson:
        totalDishVitaminsJson.present
            ? totalDishVitaminsJson.value
            : this.totalDishVitaminsJson,
    totalDishMineralsJson:
        totalDishMineralsJson.present
            ? totalDishMineralsJson.value
            : this.totalDishMineralsJson,
  );
  LocalRecipe copyWithCompanion(LocalRecipesCompanion data) {
    return LocalRecipe(
      id: data.id.present ? data.id.value : this.id,
      name: data.name.present ? data.name.value : this.name,
      description:
          data.description.present ? data.description.value : this.description,
      instructions:
          data.instructions.present
              ? data.instructions.value
              : this.instructions,
      imageUrl: data.imageUrl.present ? data.imageUrl.value : this.imageUrl,
      source: data.source.present ? data.source.value : this.source,
      isFavorite:
          data.isFavorite.present ? data.isFavorite.value : this.isFavorite,
      prepTimeMinutes:
          data.prepTimeMinutes.present
              ? data.prepTimeMinutes.value
              : this.prepTimeMinutes,
      cookTimeMinutes:
          data.cookTimeMinutes.present
              ? data.cookTimeMinutes.value
              : this.cookTimeMinutes,
      servings: data.servings.present ? data.servings.value : this.servings,
      caloriesPerServing:
          data.caloriesPerServing.present
              ? data.caloriesPerServing.value
              : this.caloriesPerServing,
      proteinGPerServing:
          data.proteinGPerServing.present
              ? data.proteinGPerServing.value
              : this.proteinGPerServing,
      carbsGPerServing:
          data.carbsGPerServing.present
              ? data.carbsGPerServing.value
              : this.carbsGPerServing,
      fatGPerServing:
          data.fatGPerServing.present
              ? data.fatGPerServing.value
              : this.fatGPerServing,
      totalDishNutrientSummary:
          data.totalDishNutrientSummary.present
              ? data.totalDishNutrientSummary.value
              : this.totalDishNutrientSummary,
      dishEffectsAndNotes:
          data.dishEffectsAndNotes.present
              ? data.dishEffectsAndNotes.value
              : this.dishEffectsAndNotes,
      totalDishHeartHealthIndicatorsJson:
          data.totalDishHeartHealthIndicatorsJson.present
              ? data.totalDishHeartHealthIndicatorsJson.value
              : this.totalDishHeartHealthIndicatorsJson,
      totalDishVitaminsJson:
          data.totalDishVitaminsJson.present
              ? data.totalDishVitaminsJson.value
              : this.totalDishVitaminsJson,
      totalDishMineralsJson:
          data.totalDishMineralsJson.present
              ? data.totalDishMineralsJson.value
              : this.totalDishMineralsJson,
    );
  }

  @override
  String toString() {
    return (StringBuffer('LocalRecipe(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('description: $description, ')
          ..write('instructions: $instructions, ')
          ..write('imageUrl: $imageUrl, ')
          ..write('source: $source, ')
          ..write('isFavorite: $isFavorite, ')
          ..write('prepTimeMinutes: $prepTimeMinutes, ')
          ..write('cookTimeMinutes: $cookTimeMinutes, ')
          ..write('servings: $servings, ')
          ..write('caloriesPerServing: $caloriesPerServing, ')
          ..write('proteinGPerServing: $proteinGPerServing, ')
          ..write('carbsGPerServing: $carbsGPerServing, ')
          ..write('fatGPerServing: $fatGPerServing, ')
          ..write('totalDishNutrientSummary: $totalDishNutrientSummary, ')
          ..write('dishEffectsAndNotes: $dishEffectsAndNotes, ')
          ..write(
            'totalDishHeartHealthIndicatorsJson: $totalDishHeartHealthIndicatorsJson, ',
          )
          ..write('totalDishVitaminsJson: $totalDishVitaminsJson, ')
          ..write('totalDishMineralsJson: $totalDishMineralsJson')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
    id,
    name,
    description,
    instructions,
    imageUrl,
    source,
    isFavorite,
    prepTimeMinutes,
    cookTimeMinutes,
    servings,
    caloriesPerServing,
    proteinGPerServing,
    carbsGPerServing,
    fatGPerServing,
    totalDishNutrientSummary,
    dishEffectsAndNotes,
    totalDishHeartHealthIndicatorsJson,
    totalDishVitaminsJson,
    totalDishMineralsJson,
  );
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is LocalRecipe &&
          other.id == this.id &&
          other.name == this.name &&
          other.description == this.description &&
          other.instructions == this.instructions &&
          other.imageUrl == this.imageUrl &&
          other.source == this.source &&
          other.isFavorite == this.isFavorite &&
          other.prepTimeMinutes == this.prepTimeMinutes &&
          other.cookTimeMinutes == this.cookTimeMinutes &&
          other.servings == this.servings &&
          other.caloriesPerServing == this.caloriesPerServing &&
          other.proteinGPerServing == this.proteinGPerServing &&
          other.carbsGPerServing == this.carbsGPerServing &&
          other.fatGPerServing == this.fatGPerServing &&
          other.totalDishNutrientSummary == this.totalDishNutrientSummary &&
          other.dishEffectsAndNotes == this.dishEffectsAndNotes &&
          other.totalDishHeartHealthIndicatorsJson ==
              this.totalDishHeartHealthIndicatorsJson &&
          other.totalDishVitaminsJson == this.totalDishVitaminsJson &&
          other.totalDishMineralsJson == this.totalDishMineralsJson);
}

class LocalRecipesCompanion extends UpdateCompanion<LocalRecipe> {
  final Value<String> id;
  final Value<String> name;
  final Value<String?> description;
  final Value<String> instructions;
  final Value<String?> imageUrl;
  final Value<String> source;
  final Value<bool> isFavorite;
  final Value<int?> prepTimeMinutes;
  final Value<int?> cookTimeMinutes;
  final Value<int> servings;
  final Value<int?> caloriesPerServing;
  final Value<double?> proteinGPerServing;
  final Value<double?> carbsGPerServing;
  final Value<double?> fatGPerServing;
  final Value<String?> totalDishNutrientSummary;
  final Value<String?> dishEffectsAndNotes;
  final Value<String?> totalDishHeartHealthIndicatorsJson;
  final Value<String?> totalDishVitaminsJson;
  final Value<String?> totalDishMineralsJson;
  final Value<int> rowid;
  const LocalRecipesCompanion({
    this.id = const Value.absent(),
    this.name = const Value.absent(),
    this.description = const Value.absent(),
    this.instructions = const Value.absent(),
    this.imageUrl = const Value.absent(),
    this.source = const Value.absent(),
    this.isFavorite = const Value.absent(),
    this.prepTimeMinutes = const Value.absent(),
    this.cookTimeMinutes = const Value.absent(),
    this.servings = const Value.absent(),
    this.caloriesPerServing = const Value.absent(),
    this.proteinGPerServing = const Value.absent(),
    this.carbsGPerServing = const Value.absent(),
    this.fatGPerServing = const Value.absent(),
    this.totalDishNutrientSummary = const Value.absent(),
    this.dishEffectsAndNotes = const Value.absent(),
    this.totalDishHeartHealthIndicatorsJson = const Value.absent(),
    this.totalDishVitaminsJson = const Value.absent(),
    this.totalDishMineralsJson = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  LocalRecipesCompanion.insert({
    required String id,
    required String name,
    this.description = const Value.absent(),
    required String instructions,
    this.imageUrl = const Value.absent(),
    required String source,
    this.isFavorite = const Value.absent(),
    this.prepTimeMinutes = const Value.absent(),
    this.cookTimeMinutes = const Value.absent(),
    required int servings,
    this.caloriesPerServing = const Value.absent(),
    this.proteinGPerServing = const Value.absent(),
    this.carbsGPerServing = const Value.absent(),
    this.fatGPerServing = const Value.absent(),
    this.totalDishNutrientSummary = const Value.absent(),
    this.dishEffectsAndNotes = const Value.absent(),
    this.totalDishHeartHealthIndicatorsJson = const Value.absent(),
    this.totalDishVitaminsJson = const Value.absent(),
    this.totalDishMineralsJson = const Value.absent(),
    this.rowid = const Value.absent(),
  }) : id = Value(id),
       name = Value(name),
       instructions = Value(instructions),
       source = Value(source),
       servings = Value(servings);
  static Insertable<LocalRecipe> custom({
    Expression<String>? id,
    Expression<String>? name,
    Expression<String>? description,
    Expression<String>? instructions,
    Expression<String>? imageUrl,
    Expression<String>? source,
    Expression<bool>? isFavorite,
    Expression<int>? prepTimeMinutes,
    Expression<int>? cookTimeMinutes,
    Expression<int>? servings,
    Expression<int>? caloriesPerServing,
    Expression<double>? proteinGPerServing,
    Expression<double>? carbsGPerServing,
    Expression<double>? fatGPerServing,
    Expression<String>? totalDishNutrientSummary,
    Expression<String>? dishEffectsAndNotes,
    Expression<String>? totalDishHeartHealthIndicatorsJson,
    Expression<String>? totalDishVitaminsJson,
    Expression<String>? totalDishMineralsJson,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (name != null) 'name': name,
      if (description != null) 'description': description,
      if (instructions != null) 'instructions': instructions,
      if (imageUrl != null) 'image_url': imageUrl,
      if (source != null) 'source': source,
      if (isFavorite != null) 'is_favorite': isFavorite,
      if (prepTimeMinutes != null) 'prep_time_minutes': prepTimeMinutes,
      if (cookTimeMinutes != null) 'cook_time_minutes': cookTimeMinutes,
      if (servings != null) 'servings': servings,
      if (caloriesPerServing != null)
        'calories_per_serving': caloriesPerServing,
      if (proteinGPerServing != null)
        'protein_g_per_serving': proteinGPerServing,
      if (carbsGPerServing != null) 'carbs_g_per_serving': carbsGPerServing,
      if (fatGPerServing != null) 'fat_g_per_serving': fatGPerServing,
      if (totalDishNutrientSummary != null)
        'total_dish_nutrient_summary': totalDishNutrientSummary,
      if (dishEffectsAndNotes != null)
        'dish_effects_and_notes': dishEffectsAndNotes,
      if (totalDishHeartHealthIndicatorsJson != null)
        'total_dish_heart_health_indicators_json':
            totalDishHeartHealthIndicatorsJson,
      if (totalDishVitaminsJson != null)
        'total_dish_vitamins_json': totalDishVitaminsJson,
      if (totalDishMineralsJson != null)
        'total_dish_minerals_json': totalDishMineralsJson,
      if (rowid != null) 'rowid': rowid,
    });
  }

  LocalRecipesCompanion copyWith({
    Value<String>? id,
    Value<String>? name,
    Value<String?>? description,
    Value<String>? instructions,
    Value<String?>? imageUrl,
    Value<String>? source,
    Value<bool>? isFavorite,
    Value<int?>? prepTimeMinutes,
    Value<int?>? cookTimeMinutes,
    Value<int>? servings,
    Value<int?>? caloriesPerServing,
    Value<double?>? proteinGPerServing,
    Value<double?>? carbsGPerServing,
    Value<double?>? fatGPerServing,
    Value<String?>? totalDishNutrientSummary,
    Value<String?>? dishEffectsAndNotes,
    Value<String?>? totalDishHeartHealthIndicatorsJson,
    Value<String?>? totalDishVitaminsJson,
    Value<String?>? totalDishMineralsJson,
    Value<int>? rowid,
  }) {
    return LocalRecipesCompanion(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      instructions: instructions ?? this.instructions,
      imageUrl: imageUrl ?? this.imageUrl,
      source: source ?? this.source,
      isFavorite: isFavorite ?? this.isFavorite,
      prepTimeMinutes: prepTimeMinutes ?? this.prepTimeMinutes,
      cookTimeMinutes: cookTimeMinutes ?? this.cookTimeMinutes,
      servings: servings ?? this.servings,
      caloriesPerServing: caloriesPerServing ?? this.caloriesPerServing,
      proteinGPerServing: proteinGPerServing ?? this.proteinGPerServing,
      carbsGPerServing: carbsGPerServing ?? this.carbsGPerServing,
      fatGPerServing: fatGPerServing ?? this.fatGPerServing,
      totalDishNutrientSummary:
          totalDishNutrientSummary ?? this.totalDishNutrientSummary,
      dishEffectsAndNotes: dishEffectsAndNotes ?? this.dishEffectsAndNotes,
      totalDishHeartHealthIndicatorsJson:
          totalDishHeartHealthIndicatorsJson ??
          this.totalDishHeartHealthIndicatorsJson,
      totalDishVitaminsJson:
          totalDishVitaminsJson ?? this.totalDishVitaminsJson,
      totalDishMineralsJson:
          totalDishMineralsJson ?? this.totalDishMineralsJson,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (description.present) {
      map['description'] = Variable<String>(description.value);
    }
    if (instructions.present) {
      map['instructions'] = Variable<String>(instructions.value);
    }
    if (imageUrl.present) {
      map['image_url'] = Variable<String>(imageUrl.value);
    }
    if (source.present) {
      map['source'] = Variable<String>(source.value);
    }
    if (isFavorite.present) {
      map['is_favorite'] = Variable<bool>(isFavorite.value);
    }
    if (prepTimeMinutes.present) {
      map['prep_time_minutes'] = Variable<int>(prepTimeMinutes.value);
    }
    if (cookTimeMinutes.present) {
      map['cook_time_minutes'] = Variable<int>(cookTimeMinutes.value);
    }
    if (servings.present) {
      map['servings'] = Variable<int>(servings.value);
    }
    if (caloriesPerServing.present) {
      map['calories_per_serving'] = Variable<int>(caloriesPerServing.value);
    }
    if (proteinGPerServing.present) {
      map['protein_g_per_serving'] = Variable<double>(proteinGPerServing.value);
    }
    if (carbsGPerServing.present) {
      map['carbs_g_per_serving'] = Variable<double>(carbsGPerServing.value);
    }
    if (fatGPerServing.present) {
      map['fat_g_per_serving'] = Variable<double>(fatGPerServing.value);
    }
    if (totalDishNutrientSummary.present) {
      map['total_dish_nutrient_summary'] = Variable<String>(
        totalDishNutrientSummary.value,
      );
    }
    if (dishEffectsAndNotes.present) {
      map['dish_effects_and_notes'] = Variable<String>(
        dishEffectsAndNotes.value,
      );
    }
    if (totalDishHeartHealthIndicatorsJson.present) {
      map['total_dish_heart_health_indicators_json'] = Variable<String>(
        totalDishHeartHealthIndicatorsJson.value,
      );
    }
    if (totalDishVitaminsJson.present) {
      map['total_dish_vitamins_json'] = Variable<String>(
        totalDishVitaminsJson.value,
      );
    }
    if (totalDishMineralsJson.present) {
      map['total_dish_minerals_json'] = Variable<String>(
        totalDishMineralsJson.value,
      );
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('LocalRecipesCompanion(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('description: $description, ')
          ..write('instructions: $instructions, ')
          ..write('imageUrl: $imageUrl, ')
          ..write('source: $source, ')
          ..write('isFavorite: $isFavorite, ')
          ..write('prepTimeMinutes: $prepTimeMinutes, ')
          ..write('cookTimeMinutes: $cookTimeMinutes, ')
          ..write('servings: $servings, ')
          ..write('caloriesPerServing: $caloriesPerServing, ')
          ..write('proteinGPerServing: $proteinGPerServing, ')
          ..write('carbsGPerServing: $carbsGPerServing, ')
          ..write('fatGPerServing: $fatGPerServing, ')
          ..write('totalDishNutrientSummary: $totalDishNutrientSummary, ')
          ..write('dishEffectsAndNotes: $dishEffectsAndNotes, ')
          ..write(
            'totalDishHeartHealthIndicatorsJson: $totalDishHeartHealthIndicatorsJson, ',
          )
          ..write('totalDishVitaminsJson: $totalDishVitaminsJson, ')
          ..write('totalDishMineralsJson: $totalDishMineralsJson, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $LocalIngredientsTable extends LocalIngredients
    with TableInfo<$LocalIngredientsTable, LocalIngredient> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $LocalIngredientsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
    'id',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
    'name',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
    defaultConstraints: GeneratedColumn.constraintIsAlways('UNIQUE'),
  );
  static const VerificationMeta _defaultUnitMeta = const VerificationMeta(
    'defaultUnit',
  );
  @override
  late final GeneratedColumn<String> defaultUnit = GeneratedColumn<String>(
    'default_unit',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  @override
  List<GeneratedColumn> get $columns => [id, name, defaultUnit];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'local_ingredients';
  @override
  VerificationContext validateIntegrity(
    Insertable<LocalIngredient> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('name')) {
      context.handle(
        _nameMeta,
        name.isAcceptableOrUnknown(data['name']!, _nameMeta),
      );
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('default_unit')) {
      context.handle(
        _defaultUnitMeta,
        defaultUnit.isAcceptableOrUnknown(
          data['default_unit']!,
          _defaultUnitMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_defaultUnitMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  LocalIngredient map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return LocalIngredient(
      id:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}id'],
          )!,
      name:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}name'],
          )!,
      defaultUnit:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}default_unit'],
          )!,
    );
  }

  @override
  $LocalIngredientsTable createAlias(String alias) {
    return $LocalIngredientsTable(attachedDatabase, alias);
  }
}

class LocalIngredient extends DataClass implements Insertable<LocalIngredient> {
  /// Khóa chính. Chính là ingredient_id từ server
  final String id;

  /// Tên nguyên liệu, là duy nhất
  final String name;

  /// Đơn vị mặc định (ví dụ: 'g', 'ml')
  final String defaultUnit;
  const LocalIngredient({
    required this.id,
    required this.name,
    required this.defaultUnit,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['name'] = Variable<String>(name);
    map['default_unit'] = Variable<String>(defaultUnit);
    return map;
  }

  LocalIngredientsCompanion toCompanion(bool nullToAbsent) {
    return LocalIngredientsCompanion(
      id: Value(id),
      name: Value(name),
      defaultUnit: Value(defaultUnit),
    );
  }

  factory LocalIngredient.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return LocalIngredient(
      id: serializer.fromJson<String>(json['id']),
      name: serializer.fromJson<String>(json['name']),
      defaultUnit: serializer.fromJson<String>(json['defaultUnit']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'name': serializer.toJson<String>(name),
      'defaultUnit': serializer.toJson<String>(defaultUnit),
    };
  }

  LocalIngredient copyWith({String? id, String? name, String? defaultUnit}) =>
      LocalIngredient(
        id: id ?? this.id,
        name: name ?? this.name,
        defaultUnit: defaultUnit ?? this.defaultUnit,
      );
  LocalIngredient copyWithCompanion(LocalIngredientsCompanion data) {
    return LocalIngredient(
      id: data.id.present ? data.id.value : this.id,
      name: data.name.present ? data.name.value : this.name,
      defaultUnit:
          data.defaultUnit.present ? data.defaultUnit.value : this.defaultUnit,
    );
  }

  @override
  String toString() {
    return (StringBuffer('LocalIngredient(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('defaultUnit: $defaultUnit')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, name, defaultUnit);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is LocalIngredient &&
          other.id == this.id &&
          other.name == this.name &&
          other.defaultUnit == this.defaultUnit);
}

class LocalIngredientsCompanion extends UpdateCompanion<LocalIngredient> {
  final Value<String> id;
  final Value<String> name;
  final Value<String> defaultUnit;
  final Value<int> rowid;
  const LocalIngredientsCompanion({
    this.id = const Value.absent(),
    this.name = const Value.absent(),
    this.defaultUnit = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  LocalIngredientsCompanion.insert({
    required String id,
    required String name,
    required String defaultUnit,
    this.rowid = const Value.absent(),
  }) : id = Value(id),
       name = Value(name),
       defaultUnit = Value(defaultUnit);
  static Insertable<LocalIngredient> custom({
    Expression<String>? id,
    Expression<String>? name,
    Expression<String>? defaultUnit,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (name != null) 'name': name,
      if (defaultUnit != null) 'default_unit': defaultUnit,
      if (rowid != null) 'rowid': rowid,
    });
  }

  LocalIngredientsCompanion copyWith({
    Value<String>? id,
    Value<String>? name,
    Value<String>? defaultUnit,
    Value<int>? rowid,
  }) {
    return LocalIngredientsCompanion(
      id: id ?? this.id,
      name: name ?? this.name,
      defaultUnit: defaultUnit ?? this.defaultUnit,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (defaultUnit.present) {
      map['default_unit'] = Variable<String>(defaultUnit.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('LocalIngredientsCompanion(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('defaultUnit: $defaultUnit, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $LocalRecipeIngredientsTable extends LocalRecipeIngredients
    with TableInfo<$LocalRecipeIngredientsTable, LocalRecipeIngredient> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $LocalRecipeIngredientsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _recipeIdMeta = const VerificationMeta(
    'recipeId',
  );
  @override
  late final GeneratedColumn<String> recipeId = GeneratedColumn<String>(
    'recipe_id',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _ingredientIdMeta = const VerificationMeta(
    'ingredientId',
  );
  @override
  late final GeneratedColumn<String> ingredientId = GeneratedColumn<String>(
    'ingredient_id',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _quantityMeta = const VerificationMeta(
    'quantity',
  );
  @override
  late final GeneratedColumn<double> quantity = GeneratedColumn<double>(
    'quantity',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _unitMeta = const VerificationMeta('unit');
  @override
  late final GeneratedColumn<String> unit = GeneratedColumn<String>(
    'unit',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  @override
  List<GeneratedColumn> get $columns => [
    recipeId,
    ingredientId,
    quantity,
    unit,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'local_recipe_ingredients';
  @override
  VerificationContext validateIntegrity(
    Insertable<LocalRecipeIngredient> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('recipe_id')) {
      context.handle(
        _recipeIdMeta,
        recipeId.isAcceptableOrUnknown(data['recipe_id']!, _recipeIdMeta),
      );
    } else if (isInserting) {
      context.missing(_recipeIdMeta);
    }
    if (data.containsKey('ingredient_id')) {
      context.handle(
        _ingredientIdMeta,
        ingredientId.isAcceptableOrUnknown(
          data['ingredient_id']!,
          _ingredientIdMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_ingredientIdMeta);
    }
    if (data.containsKey('quantity')) {
      context.handle(
        _quantityMeta,
        quantity.isAcceptableOrUnknown(data['quantity']!, _quantityMeta),
      );
    } else if (isInserting) {
      context.missing(_quantityMeta);
    }
    if (data.containsKey('unit')) {
      context.handle(
        _unitMeta,
        unit.isAcceptableOrUnknown(data['unit']!, _unitMeta),
      );
    } else if (isInserting) {
      context.missing(_unitMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {recipeId, ingredientId};
  @override
  LocalRecipeIngredient map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return LocalRecipeIngredient(
      recipeId:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}recipe_id'],
          )!,
      ingredientId:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}ingredient_id'],
          )!,
      quantity:
          attachedDatabase.typeMapping.read(
            DriftSqlType.double,
            data['${effectivePrefix}quantity'],
          )!,
      unit:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}unit'],
          )!,
    );
  }

  @override
  $LocalRecipeIngredientsTable createAlias(String alias) {
    return $LocalRecipeIngredientsTable(attachedDatabase, alias);
  }
}

class LocalRecipeIngredient extends DataClass
    implements Insertable<LocalRecipeIngredient> {
  /// Phần của Khóa chính phức hợp. Tham chiếu LocalRecipes.id
  final String recipeId;

  /// Phần của Khóa chính phức hợp. Tham chiếu LocalIngredients.id
  final String ingredientId;

  /// Số lượng cần thiết
  final double quantity;

  /// Đơn vị tính của số lượng
  final String unit;
  const LocalRecipeIngredient({
    required this.recipeId,
    required this.ingredientId,
    required this.quantity,
    required this.unit,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['recipe_id'] = Variable<String>(recipeId);
    map['ingredient_id'] = Variable<String>(ingredientId);
    map['quantity'] = Variable<double>(quantity);
    map['unit'] = Variable<String>(unit);
    return map;
  }

  LocalRecipeIngredientsCompanion toCompanion(bool nullToAbsent) {
    return LocalRecipeIngredientsCompanion(
      recipeId: Value(recipeId),
      ingredientId: Value(ingredientId),
      quantity: Value(quantity),
      unit: Value(unit),
    );
  }

  factory LocalRecipeIngredient.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return LocalRecipeIngredient(
      recipeId: serializer.fromJson<String>(json['recipeId']),
      ingredientId: serializer.fromJson<String>(json['ingredientId']),
      quantity: serializer.fromJson<double>(json['quantity']),
      unit: serializer.fromJson<String>(json['unit']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'recipeId': serializer.toJson<String>(recipeId),
      'ingredientId': serializer.toJson<String>(ingredientId),
      'quantity': serializer.toJson<double>(quantity),
      'unit': serializer.toJson<String>(unit),
    };
  }

  LocalRecipeIngredient copyWith({
    String? recipeId,
    String? ingredientId,
    double? quantity,
    String? unit,
  }) => LocalRecipeIngredient(
    recipeId: recipeId ?? this.recipeId,
    ingredientId: ingredientId ?? this.ingredientId,
    quantity: quantity ?? this.quantity,
    unit: unit ?? this.unit,
  );
  LocalRecipeIngredient copyWithCompanion(
    LocalRecipeIngredientsCompanion data,
  ) {
    return LocalRecipeIngredient(
      recipeId: data.recipeId.present ? data.recipeId.value : this.recipeId,
      ingredientId:
          data.ingredientId.present
              ? data.ingredientId.value
              : this.ingredientId,
      quantity: data.quantity.present ? data.quantity.value : this.quantity,
      unit: data.unit.present ? data.unit.value : this.unit,
    );
  }

  @override
  String toString() {
    return (StringBuffer('LocalRecipeIngredient(')
          ..write('recipeId: $recipeId, ')
          ..write('ingredientId: $ingredientId, ')
          ..write('quantity: $quantity, ')
          ..write('unit: $unit')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(recipeId, ingredientId, quantity, unit);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is LocalRecipeIngredient &&
          other.recipeId == this.recipeId &&
          other.ingredientId == this.ingredientId &&
          other.quantity == this.quantity &&
          other.unit == this.unit);
}

class LocalRecipeIngredientsCompanion
    extends UpdateCompanion<LocalRecipeIngredient> {
  final Value<String> recipeId;
  final Value<String> ingredientId;
  final Value<double> quantity;
  final Value<String> unit;
  final Value<int> rowid;
  const LocalRecipeIngredientsCompanion({
    this.recipeId = const Value.absent(),
    this.ingredientId = const Value.absent(),
    this.quantity = const Value.absent(),
    this.unit = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  LocalRecipeIngredientsCompanion.insert({
    required String recipeId,
    required String ingredientId,
    required double quantity,
    required String unit,
    this.rowid = const Value.absent(),
  }) : recipeId = Value(recipeId),
       ingredientId = Value(ingredientId),
       quantity = Value(quantity),
       unit = Value(unit);
  static Insertable<LocalRecipeIngredient> custom({
    Expression<String>? recipeId,
    Expression<String>? ingredientId,
    Expression<double>? quantity,
    Expression<String>? unit,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (recipeId != null) 'recipe_id': recipeId,
      if (ingredientId != null) 'ingredient_id': ingredientId,
      if (quantity != null) 'quantity': quantity,
      if (unit != null) 'unit': unit,
      if (rowid != null) 'rowid': rowid,
    });
  }

  LocalRecipeIngredientsCompanion copyWith({
    Value<String>? recipeId,
    Value<String>? ingredientId,
    Value<double>? quantity,
    Value<String>? unit,
    Value<int>? rowid,
  }) {
    return LocalRecipeIngredientsCompanion(
      recipeId: recipeId ?? this.recipeId,
      ingredientId: ingredientId ?? this.ingredientId,
      quantity: quantity ?? this.quantity,
      unit: unit ?? this.unit,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (recipeId.present) {
      map['recipe_id'] = Variable<String>(recipeId.value);
    }
    if (ingredientId.present) {
      map['ingredient_id'] = Variable<String>(ingredientId.value);
    }
    if (quantity.present) {
      map['quantity'] = Variable<double>(quantity.value);
    }
    if (unit.present) {
      map['unit'] = Variable<String>(unit.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('LocalRecipeIngredientsCompanion(')
          ..write('recipeId: $recipeId, ')
          ..write('ingredientId: $ingredientId, ')
          ..write('quantity: $quantity, ')
          ..write('unit: $unit, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $MealPlansTable extends MealPlans
    with TableInfo<$MealPlansTable, MealPlan> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $MealPlansTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
    'id',
    aliasedName,
    false,
    hasAutoIncrement: true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'PRIMARY KEY AUTOINCREMENT',
    ),
  );
  static const VerificationMeta _dateMeta = const VerificationMeta('date');
  @override
  late final GeneratedColumn<DateTime> date = GeneratedColumn<DateTime>(
    'date',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _mealTypeMeta = const VerificationMeta(
    'mealType',
  );
  @override
  late final GeneratedColumn<String> mealType = GeneratedColumn<String>(
    'meal_type',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _recipeIdMeta = const VerificationMeta(
    'recipeId',
  );
  @override
  late final GeneratedColumn<String> recipeId = GeneratedColumn<String>(
    'recipe_id',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _syncStatusMeta = const VerificationMeta(
    'syncStatus',
  );
  @override
  late final GeneratedColumn<String> syncStatus = GeneratedColumn<String>(
    'sync_status',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
    defaultValue: const Constant('needs_create'),
  );
  @override
  List<GeneratedColumn> get $columns => [
    id,
    date,
    mealType,
    recipeId,
    syncStatus,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'meal_plans';
  @override
  VerificationContext validateIntegrity(
    Insertable<MealPlan> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('date')) {
      context.handle(
        _dateMeta,
        date.isAcceptableOrUnknown(data['date']!, _dateMeta),
      );
    } else if (isInserting) {
      context.missing(_dateMeta);
    }
    if (data.containsKey('meal_type')) {
      context.handle(
        _mealTypeMeta,
        mealType.isAcceptableOrUnknown(data['meal_type']!, _mealTypeMeta),
      );
    } else if (isInserting) {
      context.missing(_mealTypeMeta);
    }
    if (data.containsKey('recipe_id')) {
      context.handle(
        _recipeIdMeta,
        recipeId.isAcceptableOrUnknown(data['recipe_id']!, _recipeIdMeta),
      );
    } else if (isInserting) {
      context.missing(_recipeIdMeta);
    }
    if (data.containsKey('sync_status')) {
      context.handle(
        _syncStatusMeta,
        syncStatus.isAcceptableOrUnknown(data['sync_status']!, _syncStatusMeta),
      );
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  MealPlan map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return MealPlan(
      id:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}id'],
          )!,
      date:
          attachedDatabase.typeMapping.read(
            DriftSqlType.dateTime,
            data['${effectivePrefix}date'],
          )!,
      mealType:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}meal_type'],
          )!,
      recipeId:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}recipe_id'],
          )!,
      syncStatus:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}sync_status'],
          )!,
    );
  }

  @override
  $MealPlansTable createAlias(String alias) {
    return $MealPlansTable(attachedDatabase, alias);
  }
}

class MealPlan extends DataClass implements Insertable<MealPlan> {
  /// Khóa chính tự tăng (chỉ có ý nghĩa ở local)
  final int id;

  /// Ngày của bữa ăn
  final DateTime date;

  /// Ví dụ: 'Bữa sáng', 'Bữa trưa', 'Bữa tối'
  final String mealType;

  /// Tham chiếu đến LocalRecipes.id
  final String recipeId;

  /// Trạng thái đồng bộ: 'synced', 'needs_create', v.v.
  final String syncStatus;
  const MealPlan({
    required this.id,
    required this.date,
    required this.mealType,
    required this.recipeId,
    required this.syncStatus,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['date'] = Variable<DateTime>(date);
    map['meal_type'] = Variable<String>(mealType);
    map['recipe_id'] = Variable<String>(recipeId);
    map['sync_status'] = Variable<String>(syncStatus);
    return map;
  }

  MealPlansCompanion toCompanion(bool nullToAbsent) {
    return MealPlansCompanion(
      id: Value(id),
      date: Value(date),
      mealType: Value(mealType),
      recipeId: Value(recipeId),
      syncStatus: Value(syncStatus),
    );
  }

  factory MealPlan.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return MealPlan(
      id: serializer.fromJson<int>(json['id']),
      date: serializer.fromJson<DateTime>(json['date']),
      mealType: serializer.fromJson<String>(json['mealType']),
      recipeId: serializer.fromJson<String>(json['recipeId']),
      syncStatus: serializer.fromJson<String>(json['syncStatus']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'date': serializer.toJson<DateTime>(date),
      'mealType': serializer.toJson<String>(mealType),
      'recipeId': serializer.toJson<String>(recipeId),
      'syncStatus': serializer.toJson<String>(syncStatus),
    };
  }

  MealPlan copyWith({
    int? id,
    DateTime? date,
    String? mealType,
    String? recipeId,
    String? syncStatus,
  }) => MealPlan(
    id: id ?? this.id,
    date: date ?? this.date,
    mealType: mealType ?? this.mealType,
    recipeId: recipeId ?? this.recipeId,
    syncStatus: syncStatus ?? this.syncStatus,
  );
  MealPlan copyWithCompanion(MealPlansCompanion data) {
    return MealPlan(
      id: data.id.present ? data.id.value : this.id,
      date: data.date.present ? data.date.value : this.date,
      mealType: data.mealType.present ? data.mealType.value : this.mealType,
      recipeId: data.recipeId.present ? data.recipeId.value : this.recipeId,
      syncStatus:
          data.syncStatus.present ? data.syncStatus.value : this.syncStatus,
    );
  }

  @override
  String toString() {
    return (StringBuffer('MealPlan(')
          ..write('id: $id, ')
          ..write('date: $date, ')
          ..write('mealType: $mealType, ')
          ..write('recipeId: $recipeId, ')
          ..write('syncStatus: $syncStatus')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, date, mealType, recipeId, syncStatus);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is MealPlan &&
          other.id == this.id &&
          other.date == this.date &&
          other.mealType == this.mealType &&
          other.recipeId == this.recipeId &&
          other.syncStatus == this.syncStatus);
}

class MealPlansCompanion extends UpdateCompanion<MealPlan> {
  final Value<int> id;
  final Value<DateTime> date;
  final Value<String> mealType;
  final Value<String> recipeId;
  final Value<String> syncStatus;
  const MealPlansCompanion({
    this.id = const Value.absent(),
    this.date = const Value.absent(),
    this.mealType = const Value.absent(),
    this.recipeId = const Value.absent(),
    this.syncStatus = const Value.absent(),
  });
  MealPlansCompanion.insert({
    this.id = const Value.absent(),
    required DateTime date,
    required String mealType,
    required String recipeId,
    this.syncStatus = const Value.absent(),
  }) : date = Value(date),
       mealType = Value(mealType),
       recipeId = Value(recipeId);
  static Insertable<MealPlan> custom({
    Expression<int>? id,
    Expression<DateTime>? date,
    Expression<String>? mealType,
    Expression<String>? recipeId,
    Expression<String>? syncStatus,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (date != null) 'date': date,
      if (mealType != null) 'meal_type': mealType,
      if (recipeId != null) 'recipe_id': recipeId,
      if (syncStatus != null) 'sync_status': syncStatus,
    });
  }

  MealPlansCompanion copyWith({
    Value<int>? id,
    Value<DateTime>? date,
    Value<String>? mealType,
    Value<String>? recipeId,
    Value<String>? syncStatus,
  }) {
    return MealPlansCompanion(
      id: id ?? this.id,
      date: date ?? this.date,
      mealType: mealType ?? this.mealType,
      recipeId: recipeId ?? this.recipeId,
      syncStatus: syncStatus ?? this.syncStatus,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (date.present) {
      map['date'] = Variable<DateTime>(date.value);
    }
    if (mealType.present) {
      map['meal_type'] = Variable<String>(mealType.value);
    }
    if (recipeId.present) {
      map['recipe_id'] = Variable<String>(recipeId.value);
    }
    if (syncStatus.present) {
      map['sync_status'] = Variable<String>(syncStatus.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('MealPlansCompanion(')
          ..write('id: $id, ')
          ..write('date: $date, ')
          ..write('mealType: $mealType, ')
          ..write('recipeId: $recipeId, ')
          ..write('syncStatus: $syncStatus')
          ..write(')'))
        .toString();
  }
}

class $ShoppingListItemsTable extends ShoppingListItems
    with TableInfo<$ShoppingListItemsTable, ShoppingListItem> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ShoppingListItemsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
    'id',
    aliasedName,
    false,
    hasAutoIncrement: true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'PRIMARY KEY AUTOINCREMENT',
    ),
  );
  static const VerificationMeta _ingredientIdMeta = const VerificationMeta(
    'ingredientId',
  );
  @override
  late final GeneratedColumn<String> ingredientId = GeneratedColumn<String>(
    'ingredient_id',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _totalQuantityMeta = const VerificationMeta(
    'totalQuantity',
  );
  @override
  late final GeneratedColumn<double> totalQuantity = GeneratedColumn<double>(
    'total_quantity',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _isPurchasedMeta = const VerificationMeta(
    'isPurchased',
  );
  @override
  late final GeneratedColumn<bool> isPurchased = GeneratedColumn<bool>(
    'is_purchased',
    aliasedName,
    false,
    type: DriftSqlType.bool,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'CHECK ("is_purchased" IN (0, 1))',
    ),
    defaultValue: const Constant(false),
  );
  @override
  List<GeneratedColumn> get $columns => [
    id,
    ingredientId,
    totalQuantity,
    isPurchased,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'shopping_list_items';
  @override
  VerificationContext validateIntegrity(
    Insertable<ShoppingListItem> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('ingredient_id')) {
      context.handle(
        _ingredientIdMeta,
        ingredientId.isAcceptableOrUnknown(
          data['ingredient_id']!,
          _ingredientIdMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_ingredientIdMeta);
    }
    if (data.containsKey('total_quantity')) {
      context.handle(
        _totalQuantityMeta,
        totalQuantity.isAcceptableOrUnknown(
          data['total_quantity']!,
          _totalQuantityMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_totalQuantityMeta);
    }
    if (data.containsKey('is_purchased')) {
      context.handle(
        _isPurchasedMeta,
        isPurchased.isAcceptableOrUnknown(
          data['is_purchased']!,
          _isPurchasedMeta,
        ),
      );
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  ShoppingListItem map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ShoppingListItem(
      id:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}id'],
          )!,
      ingredientId:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}ingredient_id'],
          )!,
      totalQuantity:
          attachedDatabase.typeMapping.read(
            DriftSqlType.double,
            data['${effectivePrefix}total_quantity'],
          )!,
      isPurchased:
          attachedDatabase.typeMapping.read(
            DriftSqlType.bool,
            data['${effectivePrefix}is_purchased'],
          )!,
    );
  }

  @override
  $ShoppingListItemsTable createAlias(String alias) {
    return $ShoppingListItemsTable(attachedDatabase, alias);
  }
}

class ShoppingListItem extends DataClass
    implements Insertable<ShoppingListItem> {
  /// Khóa chính tự tăng
  final int id;

  /// Tham chiếu đến LocalIngredients.id
  final String ingredientId;

  /// Tổng số lượng cần mua (đã được gom lại)
  final double totalQuantity;

  /// Trạng thái đã mua hay chưa
  final bool isPurchased;
  const ShoppingListItem({
    required this.id,
    required this.ingredientId,
    required this.totalQuantity,
    required this.isPurchased,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['ingredient_id'] = Variable<String>(ingredientId);
    map['total_quantity'] = Variable<double>(totalQuantity);
    map['is_purchased'] = Variable<bool>(isPurchased);
    return map;
  }

  ShoppingListItemsCompanion toCompanion(bool nullToAbsent) {
    return ShoppingListItemsCompanion(
      id: Value(id),
      ingredientId: Value(ingredientId),
      totalQuantity: Value(totalQuantity),
      isPurchased: Value(isPurchased),
    );
  }

  factory ShoppingListItem.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ShoppingListItem(
      id: serializer.fromJson<int>(json['id']),
      ingredientId: serializer.fromJson<String>(json['ingredientId']),
      totalQuantity: serializer.fromJson<double>(json['totalQuantity']),
      isPurchased: serializer.fromJson<bool>(json['isPurchased']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'ingredientId': serializer.toJson<String>(ingredientId),
      'totalQuantity': serializer.toJson<double>(totalQuantity),
      'isPurchased': serializer.toJson<bool>(isPurchased),
    };
  }

  ShoppingListItem copyWith({
    int? id,
    String? ingredientId,
    double? totalQuantity,
    bool? isPurchased,
  }) => ShoppingListItem(
    id: id ?? this.id,
    ingredientId: ingredientId ?? this.ingredientId,
    totalQuantity: totalQuantity ?? this.totalQuantity,
    isPurchased: isPurchased ?? this.isPurchased,
  );
  ShoppingListItem copyWithCompanion(ShoppingListItemsCompanion data) {
    return ShoppingListItem(
      id: data.id.present ? data.id.value : this.id,
      ingredientId:
          data.ingredientId.present
              ? data.ingredientId.value
              : this.ingredientId,
      totalQuantity:
          data.totalQuantity.present
              ? data.totalQuantity.value
              : this.totalQuantity,
      isPurchased:
          data.isPurchased.present ? data.isPurchased.value : this.isPurchased,
    );
  }

  @override
  String toString() {
    return (StringBuffer('ShoppingListItem(')
          ..write('id: $id, ')
          ..write('ingredientId: $ingredientId, ')
          ..write('totalQuantity: $totalQuantity, ')
          ..write('isPurchased: $isPurchased')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, ingredientId, totalQuantity, isPurchased);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ShoppingListItem &&
          other.id == this.id &&
          other.ingredientId == this.ingredientId &&
          other.totalQuantity == this.totalQuantity &&
          other.isPurchased == this.isPurchased);
}

class ShoppingListItemsCompanion extends UpdateCompanion<ShoppingListItem> {
  final Value<int> id;
  final Value<String> ingredientId;
  final Value<double> totalQuantity;
  final Value<bool> isPurchased;
  const ShoppingListItemsCompanion({
    this.id = const Value.absent(),
    this.ingredientId = const Value.absent(),
    this.totalQuantity = const Value.absent(),
    this.isPurchased = const Value.absent(),
  });
  ShoppingListItemsCompanion.insert({
    this.id = const Value.absent(),
    required String ingredientId,
    required double totalQuantity,
    this.isPurchased = const Value.absent(),
  }) : ingredientId = Value(ingredientId),
       totalQuantity = Value(totalQuantity);
  static Insertable<ShoppingListItem> custom({
    Expression<int>? id,
    Expression<String>? ingredientId,
    Expression<double>? totalQuantity,
    Expression<bool>? isPurchased,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (ingredientId != null) 'ingredient_id': ingredientId,
      if (totalQuantity != null) 'total_quantity': totalQuantity,
      if (isPurchased != null) 'is_purchased': isPurchased,
    });
  }

  ShoppingListItemsCompanion copyWith({
    Value<int>? id,
    Value<String>? ingredientId,
    Value<double>? totalQuantity,
    Value<bool>? isPurchased,
  }) {
    return ShoppingListItemsCompanion(
      id: id ?? this.id,
      ingredientId: ingredientId ?? this.ingredientId,
      totalQuantity: totalQuantity ?? this.totalQuantity,
      isPurchased: isPurchased ?? this.isPurchased,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (ingredientId.present) {
      map['ingredient_id'] = Variable<String>(ingredientId.value);
    }
    if (totalQuantity.present) {
      map['total_quantity'] = Variable<double>(totalQuantity.value);
    }
    if (isPurchased.present) {
      map['is_purchased'] = Variable<bool>(isPurchased.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ShoppingListItemsCompanion(')
          ..write('id: $id, ')
          ..write('ingredientId: $ingredientId, ')
          ..write('totalQuantity: $totalQuantity, ')
          ..write('isPurchased: $isPurchased')
          ..write(')'))
        .toString();
  }
}

class $ImageScanHistoriesTable extends ImageScanHistories
    with TableInfo<$ImageScanHistoriesTable, ImageScanHistory> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ImageScanHistoriesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
    'id',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _scanTypeMeta = const VerificationMeta(
    'scanType',
  );
  @override
  late final GeneratedColumn<String> scanType = GeneratedColumn<String>(
    'scan_type',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _requestTimestampMeta = const VerificationMeta(
    'requestTimestamp',
  );
  @override
  late final GeneratedColumn<DateTime> requestTimestamp =
      GeneratedColumn<DateTime>(
        'request_timestamp',
        aliasedName,
        false,
        type: DriftSqlType.dateTime,
        requiredDuringInsert: true,
      );
  static const VerificationMeta _cachedImageUrlMeta = const VerificationMeta(
    'cachedImageUrl',
  );
  @override
  late final GeneratedColumn<String> cachedImageUrl = GeneratedColumn<String>(
    'cached_image_url',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _userConfirmedResultJsonMeta =
      const VerificationMeta('userConfirmedResultJson');
  @override
  late final GeneratedColumn<String> userConfirmedResultJson =
      GeneratedColumn<String>(
        'user_confirmed_result_json',
        aliasedName,
        true,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
      );
  static const VerificationMeta _linkedRecipeIdMeta = const VerificationMeta(
    'linkedRecipeId',
  );
  @override
  late final GeneratedColumn<String> linkedRecipeId = GeneratedColumn<String>(
    'linked_recipe_id',
    aliasedName,
    true,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
  );
  @override
  List<GeneratedColumn> get $columns => [
    id,
    scanType,
    requestTimestamp,
    cachedImageUrl,
    userConfirmedResultJson,
    linkedRecipeId,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'image_scan_histories';
  @override
  VerificationContext validateIntegrity(
    Insertable<ImageScanHistory> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('scan_type')) {
      context.handle(
        _scanTypeMeta,
        scanType.isAcceptableOrUnknown(data['scan_type']!, _scanTypeMeta),
      );
    } else if (isInserting) {
      context.missing(_scanTypeMeta);
    }
    if (data.containsKey('request_timestamp')) {
      context.handle(
        _requestTimestampMeta,
        requestTimestamp.isAcceptableOrUnknown(
          data['request_timestamp']!,
          _requestTimestampMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_requestTimestampMeta);
    }
    if (data.containsKey('cached_image_url')) {
      context.handle(
        _cachedImageUrlMeta,
        cachedImageUrl.isAcceptableOrUnknown(
          data['cached_image_url']!,
          _cachedImageUrlMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_cachedImageUrlMeta);
    }
    if (data.containsKey('user_confirmed_result_json')) {
      context.handle(
        _userConfirmedResultJsonMeta,
        userConfirmedResultJson.isAcceptableOrUnknown(
          data['user_confirmed_result_json']!,
          _userConfirmedResultJsonMeta,
        ),
      );
    }
    if (data.containsKey('linked_recipe_id')) {
      context.handle(
        _linkedRecipeIdMeta,
        linkedRecipeId.isAcceptableOrUnknown(
          data['linked_recipe_id']!,
          _linkedRecipeIdMeta,
        ),
      );
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  ImageScanHistory map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ImageScanHistory(
      id:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}id'],
          )!,
      scanType:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}scan_type'],
          )!,
      requestTimestamp:
          attachedDatabase.typeMapping.read(
            DriftSqlType.dateTime,
            data['${effectivePrefix}request_timestamp'],
          )!,
      cachedImageUrl:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}cached_image_url'],
          )!,
      userConfirmedResultJson: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}user_confirmed_result_json'],
      ),
      linkedRecipeId: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}linked_recipe_id'],
      ),
    );
  }

  @override
  $ImageScanHistoriesTable createAlias(String alias) {
    return $ImageScanHistoriesTable(attachedDatabase, alias);
  }
}

class ImageScanHistory extends DataClass
    implements Insertable<ImageScanHistory> {
  /// Khóa chính. Chính là scan_id từ server
  final String id;

  /// Loại quét: 'cooked_meal' (F2a) hoặc 'ingredients' (F2b)
  final String scanType;

  /// Thời gian người dùng thực hiện quét
  final DateTime requestTimestamp;

  /// Đường dẫn file ảnh đã cache hoặc URL gốc
  final String cachedImageUrl;

  /// Chuỗi JSON chứa kết quả người dùng đã xác nhận/sửa
  final String? userConfirmedResultJson;

  /// recipe_id đã được liên kết (có thể null)
  final String? linkedRecipeId;
  const ImageScanHistory({
    required this.id,
    required this.scanType,
    required this.requestTimestamp,
    required this.cachedImageUrl,
    this.userConfirmedResultJson,
    this.linkedRecipeId,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['scan_type'] = Variable<String>(scanType);
    map['request_timestamp'] = Variable<DateTime>(requestTimestamp);
    map['cached_image_url'] = Variable<String>(cachedImageUrl);
    if (!nullToAbsent || userConfirmedResultJson != null) {
      map['user_confirmed_result_json'] = Variable<String>(
        userConfirmedResultJson,
      );
    }
    if (!nullToAbsent || linkedRecipeId != null) {
      map['linked_recipe_id'] = Variable<String>(linkedRecipeId);
    }
    return map;
  }

  ImageScanHistoriesCompanion toCompanion(bool nullToAbsent) {
    return ImageScanHistoriesCompanion(
      id: Value(id),
      scanType: Value(scanType),
      requestTimestamp: Value(requestTimestamp),
      cachedImageUrl: Value(cachedImageUrl),
      userConfirmedResultJson:
          userConfirmedResultJson == null && nullToAbsent
              ? const Value.absent()
              : Value(userConfirmedResultJson),
      linkedRecipeId:
          linkedRecipeId == null && nullToAbsent
              ? const Value.absent()
              : Value(linkedRecipeId),
    );
  }

  factory ImageScanHistory.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ImageScanHistory(
      id: serializer.fromJson<String>(json['id']),
      scanType: serializer.fromJson<String>(json['scanType']),
      requestTimestamp: serializer.fromJson<DateTime>(json['requestTimestamp']),
      cachedImageUrl: serializer.fromJson<String>(json['cachedImageUrl']),
      userConfirmedResultJson: serializer.fromJson<String?>(
        json['userConfirmedResultJson'],
      ),
      linkedRecipeId: serializer.fromJson<String?>(json['linkedRecipeId']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'scanType': serializer.toJson<String>(scanType),
      'requestTimestamp': serializer.toJson<DateTime>(requestTimestamp),
      'cachedImageUrl': serializer.toJson<String>(cachedImageUrl),
      'userConfirmedResultJson': serializer.toJson<String?>(
        userConfirmedResultJson,
      ),
      'linkedRecipeId': serializer.toJson<String?>(linkedRecipeId),
    };
  }

  ImageScanHistory copyWith({
    String? id,
    String? scanType,
    DateTime? requestTimestamp,
    String? cachedImageUrl,
    Value<String?> userConfirmedResultJson = const Value.absent(),
    Value<String?> linkedRecipeId = const Value.absent(),
  }) => ImageScanHistory(
    id: id ?? this.id,
    scanType: scanType ?? this.scanType,
    requestTimestamp: requestTimestamp ?? this.requestTimestamp,
    cachedImageUrl: cachedImageUrl ?? this.cachedImageUrl,
    userConfirmedResultJson:
        userConfirmedResultJson.present
            ? userConfirmedResultJson.value
            : this.userConfirmedResultJson,
    linkedRecipeId:
        linkedRecipeId.present ? linkedRecipeId.value : this.linkedRecipeId,
  );
  ImageScanHistory copyWithCompanion(ImageScanHistoriesCompanion data) {
    return ImageScanHistory(
      id: data.id.present ? data.id.value : this.id,
      scanType: data.scanType.present ? data.scanType.value : this.scanType,
      requestTimestamp:
          data.requestTimestamp.present
              ? data.requestTimestamp.value
              : this.requestTimestamp,
      cachedImageUrl:
          data.cachedImageUrl.present
              ? data.cachedImageUrl.value
              : this.cachedImageUrl,
      userConfirmedResultJson:
          data.userConfirmedResultJson.present
              ? data.userConfirmedResultJson.value
              : this.userConfirmedResultJson,
      linkedRecipeId:
          data.linkedRecipeId.present
              ? data.linkedRecipeId.value
              : this.linkedRecipeId,
    );
  }

  @override
  String toString() {
    return (StringBuffer('ImageScanHistory(')
          ..write('id: $id, ')
          ..write('scanType: $scanType, ')
          ..write('requestTimestamp: $requestTimestamp, ')
          ..write('cachedImageUrl: $cachedImageUrl, ')
          ..write('userConfirmedResultJson: $userConfirmedResultJson, ')
          ..write('linkedRecipeId: $linkedRecipeId')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
    id,
    scanType,
    requestTimestamp,
    cachedImageUrl,
    userConfirmedResultJson,
    linkedRecipeId,
  );
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ImageScanHistory &&
          other.id == this.id &&
          other.scanType == this.scanType &&
          other.requestTimestamp == this.requestTimestamp &&
          other.cachedImageUrl == this.cachedImageUrl &&
          other.userConfirmedResultJson == this.userConfirmedResultJson &&
          other.linkedRecipeId == this.linkedRecipeId);
}

class ImageScanHistoriesCompanion extends UpdateCompanion<ImageScanHistory> {
  final Value<String> id;
  final Value<String> scanType;
  final Value<DateTime> requestTimestamp;
  final Value<String> cachedImageUrl;
  final Value<String?> userConfirmedResultJson;
  final Value<String?> linkedRecipeId;
  final Value<int> rowid;
  const ImageScanHistoriesCompanion({
    this.id = const Value.absent(),
    this.scanType = const Value.absent(),
    this.requestTimestamp = const Value.absent(),
    this.cachedImageUrl = const Value.absent(),
    this.userConfirmedResultJson = const Value.absent(),
    this.linkedRecipeId = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  ImageScanHistoriesCompanion.insert({
    required String id,
    required String scanType,
    required DateTime requestTimestamp,
    required String cachedImageUrl,
    this.userConfirmedResultJson = const Value.absent(),
    this.linkedRecipeId = const Value.absent(),
    this.rowid = const Value.absent(),
  }) : id = Value(id),
       scanType = Value(scanType),
       requestTimestamp = Value(requestTimestamp),
       cachedImageUrl = Value(cachedImageUrl);
  static Insertable<ImageScanHistory> custom({
    Expression<String>? id,
    Expression<String>? scanType,
    Expression<DateTime>? requestTimestamp,
    Expression<String>? cachedImageUrl,
    Expression<String>? userConfirmedResultJson,
    Expression<String>? linkedRecipeId,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (scanType != null) 'scan_type': scanType,
      if (requestTimestamp != null) 'request_timestamp': requestTimestamp,
      if (cachedImageUrl != null) 'cached_image_url': cachedImageUrl,
      if (userConfirmedResultJson != null)
        'user_confirmed_result_json': userConfirmedResultJson,
      if (linkedRecipeId != null) 'linked_recipe_id': linkedRecipeId,
      if (rowid != null) 'rowid': rowid,
    });
  }

  ImageScanHistoriesCompanion copyWith({
    Value<String>? id,
    Value<String>? scanType,
    Value<DateTime>? requestTimestamp,
    Value<String>? cachedImageUrl,
    Value<String?>? userConfirmedResultJson,
    Value<String?>? linkedRecipeId,
    Value<int>? rowid,
  }) {
    return ImageScanHistoriesCompanion(
      id: id ?? this.id,
      scanType: scanType ?? this.scanType,
      requestTimestamp: requestTimestamp ?? this.requestTimestamp,
      cachedImageUrl: cachedImageUrl ?? this.cachedImageUrl,
      userConfirmedResultJson:
          userConfirmedResultJson ?? this.userConfirmedResultJson,
      linkedRecipeId: linkedRecipeId ?? this.linkedRecipeId,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (scanType.present) {
      map['scan_type'] = Variable<String>(scanType.value);
    }
    if (requestTimestamp.present) {
      map['request_timestamp'] = Variable<DateTime>(requestTimestamp.value);
    }
    if (cachedImageUrl.present) {
      map['cached_image_url'] = Variable<String>(cachedImageUrl.value);
    }
    if (userConfirmedResultJson.present) {
      map['user_confirmed_result_json'] = Variable<String>(
        userConfirmedResultJson.value,
      );
    }
    if (linkedRecipeId.present) {
      map['linked_recipe_id'] = Variable<String>(linkedRecipeId.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ImageScanHistoriesCompanion(')
          ..write('id: $id, ')
          ..write('scanType: $scanType, ')
          ..write('requestTimestamp: $requestTimestamp, ')
          ..write('cachedImageUrl: $cachedImageUrl, ')
          ..write('userConfirmedResultJson: $userConfirmedResultJson, ')
          ..write('linkedRecipeId: $linkedRecipeId, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $UserTemporaryStatesTable extends UserTemporaryStates
    with TableInfo<$UserTemporaryStatesTable, UserTemporaryState> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $UserTemporaryStatesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
    'id',
    aliasedName,
    false,
    hasAutoIncrement: true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'PRIMARY KEY AUTOINCREMENT',
    ),
  );
  static const VerificationMeta _stateIdFromServerMeta = const VerificationMeta(
    'stateIdFromServer',
  );
  @override
  late final GeneratedColumn<String> stateIdFromServer =
      GeneratedColumn<String>(
        'state_id_from_server',
        aliasedName,
        true,
        type: DriftSqlType.string,
        requiredDuringInsert: false,
      );
  static const VerificationMeta _userIdMeta = const VerificationMeta('userId');
  @override
  late final GeneratedColumn<String> userId = GeneratedColumn<String>(
    'user_id',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _stateTagMeta = const VerificationMeta(
    'stateTag',
  );
  @override
  late final GeneratedColumn<String> stateTag = GeneratedColumn<String>(
    'state_tag',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _stateDescriptionMeta = const VerificationMeta(
    'stateDescription',
  );
  @override
  late final GeneratedColumn<String> stateDescription = GeneratedColumn<String>(
    'state_description',
    aliasedName,
    true,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _appliesFromMeta = const VerificationMeta(
    'appliesFrom',
  );
  @override
  late final GeneratedColumn<DateTime> appliesFrom = GeneratedColumn<DateTime>(
    'applies_from',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _appliesUntilMeta = const VerificationMeta(
    'appliesUntil',
  );
  @override
  late final GeneratedColumn<DateTime> appliesUntil = GeneratedColumn<DateTime>(
    'applies_until',
    aliasedName,
    true,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _isActiveMeta = const VerificationMeta(
    'isActive',
  );
  @override
  late final GeneratedColumn<bool> isActive = GeneratedColumn<bool>(
    'is_active',
    aliasedName,
    false,
    type: DriftSqlType.bool,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'CHECK ("is_active" IN (0, 1))',
    ),
    defaultValue: const Constant(false),
  );
  static const VerificationMeta _syncStatusMeta = const VerificationMeta(
    'syncStatus',
  );
  @override
  late final GeneratedColumn<String> syncStatus = GeneratedColumn<String>(
    'sync_status',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
    defaultValue: const Constant('needs_create'),
  );
  @override
  List<GeneratedColumn> get $columns => [
    id,
    stateIdFromServer,
    userId,
    stateTag,
    stateDescription,
    appliesFrom,
    appliesUntil,
    isActive,
    syncStatus,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'user_temporary_states';
  @override
  VerificationContext validateIntegrity(
    Insertable<UserTemporaryState> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('state_id_from_server')) {
      context.handle(
        _stateIdFromServerMeta,
        stateIdFromServer.isAcceptableOrUnknown(
          data['state_id_from_server']!,
          _stateIdFromServerMeta,
        ),
      );
    }
    if (data.containsKey('user_id')) {
      context.handle(
        _userIdMeta,
        userId.isAcceptableOrUnknown(data['user_id']!, _userIdMeta),
      );
    } else if (isInserting) {
      context.missing(_userIdMeta);
    }
    if (data.containsKey('state_tag')) {
      context.handle(
        _stateTagMeta,
        stateTag.isAcceptableOrUnknown(data['state_tag']!, _stateTagMeta),
      );
    } else if (isInserting) {
      context.missing(_stateTagMeta);
    }
    if (data.containsKey('state_description')) {
      context.handle(
        _stateDescriptionMeta,
        stateDescription.isAcceptableOrUnknown(
          data['state_description']!,
          _stateDescriptionMeta,
        ),
      );
    }
    if (data.containsKey('applies_from')) {
      context.handle(
        _appliesFromMeta,
        appliesFrom.isAcceptableOrUnknown(
          data['applies_from']!,
          _appliesFromMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_appliesFromMeta);
    }
    if (data.containsKey('applies_until')) {
      context.handle(
        _appliesUntilMeta,
        appliesUntil.isAcceptableOrUnknown(
          data['applies_until']!,
          _appliesUntilMeta,
        ),
      );
    }
    if (data.containsKey('is_active')) {
      context.handle(
        _isActiveMeta,
        isActive.isAcceptableOrUnknown(data['is_active']!, _isActiveMeta),
      );
    }
    if (data.containsKey('sync_status')) {
      context.handle(
        _syncStatusMeta,
        syncStatus.isAcceptableOrUnknown(data['sync_status']!, _syncStatusMeta),
      );
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  UserTemporaryState map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return UserTemporaryState(
      id:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}id'],
          )!,
      stateIdFromServer: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}state_id_from_server'],
      ),
      userId:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}user_id'],
          )!,
      stateTag:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}state_tag'],
          )!,
      stateDescription: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}state_description'],
      ),
      appliesFrom:
          attachedDatabase.typeMapping.read(
            DriftSqlType.dateTime,
            data['${effectivePrefix}applies_from'],
          )!,
      appliesUntil: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}applies_until'],
      ),
      isActive:
          attachedDatabase.typeMapping.read(
            DriftSqlType.bool,
            data['${effectivePrefix}is_active'],
          )!,
      syncStatus:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}sync_status'],
          )!,
    );
  }

  @override
  $UserTemporaryStatesTable createAlias(String alias) {
    return $UserTemporaryStatesTable(attachedDatabase, alias);
  }
}

class UserTemporaryState extends DataClass
    implements Insertable<UserTemporaryState> {
  /// Khóa chính tự tăng (chỉ có ý nghĩa ở local)
  final int id;

  /// state_id từ server để đồng bộ (có thể null)
  final String? stateIdFromServer;

  /// Tham chiếu đến LocalUsers.id
  final String userId;

  /// Thẻ mô tả trạng thái (ví dụ: 'feeling_sick')
  final String stateTag;

  /// Mô tả chi tiết thêm (có thể null)
  final String? stateDescription;

  /// Thời điểm trạng thái bắt đầu áp dụng
  final DateTime appliesFrom;

  /// Thời điểm trạng thái kết thúc (có thể null)
  final DateTime? appliesUntil;

  /// Đánh dấu trạng thái đang hoạt động
  final bool isActive;

  /// Trạng thái đồng bộ: 'synced', 'needs_create', v.v.
  final String syncStatus;
  const UserTemporaryState({
    required this.id,
    this.stateIdFromServer,
    required this.userId,
    required this.stateTag,
    this.stateDescription,
    required this.appliesFrom,
    this.appliesUntil,
    required this.isActive,
    required this.syncStatus,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    if (!nullToAbsent || stateIdFromServer != null) {
      map['state_id_from_server'] = Variable<String>(stateIdFromServer);
    }
    map['user_id'] = Variable<String>(userId);
    map['state_tag'] = Variable<String>(stateTag);
    if (!nullToAbsent || stateDescription != null) {
      map['state_description'] = Variable<String>(stateDescription);
    }
    map['applies_from'] = Variable<DateTime>(appliesFrom);
    if (!nullToAbsent || appliesUntil != null) {
      map['applies_until'] = Variable<DateTime>(appliesUntil);
    }
    map['is_active'] = Variable<bool>(isActive);
    map['sync_status'] = Variable<String>(syncStatus);
    return map;
  }

  UserTemporaryStatesCompanion toCompanion(bool nullToAbsent) {
    return UserTemporaryStatesCompanion(
      id: Value(id),
      stateIdFromServer:
          stateIdFromServer == null && nullToAbsent
              ? const Value.absent()
              : Value(stateIdFromServer),
      userId: Value(userId),
      stateTag: Value(stateTag),
      stateDescription:
          stateDescription == null && nullToAbsent
              ? const Value.absent()
              : Value(stateDescription),
      appliesFrom: Value(appliesFrom),
      appliesUntil:
          appliesUntil == null && nullToAbsent
              ? const Value.absent()
              : Value(appliesUntil),
      isActive: Value(isActive),
      syncStatus: Value(syncStatus),
    );
  }

  factory UserTemporaryState.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return UserTemporaryState(
      id: serializer.fromJson<int>(json['id']),
      stateIdFromServer: serializer.fromJson<String?>(
        json['stateIdFromServer'],
      ),
      userId: serializer.fromJson<String>(json['userId']),
      stateTag: serializer.fromJson<String>(json['stateTag']),
      stateDescription: serializer.fromJson<String?>(json['stateDescription']),
      appliesFrom: serializer.fromJson<DateTime>(json['appliesFrom']),
      appliesUntil: serializer.fromJson<DateTime?>(json['appliesUntil']),
      isActive: serializer.fromJson<bool>(json['isActive']),
      syncStatus: serializer.fromJson<String>(json['syncStatus']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'stateIdFromServer': serializer.toJson<String?>(stateIdFromServer),
      'userId': serializer.toJson<String>(userId),
      'stateTag': serializer.toJson<String>(stateTag),
      'stateDescription': serializer.toJson<String?>(stateDescription),
      'appliesFrom': serializer.toJson<DateTime>(appliesFrom),
      'appliesUntil': serializer.toJson<DateTime?>(appliesUntil),
      'isActive': serializer.toJson<bool>(isActive),
      'syncStatus': serializer.toJson<String>(syncStatus),
    };
  }

  UserTemporaryState copyWith({
    int? id,
    Value<String?> stateIdFromServer = const Value.absent(),
    String? userId,
    String? stateTag,
    Value<String?> stateDescription = const Value.absent(),
    DateTime? appliesFrom,
    Value<DateTime?> appliesUntil = const Value.absent(),
    bool? isActive,
    String? syncStatus,
  }) => UserTemporaryState(
    id: id ?? this.id,
    stateIdFromServer:
        stateIdFromServer.present
            ? stateIdFromServer.value
            : this.stateIdFromServer,
    userId: userId ?? this.userId,
    stateTag: stateTag ?? this.stateTag,
    stateDescription:
        stateDescription.present
            ? stateDescription.value
            : this.stateDescription,
    appliesFrom: appliesFrom ?? this.appliesFrom,
    appliesUntil: appliesUntil.present ? appliesUntil.value : this.appliesUntil,
    isActive: isActive ?? this.isActive,
    syncStatus: syncStatus ?? this.syncStatus,
  );
  UserTemporaryState copyWithCompanion(UserTemporaryStatesCompanion data) {
    return UserTemporaryState(
      id: data.id.present ? data.id.value : this.id,
      stateIdFromServer:
          data.stateIdFromServer.present
              ? data.stateIdFromServer.value
              : this.stateIdFromServer,
      userId: data.userId.present ? data.userId.value : this.userId,
      stateTag: data.stateTag.present ? data.stateTag.value : this.stateTag,
      stateDescription:
          data.stateDescription.present
              ? data.stateDescription.value
              : this.stateDescription,
      appliesFrom:
          data.appliesFrom.present ? data.appliesFrom.value : this.appliesFrom,
      appliesUntil:
          data.appliesUntil.present
              ? data.appliesUntil.value
              : this.appliesUntil,
      isActive: data.isActive.present ? data.isActive.value : this.isActive,
      syncStatus:
          data.syncStatus.present ? data.syncStatus.value : this.syncStatus,
    );
  }

  @override
  String toString() {
    return (StringBuffer('UserTemporaryState(')
          ..write('id: $id, ')
          ..write('stateIdFromServer: $stateIdFromServer, ')
          ..write('userId: $userId, ')
          ..write('stateTag: $stateTag, ')
          ..write('stateDescription: $stateDescription, ')
          ..write('appliesFrom: $appliesFrom, ')
          ..write('appliesUntil: $appliesUntil, ')
          ..write('isActive: $isActive, ')
          ..write('syncStatus: $syncStatus')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
    id,
    stateIdFromServer,
    userId,
    stateTag,
    stateDescription,
    appliesFrom,
    appliesUntil,
    isActive,
    syncStatus,
  );
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is UserTemporaryState &&
          other.id == this.id &&
          other.stateIdFromServer == this.stateIdFromServer &&
          other.userId == this.userId &&
          other.stateTag == this.stateTag &&
          other.stateDescription == this.stateDescription &&
          other.appliesFrom == this.appliesFrom &&
          other.appliesUntil == this.appliesUntil &&
          other.isActive == this.isActive &&
          other.syncStatus == this.syncStatus);
}

class UserTemporaryStatesCompanion extends UpdateCompanion<UserTemporaryState> {
  final Value<int> id;
  final Value<String?> stateIdFromServer;
  final Value<String> userId;
  final Value<String> stateTag;
  final Value<String?> stateDescription;
  final Value<DateTime> appliesFrom;
  final Value<DateTime?> appliesUntil;
  final Value<bool> isActive;
  final Value<String> syncStatus;
  const UserTemporaryStatesCompanion({
    this.id = const Value.absent(),
    this.stateIdFromServer = const Value.absent(),
    this.userId = const Value.absent(),
    this.stateTag = const Value.absent(),
    this.stateDescription = const Value.absent(),
    this.appliesFrom = const Value.absent(),
    this.appliesUntil = const Value.absent(),
    this.isActive = const Value.absent(),
    this.syncStatus = const Value.absent(),
  });
  UserTemporaryStatesCompanion.insert({
    this.id = const Value.absent(),
    this.stateIdFromServer = const Value.absent(),
    required String userId,
    required String stateTag,
    this.stateDescription = const Value.absent(),
    required DateTime appliesFrom,
    this.appliesUntil = const Value.absent(),
    this.isActive = const Value.absent(),
    this.syncStatus = const Value.absent(),
  }) : userId = Value(userId),
       stateTag = Value(stateTag),
       appliesFrom = Value(appliesFrom);
  static Insertable<UserTemporaryState> custom({
    Expression<int>? id,
    Expression<String>? stateIdFromServer,
    Expression<String>? userId,
    Expression<String>? stateTag,
    Expression<String>? stateDescription,
    Expression<DateTime>? appliesFrom,
    Expression<DateTime>? appliesUntil,
    Expression<bool>? isActive,
    Expression<String>? syncStatus,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (stateIdFromServer != null) 'state_id_from_server': stateIdFromServer,
      if (userId != null) 'user_id': userId,
      if (stateTag != null) 'state_tag': stateTag,
      if (stateDescription != null) 'state_description': stateDescription,
      if (appliesFrom != null) 'applies_from': appliesFrom,
      if (appliesUntil != null) 'applies_until': appliesUntil,
      if (isActive != null) 'is_active': isActive,
      if (syncStatus != null) 'sync_status': syncStatus,
    });
  }

  UserTemporaryStatesCompanion copyWith({
    Value<int>? id,
    Value<String?>? stateIdFromServer,
    Value<String>? userId,
    Value<String>? stateTag,
    Value<String?>? stateDescription,
    Value<DateTime>? appliesFrom,
    Value<DateTime?>? appliesUntil,
    Value<bool>? isActive,
    Value<String>? syncStatus,
  }) {
    return UserTemporaryStatesCompanion(
      id: id ?? this.id,
      stateIdFromServer: stateIdFromServer ?? this.stateIdFromServer,
      userId: userId ?? this.userId,
      stateTag: stateTag ?? this.stateTag,
      stateDescription: stateDescription ?? this.stateDescription,
      appliesFrom: appliesFrom ?? this.appliesFrom,
      appliesUntil: appliesUntil ?? this.appliesUntil,
      isActive: isActive ?? this.isActive,
      syncStatus: syncStatus ?? this.syncStatus,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (stateIdFromServer.present) {
      map['state_id_from_server'] = Variable<String>(stateIdFromServer.value);
    }
    if (userId.present) {
      map['user_id'] = Variable<String>(userId.value);
    }
    if (stateTag.present) {
      map['state_tag'] = Variable<String>(stateTag.value);
    }
    if (stateDescription.present) {
      map['state_description'] = Variable<String>(stateDescription.value);
    }
    if (appliesFrom.present) {
      map['applies_from'] = Variable<DateTime>(appliesFrom.value);
    }
    if (appliesUntil.present) {
      map['applies_until'] = Variable<DateTime>(appliesUntil.value);
    }
    if (isActive.present) {
      map['is_active'] = Variable<bool>(isActive.value);
    }
    if (syncStatus.present) {
      map['sync_status'] = Variable<String>(syncStatus.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('UserTemporaryStatesCompanion(')
          ..write('id: $id, ')
          ..write('stateIdFromServer: $stateIdFromServer, ')
          ..write('userId: $userId, ')
          ..write('stateTag: $stateTag, ')
          ..write('stateDescription: $stateDescription, ')
          ..write('appliesFrom: $appliesFrom, ')
          ..write('appliesUntil: $appliesUntil, ')
          ..write('isActive: $isActive, ')
          ..write('syncStatus: $syncStatus')
          ..write(')'))
        .toString();
  }
}

abstract class _$AppDatabase extends GeneratedDatabase {
  _$AppDatabase(QueryExecutor e) : super(e);
  $AppDatabaseManager get managers => $AppDatabaseManager(this);
  late final $LocalUsersTable localUsers = $LocalUsersTable(this);
  late final $LocalUserProfilesTable localUserProfiles =
      $LocalUserProfilesTable(this);
  late final $LocalRecipesTable localRecipes = $LocalRecipesTable(this);
  late final $LocalIngredientsTable localIngredients = $LocalIngredientsTable(
    this,
  );
  late final $LocalRecipeIngredientsTable localRecipeIngredients =
      $LocalRecipeIngredientsTable(this);
  late final $MealPlansTable mealPlans = $MealPlansTable(this);
  late final $ShoppingListItemsTable shoppingListItems =
      $ShoppingListItemsTable(this);
  late final $ImageScanHistoriesTable imageScanHistories =
      $ImageScanHistoriesTable(this);
  late final $UserTemporaryStatesTable userTemporaryStates =
      $UserTemporaryStatesTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [
    localUsers,
    localUserProfiles,
    localRecipes,
    localIngredients,
    localRecipeIngredients,
    mealPlans,
    shoppingListItems,
    imageScanHistories,
    userTemporaryStates,
  ];
  @override
  DriftDatabaseOptions get options =>
      const DriftDatabaseOptions(storeDateTimeAsText: true);
}

typedef $$LocalUsersTableCreateCompanionBuilder =
    LocalUsersCompanion Function({
      required String id,
      required String email,
      Value<String?> displayName,
      Value<String?> photoUrl,
      Value<String?> referralCode,
      required DateTime lastSyncedAt,
      Value<int> rowid,
    });
typedef $$LocalUsersTableUpdateCompanionBuilder =
    LocalUsersCompanion Function({
      Value<String> id,
      Value<String> email,
      Value<String?> displayName,
      Value<String?> photoUrl,
      Value<String?> referralCode,
      Value<DateTime> lastSyncedAt,
      Value<int> rowid,
    });

class $$LocalUsersTableFilterComposer
    extends Composer<_$AppDatabase, $LocalUsersTable> {
  $$LocalUsersTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get email => $composableBuilder(
    column: $table.email,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get displayName => $composableBuilder(
    column: $table.displayName,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get photoUrl => $composableBuilder(
    column: $table.photoUrl,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get referralCode => $composableBuilder(
    column: $table.referralCode,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get lastSyncedAt => $composableBuilder(
    column: $table.lastSyncedAt,
    builder: (column) => ColumnFilters(column),
  );
}

class $$LocalUsersTableOrderingComposer
    extends Composer<_$AppDatabase, $LocalUsersTable> {
  $$LocalUsersTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get email => $composableBuilder(
    column: $table.email,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get displayName => $composableBuilder(
    column: $table.displayName,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get photoUrl => $composableBuilder(
    column: $table.photoUrl,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get referralCode => $composableBuilder(
    column: $table.referralCode,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get lastSyncedAt => $composableBuilder(
    column: $table.lastSyncedAt,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$LocalUsersTableAnnotationComposer
    extends Composer<_$AppDatabase, $LocalUsersTable> {
  $$LocalUsersTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get email =>
      $composableBuilder(column: $table.email, builder: (column) => column);

  GeneratedColumn<String> get displayName => $composableBuilder(
    column: $table.displayName,
    builder: (column) => column,
  );

  GeneratedColumn<String> get photoUrl =>
      $composableBuilder(column: $table.photoUrl, builder: (column) => column);

  GeneratedColumn<String> get referralCode => $composableBuilder(
    column: $table.referralCode,
    builder: (column) => column,
  );

  GeneratedColumn<DateTime> get lastSyncedAt => $composableBuilder(
    column: $table.lastSyncedAt,
    builder: (column) => column,
  );
}

class $$LocalUsersTableTableManager
    extends
        RootTableManager<
          _$AppDatabase,
          $LocalUsersTable,
          LocalUser,
          $$LocalUsersTableFilterComposer,
          $$LocalUsersTableOrderingComposer,
          $$LocalUsersTableAnnotationComposer,
          $$LocalUsersTableCreateCompanionBuilder,
          $$LocalUsersTableUpdateCompanionBuilder,
          (
            LocalUser,
            BaseReferences<_$AppDatabase, $LocalUsersTable, LocalUser>,
          ),
          LocalUser,
          PrefetchHooks Function()
        > {
  $$LocalUsersTableTableManager(_$AppDatabase db, $LocalUsersTable table)
    : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer:
              () => $$LocalUsersTableFilterComposer($db: db, $table: table),
          createOrderingComposer:
              () => $$LocalUsersTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer:
              () => $$LocalUsersTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback:
              ({
                Value<String> id = const Value.absent(),
                Value<String> email = const Value.absent(),
                Value<String?> displayName = const Value.absent(),
                Value<String?> photoUrl = const Value.absent(),
                Value<String?> referralCode = const Value.absent(),
                Value<DateTime> lastSyncedAt = const Value.absent(),
                Value<int> rowid = const Value.absent(),
              }) => LocalUsersCompanion(
                id: id,
                email: email,
                displayName: displayName,
                photoUrl: photoUrl,
                referralCode: referralCode,
                lastSyncedAt: lastSyncedAt,
                rowid: rowid,
              ),
          createCompanionCallback:
              ({
                required String id,
                required String email,
                Value<String?> displayName = const Value.absent(),
                Value<String?> photoUrl = const Value.absent(),
                Value<String?> referralCode = const Value.absent(),
                required DateTime lastSyncedAt,
                Value<int> rowid = const Value.absent(),
              }) => LocalUsersCompanion.insert(
                id: id,
                email: email,
                displayName: displayName,
                photoUrl: photoUrl,
                referralCode: referralCode,
                lastSyncedAt: lastSyncedAt,
                rowid: rowid,
              ),
          withReferenceMapper:
              (p0) =>
                  p0
                      .map(
                        (e) => (
                          e.readTable(table),
                          BaseReferences(db, table, e),
                        ),
                      )
                      .toList(),
          prefetchHooksCallback: null,
        ),
      );
}

typedef $$LocalUsersTableProcessedTableManager =
    ProcessedTableManager<
      _$AppDatabase,
      $LocalUsersTable,
      LocalUser,
      $$LocalUsersTableFilterComposer,
      $$LocalUsersTableOrderingComposer,
      $$LocalUsersTableAnnotationComposer,
      $$LocalUsersTableCreateCompanionBuilder,
      $$LocalUsersTableUpdateCompanionBuilder,
      (LocalUser, BaseReferences<_$AppDatabase, $LocalUsersTable, LocalUser>),
      LocalUser,
      PrefetchHooks Function()
    >;
typedef $$LocalUserProfilesTableCreateCompanionBuilder =
    LocalUserProfilesCompanion Function({
      required String userId,
      required DateTime updatedAt,
      Value<DateTime?> dateOfBirth,
      Value<String?> gender,
      Value<double?> weightKg,
      Value<double?> heightCm,
      Value<String?> activityLevel,
      Value<String?> healthGoals,
      Value<String?> foodAllergies,
      Value<String?> medicalConditions,
      Value<bool> isPregnantOrBreastfeeding,
      Value<String?> spiceLevelPreference,
      Value<String?> favoriteFoodsIngredients,
      Value<String?> dislikedFoodsIngredients,
      Value<String?> texturePreferences,
      Value<String?> countryRegionOfResidence,
      Value<String?> preferredCuisines,
      Value<String?> eatingLifestyle,
      Value<String?> selectedMealTypes,
      Value<String?> cookingSkillLevel,
      Value<int?> avgTimeForMainMealPrepCookMinutes,
      Value<String?> availableKitchenEquipment,
      Value<int> rowid,
    });
typedef $$LocalUserProfilesTableUpdateCompanionBuilder =
    LocalUserProfilesCompanion Function({
      Value<String> userId,
      Value<DateTime> updatedAt,
      Value<DateTime?> dateOfBirth,
      Value<String?> gender,
      Value<double?> weightKg,
      Value<double?> heightCm,
      Value<String?> activityLevel,
      Value<String?> healthGoals,
      Value<String?> foodAllergies,
      Value<String?> medicalConditions,
      Value<bool> isPregnantOrBreastfeeding,
      Value<String?> spiceLevelPreference,
      Value<String?> favoriteFoodsIngredients,
      Value<String?> dislikedFoodsIngredients,
      Value<String?> texturePreferences,
      Value<String?> countryRegionOfResidence,
      Value<String?> preferredCuisines,
      Value<String?> eatingLifestyle,
      Value<String?> selectedMealTypes,
      Value<String?> cookingSkillLevel,
      Value<int?> avgTimeForMainMealPrepCookMinutes,
      Value<String?> availableKitchenEquipment,
      Value<int> rowid,
    });

class $$LocalUserProfilesTableFilterComposer
    extends Composer<_$AppDatabase, $LocalUserProfilesTable> {
  $$LocalUserProfilesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get userId => $composableBuilder(
    column: $table.userId,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
    column: $table.updatedAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get dateOfBirth => $composableBuilder(
    column: $table.dateOfBirth,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get gender => $composableBuilder(
    column: $table.gender,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get weightKg => $composableBuilder(
    column: $table.weightKg,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get heightCm => $composableBuilder(
    column: $table.heightCm,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get activityLevel => $composableBuilder(
    column: $table.activityLevel,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get healthGoals => $composableBuilder(
    column: $table.healthGoals,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get foodAllergies => $composableBuilder(
    column: $table.foodAllergies,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get medicalConditions => $composableBuilder(
    column: $table.medicalConditions,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<bool> get isPregnantOrBreastfeeding => $composableBuilder(
    column: $table.isPregnantOrBreastfeeding,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get spiceLevelPreference => $composableBuilder(
    column: $table.spiceLevelPreference,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get favoriteFoodsIngredients => $composableBuilder(
    column: $table.favoriteFoodsIngredients,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get dislikedFoodsIngredients => $composableBuilder(
    column: $table.dislikedFoodsIngredients,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get texturePreferences => $composableBuilder(
    column: $table.texturePreferences,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get countryRegionOfResidence => $composableBuilder(
    column: $table.countryRegionOfResidence,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get preferredCuisines => $composableBuilder(
    column: $table.preferredCuisines,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get eatingLifestyle => $composableBuilder(
    column: $table.eatingLifestyle,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get selectedMealTypes => $composableBuilder(
    column: $table.selectedMealTypes,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get cookingSkillLevel => $composableBuilder(
    column: $table.cookingSkillLevel,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get avgTimeForMainMealPrepCookMinutes =>
      $composableBuilder(
        column: $table.avgTimeForMainMealPrepCookMinutes,
        builder: (column) => ColumnFilters(column),
      );

  ColumnFilters<String> get availableKitchenEquipment => $composableBuilder(
    column: $table.availableKitchenEquipment,
    builder: (column) => ColumnFilters(column),
  );
}

class $$LocalUserProfilesTableOrderingComposer
    extends Composer<_$AppDatabase, $LocalUserProfilesTable> {
  $$LocalUserProfilesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get userId => $composableBuilder(
    column: $table.userId,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
    column: $table.updatedAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get dateOfBirth => $composableBuilder(
    column: $table.dateOfBirth,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get gender => $composableBuilder(
    column: $table.gender,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get weightKg => $composableBuilder(
    column: $table.weightKg,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get heightCm => $composableBuilder(
    column: $table.heightCm,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get activityLevel => $composableBuilder(
    column: $table.activityLevel,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get healthGoals => $composableBuilder(
    column: $table.healthGoals,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get foodAllergies => $composableBuilder(
    column: $table.foodAllergies,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get medicalConditions => $composableBuilder(
    column: $table.medicalConditions,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<bool> get isPregnantOrBreastfeeding => $composableBuilder(
    column: $table.isPregnantOrBreastfeeding,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get spiceLevelPreference => $composableBuilder(
    column: $table.spiceLevelPreference,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get favoriteFoodsIngredients => $composableBuilder(
    column: $table.favoriteFoodsIngredients,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get dislikedFoodsIngredients => $composableBuilder(
    column: $table.dislikedFoodsIngredients,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get texturePreferences => $composableBuilder(
    column: $table.texturePreferences,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get countryRegionOfResidence => $composableBuilder(
    column: $table.countryRegionOfResidence,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get preferredCuisines => $composableBuilder(
    column: $table.preferredCuisines,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get eatingLifestyle => $composableBuilder(
    column: $table.eatingLifestyle,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get selectedMealTypes => $composableBuilder(
    column: $table.selectedMealTypes,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get cookingSkillLevel => $composableBuilder(
    column: $table.cookingSkillLevel,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get avgTimeForMainMealPrepCookMinutes =>
      $composableBuilder(
        column: $table.avgTimeForMainMealPrepCookMinutes,
        builder: (column) => ColumnOrderings(column),
      );

  ColumnOrderings<String> get availableKitchenEquipment => $composableBuilder(
    column: $table.availableKitchenEquipment,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$LocalUserProfilesTableAnnotationComposer
    extends Composer<_$AppDatabase, $LocalUserProfilesTable> {
  $$LocalUserProfilesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get userId =>
      $composableBuilder(column: $table.userId, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  GeneratedColumn<DateTime> get dateOfBirth => $composableBuilder(
    column: $table.dateOfBirth,
    builder: (column) => column,
  );

  GeneratedColumn<String> get gender =>
      $composableBuilder(column: $table.gender, builder: (column) => column);

  GeneratedColumn<double> get weightKg =>
      $composableBuilder(column: $table.weightKg, builder: (column) => column);

  GeneratedColumn<double> get heightCm =>
      $composableBuilder(column: $table.heightCm, builder: (column) => column);

  GeneratedColumn<String> get activityLevel => $composableBuilder(
    column: $table.activityLevel,
    builder: (column) => column,
  );

  GeneratedColumn<String> get healthGoals => $composableBuilder(
    column: $table.healthGoals,
    builder: (column) => column,
  );

  GeneratedColumn<String> get foodAllergies => $composableBuilder(
    column: $table.foodAllergies,
    builder: (column) => column,
  );

  GeneratedColumn<String> get medicalConditions => $composableBuilder(
    column: $table.medicalConditions,
    builder: (column) => column,
  );

  GeneratedColumn<bool> get isPregnantOrBreastfeeding => $composableBuilder(
    column: $table.isPregnantOrBreastfeeding,
    builder: (column) => column,
  );

  GeneratedColumn<String> get spiceLevelPreference => $composableBuilder(
    column: $table.spiceLevelPreference,
    builder: (column) => column,
  );

  GeneratedColumn<String> get favoriteFoodsIngredients => $composableBuilder(
    column: $table.favoriteFoodsIngredients,
    builder: (column) => column,
  );

  GeneratedColumn<String> get dislikedFoodsIngredients => $composableBuilder(
    column: $table.dislikedFoodsIngredients,
    builder: (column) => column,
  );

  GeneratedColumn<String> get texturePreferences => $composableBuilder(
    column: $table.texturePreferences,
    builder: (column) => column,
  );

  GeneratedColumn<String> get countryRegionOfResidence => $composableBuilder(
    column: $table.countryRegionOfResidence,
    builder: (column) => column,
  );

  GeneratedColumn<String> get preferredCuisines => $composableBuilder(
    column: $table.preferredCuisines,
    builder: (column) => column,
  );

  GeneratedColumn<String> get eatingLifestyle => $composableBuilder(
    column: $table.eatingLifestyle,
    builder: (column) => column,
  );

  GeneratedColumn<String> get selectedMealTypes => $composableBuilder(
    column: $table.selectedMealTypes,
    builder: (column) => column,
  );

  GeneratedColumn<String> get cookingSkillLevel => $composableBuilder(
    column: $table.cookingSkillLevel,
    builder: (column) => column,
  );

  GeneratedColumn<int> get avgTimeForMainMealPrepCookMinutes =>
      $composableBuilder(
        column: $table.avgTimeForMainMealPrepCookMinutes,
        builder: (column) => column,
      );

  GeneratedColumn<String> get availableKitchenEquipment => $composableBuilder(
    column: $table.availableKitchenEquipment,
    builder: (column) => column,
  );
}

class $$LocalUserProfilesTableTableManager
    extends
        RootTableManager<
          _$AppDatabase,
          $LocalUserProfilesTable,
          LocalUserProfile,
          $$LocalUserProfilesTableFilterComposer,
          $$LocalUserProfilesTableOrderingComposer,
          $$LocalUserProfilesTableAnnotationComposer,
          $$LocalUserProfilesTableCreateCompanionBuilder,
          $$LocalUserProfilesTableUpdateCompanionBuilder,
          (
            LocalUserProfile,
            BaseReferences<
              _$AppDatabase,
              $LocalUserProfilesTable,
              LocalUserProfile
            >,
          ),
          LocalUserProfile,
          PrefetchHooks Function()
        > {
  $$LocalUserProfilesTableTableManager(
    _$AppDatabase db,
    $LocalUserProfilesTable table,
  ) : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer:
              () => $$LocalUserProfilesTableFilterComposer(
                $db: db,
                $table: table,
              ),
          createOrderingComposer:
              () => $$LocalUserProfilesTableOrderingComposer(
                $db: db,
                $table: table,
              ),
          createComputedFieldComposer:
              () => $$LocalUserProfilesTableAnnotationComposer(
                $db: db,
                $table: table,
              ),
          updateCompanionCallback:
              ({
                Value<String> userId = const Value.absent(),
                Value<DateTime> updatedAt = const Value.absent(),
                Value<DateTime?> dateOfBirth = const Value.absent(),
                Value<String?> gender = const Value.absent(),
                Value<double?> weightKg = const Value.absent(),
                Value<double?> heightCm = const Value.absent(),
                Value<String?> activityLevel = const Value.absent(),
                Value<String?> healthGoals = const Value.absent(),
                Value<String?> foodAllergies = const Value.absent(),
                Value<String?> medicalConditions = const Value.absent(),
                Value<bool> isPregnantOrBreastfeeding = const Value.absent(),
                Value<String?> spiceLevelPreference = const Value.absent(),
                Value<String?> favoriteFoodsIngredients = const Value.absent(),
                Value<String?> dislikedFoodsIngredients = const Value.absent(),
                Value<String?> texturePreferences = const Value.absent(),
                Value<String?> countryRegionOfResidence = const Value.absent(),
                Value<String?> preferredCuisines = const Value.absent(),
                Value<String?> eatingLifestyle = const Value.absent(),
                Value<String?> selectedMealTypes = const Value.absent(),
                Value<String?> cookingSkillLevel = const Value.absent(),
                Value<int?> avgTimeForMainMealPrepCookMinutes =
                    const Value.absent(),
                Value<String?> availableKitchenEquipment = const Value.absent(),
                Value<int> rowid = const Value.absent(),
              }) => LocalUserProfilesCompanion(
                userId: userId,
                updatedAt: updatedAt,
                dateOfBirth: dateOfBirth,
                gender: gender,
                weightKg: weightKg,
                heightCm: heightCm,
                activityLevel: activityLevel,
                healthGoals: healthGoals,
                foodAllergies: foodAllergies,
                medicalConditions: medicalConditions,
                isPregnantOrBreastfeeding: isPregnantOrBreastfeeding,
                spiceLevelPreference: spiceLevelPreference,
                favoriteFoodsIngredients: favoriteFoodsIngredients,
                dislikedFoodsIngredients: dislikedFoodsIngredients,
                texturePreferences: texturePreferences,
                countryRegionOfResidence: countryRegionOfResidence,
                preferredCuisines: preferredCuisines,
                eatingLifestyle: eatingLifestyle,
                selectedMealTypes: selectedMealTypes,
                cookingSkillLevel: cookingSkillLevel,
                avgTimeForMainMealPrepCookMinutes:
                    avgTimeForMainMealPrepCookMinutes,
                availableKitchenEquipment: availableKitchenEquipment,
                rowid: rowid,
              ),
          createCompanionCallback:
              ({
                required String userId,
                required DateTime updatedAt,
                Value<DateTime?> dateOfBirth = const Value.absent(),
                Value<String?> gender = const Value.absent(),
                Value<double?> weightKg = const Value.absent(),
                Value<double?> heightCm = const Value.absent(),
                Value<String?> activityLevel = const Value.absent(),
                Value<String?> healthGoals = const Value.absent(),
                Value<String?> foodAllergies = const Value.absent(),
                Value<String?> medicalConditions = const Value.absent(),
                Value<bool> isPregnantOrBreastfeeding = const Value.absent(),
                Value<String?> spiceLevelPreference = const Value.absent(),
                Value<String?> favoriteFoodsIngredients = const Value.absent(),
                Value<String?> dislikedFoodsIngredients = const Value.absent(),
                Value<String?> texturePreferences = const Value.absent(),
                Value<String?> countryRegionOfResidence = const Value.absent(),
                Value<String?> preferredCuisines = const Value.absent(),
                Value<String?> eatingLifestyle = const Value.absent(),
                Value<String?> selectedMealTypes = const Value.absent(),
                Value<String?> cookingSkillLevel = const Value.absent(),
                Value<int?> avgTimeForMainMealPrepCookMinutes =
                    const Value.absent(),
                Value<String?> availableKitchenEquipment = const Value.absent(),
                Value<int> rowid = const Value.absent(),
              }) => LocalUserProfilesCompanion.insert(
                userId: userId,
                updatedAt: updatedAt,
                dateOfBirth: dateOfBirth,
                gender: gender,
                weightKg: weightKg,
                heightCm: heightCm,
                activityLevel: activityLevel,
                healthGoals: healthGoals,
                foodAllergies: foodAllergies,
                medicalConditions: medicalConditions,
                isPregnantOrBreastfeeding: isPregnantOrBreastfeeding,
                spiceLevelPreference: spiceLevelPreference,
                favoriteFoodsIngredients: favoriteFoodsIngredients,
                dislikedFoodsIngredients: dislikedFoodsIngredients,
                texturePreferences: texturePreferences,
                countryRegionOfResidence: countryRegionOfResidence,
                preferredCuisines: preferredCuisines,
                eatingLifestyle: eatingLifestyle,
                selectedMealTypes: selectedMealTypes,
                cookingSkillLevel: cookingSkillLevel,
                avgTimeForMainMealPrepCookMinutes:
                    avgTimeForMainMealPrepCookMinutes,
                availableKitchenEquipment: availableKitchenEquipment,
                rowid: rowid,
              ),
          withReferenceMapper:
              (p0) =>
                  p0
                      .map(
                        (e) => (
                          e.readTable(table),
                          BaseReferences(db, table, e),
                        ),
                      )
                      .toList(),
          prefetchHooksCallback: null,
        ),
      );
}

typedef $$LocalUserProfilesTableProcessedTableManager =
    ProcessedTableManager<
      _$AppDatabase,
      $LocalUserProfilesTable,
      LocalUserProfile,
      $$LocalUserProfilesTableFilterComposer,
      $$LocalUserProfilesTableOrderingComposer,
      $$LocalUserProfilesTableAnnotationComposer,
      $$LocalUserProfilesTableCreateCompanionBuilder,
      $$LocalUserProfilesTableUpdateCompanionBuilder,
      (
        LocalUserProfile,
        BaseReferences<
          _$AppDatabase,
          $LocalUserProfilesTable,
          LocalUserProfile
        >,
      ),
      LocalUserProfile,
      PrefetchHooks Function()
    >;
typedef $$LocalRecipesTableCreateCompanionBuilder =
    LocalRecipesCompanion Function({
      required String id,
      required String name,
      Value<String?> description,
      required String instructions,
      Value<String?> imageUrl,
      required String source,
      Value<bool> isFavorite,
      Value<int?> prepTimeMinutes,
      Value<int?> cookTimeMinutes,
      required int servings,
      Value<int?> caloriesPerServing,
      Value<double?> proteinGPerServing,
      Value<double?> carbsGPerServing,
      Value<double?> fatGPerServing,
      Value<String?> totalDishNutrientSummary,
      Value<String?> dishEffectsAndNotes,
      Value<String?> totalDishHeartHealthIndicatorsJson,
      Value<String?> totalDishVitaminsJson,
      Value<String?> totalDishMineralsJson,
      Value<int> rowid,
    });
typedef $$LocalRecipesTableUpdateCompanionBuilder =
    LocalRecipesCompanion Function({
      Value<String> id,
      Value<String> name,
      Value<String?> description,
      Value<String> instructions,
      Value<String?> imageUrl,
      Value<String> source,
      Value<bool> isFavorite,
      Value<int?> prepTimeMinutes,
      Value<int?> cookTimeMinutes,
      Value<int> servings,
      Value<int?> caloriesPerServing,
      Value<double?> proteinGPerServing,
      Value<double?> carbsGPerServing,
      Value<double?> fatGPerServing,
      Value<String?> totalDishNutrientSummary,
      Value<String?> dishEffectsAndNotes,
      Value<String?> totalDishHeartHealthIndicatorsJson,
      Value<String?> totalDishVitaminsJson,
      Value<String?> totalDishMineralsJson,
      Value<int> rowid,
    });

class $$LocalRecipesTableFilterComposer
    extends Composer<_$AppDatabase, $LocalRecipesTable> {
  $$LocalRecipesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get name => $composableBuilder(
    column: $table.name,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get description => $composableBuilder(
    column: $table.description,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get instructions => $composableBuilder(
    column: $table.instructions,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get imageUrl => $composableBuilder(
    column: $table.imageUrl,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get source => $composableBuilder(
    column: $table.source,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<bool> get isFavorite => $composableBuilder(
    column: $table.isFavorite,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get prepTimeMinutes => $composableBuilder(
    column: $table.prepTimeMinutes,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get cookTimeMinutes => $composableBuilder(
    column: $table.cookTimeMinutes,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get servings => $composableBuilder(
    column: $table.servings,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get caloriesPerServing => $composableBuilder(
    column: $table.caloriesPerServing,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get proteinGPerServing => $composableBuilder(
    column: $table.proteinGPerServing,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get carbsGPerServing => $composableBuilder(
    column: $table.carbsGPerServing,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get fatGPerServing => $composableBuilder(
    column: $table.fatGPerServing,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get totalDishNutrientSummary => $composableBuilder(
    column: $table.totalDishNutrientSummary,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get dishEffectsAndNotes => $composableBuilder(
    column: $table.dishEffectsAndNotes,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get totalDishHeartHealthIndicatorsJson =>
      $composableBuilder(
        column: $table.totalDishHeartHealthIndicatorsJson,
        builder: (column) => ColumnFilters(column),
      );

  ColumnFilters<String> get totalDishVitaminsJson => $composableBuilder(
    column: $table.totalDishVitaminsJson,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get totalDishMineralsJson => $composableBuilder(
    column: $table.totalDishMineralsJson,
    builder: (column) => ColumnFilters(column),
  );
}

class $$LocalRecipesTableOrderingComposer
    extends Composer<_$AppDatabase, $LocalRecipesTable> {
  $$LocalRecipesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get name => $composableBuilder(
    column: $table.name,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get description => $composableBuilder(
    column: $table.description,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get instructions => $composableBuilder(
    column: $table.instructions,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get imageUrl => $composableBuilder(
    column: $table.imageUrl,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get source => $composableBuilder(
    column: $table.source,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<bool> get isFavorite => $composableBuilder(
    column: $table.isFavorite,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get prepTimeMinutes => $composableBuilder(
    column: $table.prepTimeMinutes,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get cookTimeMinutes => $composableBuilder(
    column: $table.cookTimeMinutes,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get servings => $composableBuilder(
    column: $table.servings,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get caloriesPerServing => $composableBuilder(
    column: $table.caloriesPerServing,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get proteinGPerServing => $composableBuilder(
    column: $table.proteinGPerServing,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get carbsGPerServing => $composableBuilder(
    column: $table.carbsGPerServing,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get fatGPerServing => $composableBuilder(
    column: $table.fatGPerServing,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get totalDishNutrientSummary => $composableBuilder(
    column: $table.totalDishNutrientSummary,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get dishEffectsAndNotes => $composableBuilder(
    column: $table.dishEffectsAndNotes,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get totalDishHeartHealthIndicatorsJson =>
      $composableBuilder(
        column: $table.totalDishHeartHealthIndicatorsJson,
        builder: (column) => ColumnOrderings(column),
      );

  ColumnOrderings<String> get totalDishVitaminsJson => $composableBuilder(
    column: $table.totalDishVitaminsJson,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get totalDishMineralsJson => $composableBuilder(
    column: $table.totalDishMineralsJson,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$LocalRecipesTableAnnotationComposer
    extends Composer<_$AppDatabase, $LocalRecipesTable> {
  $$LocalRecipesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumn<String> get description => $composableBuilder(
    column: $table.description,
    builder: (column) => column,
  );

  GeneratedColumn<String> get instructions => $composableBuilder(
    column: $table.instructions,
    builder: (column) => column,
  );

  GeneratedColumn<String> get imageUrl =>
      $composableBuilder(column: $table.imageUrl, builder: (column) => column);

  GeneratedColumn<String> get source =>
      $composableBuilder(column: $table.source, builder: (column) => column);

  GeneratedColumn<bool> get isFavorite => $composableBuilder(
    column: $table.isFavorite,
    builder: (column) => column,
  );

  GeneratedColumn<int> get prepTimeMinutes => $composableBuilder(
    column: $table.prepTimeMinutes,
    builder: (column) => column,
  );

  GeneratedColumn<int> get cookTimeMinutes => $composableBuilder(
    column: $table.cookTimeMinutes,
    builder: (column) => column,
  );

  GeneratedColumn<int> get servings =>
      $composableBuilder(column: $table.servings, builder: (column) => column);

  GeneratedColumn<int> get caloriesPerServing => $composableBuilder(
    column: $table.caloriesPerServing,
    builder: (column) => column,
  );

  GeneratedColumn<double> get proteinGPerServing => $composableBuilder(
    column: $table.proteinGPerServing,
    builder: (column) => column,
  );

  GeneratedColumn<double> get carbsGPerServing => $composableBuilder(
    column: $table.carbsGPerServing,
    builder: (column) => column,
  );

  GeneratedColumn<double> get fatGPerServing => $composableBuilder(
    column: $table.fatGPerServing,
    builder: (column) => column,
  );

  GeneratedColumn<String> get totalDishNutrientSummary => $composableBuilder(
    column: $table.totalDishNutrientSummary,
    builder: (column) => column,
  );

  GeneratedColumn<String> get dishEffectsAndNotes => $composableBuilder(
    column: $table.dishEffectsAndNotes,
    builder: (column) => column,
  );

  GeneratedColumn<String> get totalDishHeartHealthIndicatorsJson =>
      $composableBuilder(
        column: $table.totalDishHeartHealthIndicatorsJson,
        builder: (column) => column,
      );

  GeneratedColumn<String> get totalDishVitaminsJson => $composableBuilder(
    column: $table.totalDishVitaminsJson,
    builder: (column) => column,
  );

  GeneratedColumn<String> get totalDishMineralsJson => $composableBuilder(
    column: $table.totalDishMineralsJson,
    builder: (column) => column,
  );
}

class $$LocalRecipesTableTableManager
    extends
        RootTableManager<
          _$AppDatabase,
          $LocalRecipesTable,
          LocalRecipe,
          $$LocalRecipesTableFilterComposer,
          $$LocalRecipesTableOrderingComposer,
          $$LocalRecipesTableAnnotationComposer,
          $$LocalRecipesTableCreateCompanionBuilder,
          $$LocalRecipesTableUpdateCompanionBuilder,
          (
            LocalRecipe,
            BaseReferences<_$AppDatabase, $LocalRecipesTable, LocalRecipe>,
          ),
          LocalRecipe,
          PrefetchHooks Function()
        > {
  $$LocalRecipesTableTableManager(_$AppDatabase db, $LocalRecipesTable table)
    : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer:
              () => $$LocalRecipesTableFilterComposer($db: db, $table: table),
          createOrderingComposer:
              () => $$LocalRecipesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer:
              () =>
                  $$LocalRecipesTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback:
              ({
                Value<String> id = const Value.absent(),
                Value<String> name = const Value.absent(),
                Value<String?> description = const Value.absent(),
                Value<String> instructions = const Value.absent(),
                Value<String?> imageUrl = const Value.absent(),
                Value<String> source = const Value.absent(),
                Value<bool> isFavorite = const Value.absent(),
                Value<int?> prepTimeMinutes = const Value.absent(),
                Value<int?> cookTimeMinutes = const Value.absent(),
                Value<int> servings = const Value.absent(),
                Value<int?> caloriesPerServing = const Value.absent(),
                Value<double?> proteinGPerServing = const Value.absent(),
                Value<double?> carbsGPerServing = const Value.absent(),
                Value<double?> fatGPerServing = const Value.absent(),
                Value<String?> totalDishNutrientSummary = const Value.absent(),
                Value<String?> dishEffectsAndNotes = const Value.absent(),
                Value<String?> totalDishHeartHealthIndicatorsJson =
                    const Value.absent(),
                Value<String?> totalDishVitaminsJson = const Value.absent(),
                Value<String?> totalDishMineralsJson = const Value.absent(),
                Value<int> rowid = const Value.absent(),
              }) => LocalRecipesCompanion(
                id: id,
                name: name,
                description: description,
                instructions: instructions,
                imageUrl: imageUrl,
                source: source,
                isFavorite: isFavorite,
                prepTimeMinutes: prepTimeMinutes,
                cookTimeMinutes: cookTimeMinutes,
                servings: servings,
                caloriesPerServing: caloriesPerServing,
                proteinGPerServing: proteinGPerServing,
                carbsGPerServing: carbsGPerServing,
                fatGPerServing: fatGPerServing,
                totalDishNutrientSummary: totalDishNutrientSummary,
                dishEffectsAndNotes: dishEffectsAndNotes,
                totalDishHeartHealthIndicatorsJson:
                    totalDishHeartHealthIndicatorsJson,
                totalDishVitaminsJson: totalDishVitaminsJson,
                totalDishMineralsJson: totalDishMineralsJson,
                rowid: rowid,
              ),
          createCompanionCallback:
              ({
                required String id,
                required String name,
                Value<String?> description = const Value.absent(),
                required String instructions,
                Value<String?> imageUrl = const Value.absent(),
                required String source,
                Value<bool> isFavorite = const Value.absent(),
                Value<int?> prepTimeMinutes = const Value.absent(),
                Value<int?> cookTimeMinutes = const Value.absent(),
                required int servings,
                Value<int?> caloriesPerServing = const Value.absent(),
                Value<double?> proteinGPerServing = const Value.absent(),
                Value<double?> carbsGPerServing = const Value.absent(),
                Value<double?> fatGPerServing = const Value.absent(),
                Value<String?> totalDishNutrientSummary = const Value.absent(),
                Value<String?> dishEffectsAndNotes = const Value.absent(),
                Value<String?> totalDishHeartHealthIndicatorsJson =
                    const Value.absent(),
                Value<String?> totalDishVitaminsJson = const Value.absent(),
                Value<String?> totalDishMineralsJson = const Value.absent(),
                Value<int> rowid = const Value.absent(),
              }) => LocalRecipesCompanion.insert(
                id: id,
                name: name,
                description: description,
                instructions: instructions,
                imageUrl: imageUrl,
                source: source,
                isFavorite: isFavorite,
                prepTimeMinutes: prepTimeMinutes,
                cookTimeMinutes: cookTimeMinutes,
                servings: servings,
                caloriesPerServing: caloriesPerServing,
                proteinGPerServing: proteinGPerServing,
                carbsGPerServing: carbsGPerServing,
                fatGPerServing: fatGPerServing,
                totalDishNutrientSummary: totalDishNutrientSummary,
                dishEffectsAndNotes: dishEffectsAndNotes,
                totalDishHeartHealthIndicatorsJson:
                    totalDishHeartHealthIndicatorsJson,
                totalDishVitaminsJson: totalDishVitaminsJson,
                totalDishMineralsJson: totalDishMineralsJson,
                rowid: rowid,
              ),
          withReferenceMapper:
              (p0) =>
                  p0
                      .map(
                        (e) => (
                          e.readTable(table),
                          BaseReferences(db, table, e),
                        ),
                      )
                      .toList(),
          prefetchHooksCallback: null,
        ),
      );
}

typedef $$LocalRecipesTableProcessedTableManager =
    ProcessedTableManager<
      _$AppDatabase,
      $LocalRecipesTable,
      LocalRecipe,
      $$LocalRecipesTableFilterComposer,
      $$LocalRecipesTableOrderingComposer,
      $$LocalRecipesTableAnnotationComposer,
      $$LocalRecipesTableCreateCompanionBuilder,
      $$LocalRecipesTableUpdateCompanionBuilder,
      (
        LocalRecipe,
        BaseReferences<_$AppDatabase, $LocalRecipesTable, LocalRecipe>,
      ),
      LocalRecipe,
      PrefetchHooks Function()
    >;
typedef $$LocalIngredientsTableCreateCompanionBuilder =
    LocalIngredientsCompanion Function({
      required String id,
      required String name,
      required String defaultUnit,
      Value<int> rowid,
    });
typedef $$LocalIngredientsTableUpdateCompanionBuilder =
    LocalIngredientsCompanion Function({
      Value<String> id,
      Value<String> name,
      Value<String> defaultUnit,
      Value<int> rowid,
    });

class $$LocalIngredientsTableFilterComposer
    extends Composer<_$AppDatabase, $LocalIngredientsTable> {
  $$LocalIngredientsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get name => $composableBuilder(
    column: $table.name,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get defaultUnit => $composableBuilder(
    column: $table.defaultUnit,
    builder: (column) => ColumnFilters(column),
  );
}

class $$LocalIngredientsTableOrderingComposer
    extends Composer<_$AppDatabase, $LocalIngredientsTable> {
  $$LocalIngredientsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get name => $composableBuilder(
    column: $table.name,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get defaultUnit => $composableBuilder(
    column: $table.defaultUnit,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$LocalIngredientsTableAnnotationComposer
    extends Composer<_$AppDatabase, $LocalIngredientsTable> {
  $$LocalIngredientsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumn<String> get defaultUnit => $composableBuilder(
    column: $table.defaultUnit,
    builder: (column) => column,
  );
}

class $$LocalIngredientsTableTableManager
    extends
        RootTableManager<
          _$AppDatabase,
          $LocalIngredientsTable,
          LocalIngredient,
          $$LocalIngredientsTableFilterComposer,
          $$LocalIngredientsTableOrderingComposer,
          $$LocalIngredientsTableAnnotationComposer,
          $$LocalIngredientsTableCreateCompanionBuilder,
          $$LocalIngredientsTableUpdateCompanionBuilder,
          (
            LocalIngredient,
            BaseReferences<
              _$AppDatabase,
              $LocalIngredientsTable,
              LocalIngredient
            >,
          ),
          LocalIngredient,
          PrefetchHooks Function()
        > {
  $$LocalIngredientsTableTableManager(
    _$AppDatabase db,
    $LocalIngredientsTable table,
  ) : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer:
              () =>
                  $$LocalIngredientsTableFilterComposer($db: db, $table: table),
          createOrderingComposer:
              () => $$LocalIngredientsTableOrderingComposer(
                $db: db,
                $table: table,
              ),
          createComputedFieldComposer:
              () => $$LocalIngredientsTableAnnotationComposer(
                $db: db,
                $table: table,
              ),
          updateCompanionCallback:
              ({
                Value<String> id = const Value.absent(),
                Value<String> name = const Value.absent(),
                Value<String> defaultUnit = const Value.absent(),
                Value<int> rowid = const Value.absent(),
              }) => LocalIngredientsCompanion(
                id: id,
                name: name,
                defaultUnit: defaultUnit,
                rowid: rowid,
              ),
          createCompanionCallback:
              ({
                required String id,
                required String name,
                required String defaultUnit,
                Value<int> rowid = const Value.absent(),
              }) => LocalIngredientsCompanion.insert(
                id: id,
                name: name,
                defaultUnit: defaultUnit,
                rowid: rowid,
              ),
          withReferenceMapper:
              (p0) =>
                  p0
                      .map(
                        (e) => (
                          e.readTable(table),
                          BaseReferences(db, table, e),
                        ),
                      )
                      .toList(),
          prefetchHooksCallback: null,
        ),
      );
}

typedef $$LocalIngredientsTableProcessedTableManager =
    ProcessedTableManager<
      _$AppDatabase,
      $LocalIngredientsTable,
      LocalIngredient,
      $$LocalIngredientsTableFilterComposer,
      $$LocalIngredientsTableOrderingComposer,
      $$LocalIngredientsTableAnnotationComposer,
      $$LocalIngredientsTableCreateCompanionBuilder,
      $$LocalIngredientsTableUpdateCompanionBuilder,
      (
        LocalIngredient,
        BaseReferences<_$AppDatabase, $LocalIngredientsTable, LocalIngredient>,
      ),
      LocalIngredient,
      PrefetchHooks Function()
    >;
typedef $$LocalRecipeIngredientsTableCreateCompanionBuilder =
    LocalRecipeIngredientsCompanion Function({
      required String recipeId,
      required String ingredientId,
      required double quantity,
      required String unit,
      Value<int> rowid,
    });
typedef $$LocalRecipeIngredientsTableUpdateCompanionBuilder =
    LocalRecipeIngredientsCompanion Function({
      Value<String> recipeId,
      Value<String> ingredientId,
      Value<double> quantity,
      Value<String> unit,
      Value<int> rowid,
    });

class $$LocalRecipeIngredientsTableFilterComposer
    extends Composer<_$AppDatabase, $LocalRecipeIngredientsTable> {
  $$LocalRecipeIngredientsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get recipeId => $composableBuilder(
    column: $table.recipeId,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get ingredientId => $composableBuilder(
    column: $table.ingredientId,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get quantity => $composableBuilder(
    column: $table.quantity,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get unit => $composableBuilder(
    column: $table.unit,
    builder: (column) => ColumnFilters(column),
  );
}

class $$LocalRecipeIngredientsTableOrderingComposer
    extends Composer<_$AppDatabase, $LocalRecipeIngredientsTable> {
  $$LocalRecipeIngredientsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get recipeId => $composableBuilder(
    column: $table.recipeId,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get ingredientId => $composableBuilder(
    column: $table.ingredientId,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get quantity => $composableBuilder(
    column: $table.quantity,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get unit => $composableBuilder(
    column: $table.unit,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$LocalRecipeIngredientsTableAnnotationComposer
    extends Composer<_$AppDatabase, $LocalRecipeIngredientsTable> {
  $$LocalRecipeIngredientsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get recipeId =>
      $composableBuilder(column: $table.recipeId, builder: (column) => column);

  GeneratedColumn<String> get ingredientId => $composableBuilder(
    column: $table.ingredientId,
    builder: (column) => column,
  );

  GeneratedColumn<double> get quantity =>
      $composableBuilder(column: $table.quantity, builder: (column) => column);

  GeneratedColumn<String> get unit =>
      $composableBuilder(column: $table.unit, builder: (column) => column);
}

class $$LocalRecipeIngredientsTableTableManager
    extends
        RootTableManager<
          _$AppDatabase,
          $LocalRecipeIngredientsTable,
          LocalRecipeIngredient,
          $$LocalRecipeIngredientsTableFilterComposer,
          $$LocalRecipeIngredientsTableOrderingComposer,
          $$LocalRecipeIngredientsTableAnnotationComposer,
          $$LocalRecipeIngredientsTableCreateCompanionBuilder,
          $$LocalRecipeIngredientsTableUpdateCompanionBuilder,
          (
            LocalRecipeIngredient,
            BaseReferences<
              _$AppDatabase,
              $LocalRecipeIngredientsTable,
              LocalRecipeIngredient
            >,
          ),
          LocalRecipeIngredient,
          PrefetchHooks Function()
        > {
  $$LocalRecipeIngredientsTableTableManager(
    _$AppDatabase db,
    $LocalRecipeIngredientsTable table,
  ) : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer:
              () => $$LocalRecipeIngredientsTableFilterComposer(
                $db: db,
                $table: table,
              ),
          createOrderingComposer:
              () => $$LocalRecipeIngredientsTableOrderingComposer(
                $db: db,
                $table: table,
              ),
          createComputedFieldComposer:
              () => $$LocalRecipeIngredientsTableAnnotationComposer(
                $db: db,
                $table: table,
              ),
          updateCompanionCallback:
              ({
                Value<String> recipeId = const Value.absent(),
                Value<String> ingredientId = const Value.absent(),
                Value<double> quantity = const Value.absent(),
                Value<String> unit = const Value.absent(),
                Value<int> rowid = const Value.absent(),
              }) => LocalRecipeIngredientsCompanion(
                recipeId: recipeId,
                ingredientId: ingredientId,
                quantity: quantity,
                unit: unit,
                rowid: rowid,
              ),
          createCompanionCallback:
              ({
                required String recipeId,
                required String ingredientId,
                required double quantity,
                required String unit,
                Value<int> rowid = const Value.absent(),
              }) => LocalRecipeIngredientsCompanion.insert(
                recipeId: recipeId,
                ingredientId: ingredientId,
                quantity: quantity,
                unit: unit,
                rowid: rowid,
              ),
          withReferenceMapper:
              (p0) =>
                  p0
                      .map(
                        (e) => (
                          e.readTable(table),
                          BaseReferences(db, table, e),
                        ),
                      )
                      .toList(),
          prefetchHooksCallback: null,
        ),
      );
}

typedef $$LocalRecipeIngredientsTableProcessedTableManager =
    ProcessedTableManager<
      _$AppDatabase,
      $LocalRecipeIngredientsTable,
      LocalRecipeIngredient,
      $$LocalRecipeIngredientsTableFilterComposer,
      $$LocalRecipeIngredientsTableOrderingComposer,
      $$LocalRecipeIngredientsTableAnnotationComposer,
      $$LocalRecipeIngredientsTableCreateCompanionBuilder,
      $$LocalRecipeIngredientsTableUpdateCompanionBuilder,
      (
        LocalRecipeIngredient,
        BaseReferences<
          _$AppDatabase,
          $LocalRecipeIngredientsTable,
          LocalRecipeIngredient
        >,
      ),
      LocalRecipeIngredient,
      PrefetchHooks Function()
    >;
typedef $$MealPlansTableCreateCompanionBuilder =
    MealPlansCompanion Function({
      Value<int> id,
      required DateTime date,
      required String mealType,
      required String recipeId,
      Value<String> syncStatus,
    });
typedef $$MealPlansTableUpdateCompanionBuilder =
    MealPlansCompanion Function({
      Value<int> id,
      Value<DateTime> date,
      Value<String> mealType,
      Value<String> recipeId,
      Value<String> syncStatus,
    });

class $$MealPlansTableFilterComposer
    extends Composer<_$AppDatabase, $MealPlansTable> {
  $$MealPlansTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get date => $composableBuilder(
    column: $table.date,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get mealType => $composableBuilder(
    column: $table.mealType,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get recipeId => $composableBuilder(
    column: $table.recipeId,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get syncStatus => $composableBuilder(
    column: $table.syncStatus,
    builder: (column) => ColumnFilters(column),
  );
}

class $$MealPlansTableOrderingComposer
    extends Composer<_$AppDatabase, $MealPlansTable> {
  $$MealPlansTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get date => $composableBuilder(
    column: $table.date,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get mealType => $composableBuilder(
    column: $table.mealType,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get recipeId => $composableBuilder(
    column: $table.recipeId,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get syncStatus => $composableBuilder(
    column: $table.syncStatus,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$MealPlansTableAnnotationComposer
    extends Composer<_$AppDatabase, $MealPlansTable> {
  $$MealPlansTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<DateTime> get date =>
      $composableBuilder(column: $table.date, builder: (column) => column);

  GeneratedColumn<String> get mealType =>
      $composableBuilder(column: $table.mealType, builder: (column) => column);

  GeneratedColumn<String> get recipeId =>
      $composableBuilder(column: $table.recipeId, builder: (column) => column);

  GeneratedColumn<String> get syncStatus => $composableBuilder(
    column: $table.syncStatus,
    builder: (column) => column,
  );
}

class $$MealPlansTableTableManager
    extends
        RootTableManager<
          _$AppDatabase,
          $MealPlansTable,
          MealPlan,
          $$MealPlansTableFilterComposer,
          $$MealPlansTableOrderingComposer,
          $$MealPlansTableAnnotationComposer,
          $$MealPlansTableCreateCompanionBuilder,
          $$MealPlansTableUpdateCompanionBuilder,
          (MealPlan, BaseReferences<_$AppDatabase, $MealPlansTable, MealPlan>),
          MealPlan,
          PrefetchHooks Function()
        > {
  $$MealPlansTableTableManager(_$AppDatabase db, $MealPlansTable table)
    : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer:
              () => $$MealPlansTableFilterComposer($db: db, $table: table),
          createOrderingComposer:
              () => $$MealPlansTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer:
              () => $$MealPlansTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                Value<DateTime> date = const Value.absent(),
                Value<String> mealType = const Value.absent(),
                Value<String> recipeId = const Value.absent(),
                Value<String> syncStatus = const Value.absent(),
              }) => MealPlansCompanion(
                id: id,
                date: date,
                mealType: mealType,
                recipeId: recipeId,
                syncStatus: syncStatus,
              ),
          createCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                required DateTime date,
                required String mealType,
                required String recipeId,
                Value<String> syncStatus = const Value.absent(),
              }) => MealPlansCompanion.insert(
                id: id,
                date: date,
                mealType: mealType,
                recipeId: recipeId,
                syncStatus: syncStatus,
              ),
          withReferenceMapper:
              (p0) =>
                  p0
                      .map(
                        (e) => (
                          e.readTable(table),
                          BaseReferences(db, table, e),
                        ),
                      )
                      .toList(),
          prefetchHooksCallback: null,
        ),
      );
}

typedef $$MealPlansTableProcessedTableManager =
    ProcessedTableManager<
      _$AppDatabase,
      $MealPlansTable,
      MealPlan,
      $$MealPlansTableFilterComposer,
      $$MealPlansTableOrderingComposer,
      $$MealPlansTableAnnotationComposer,
      $$MealPlansTableCreateCompanionBuilder,
      $$MealPlansTableUpdateCompanionBuilder,
      (MealPlan, BaseReferences<_$AppDatabase, $MealPlansTable, MealPlan>),
      MealPlan,
      PrefetchHooks Function()
    >;
typedef $$ShoppingListItemsTableCreateCompanionBuilder =
    ShoppingListItemsCompanion Function({
      Value<int> id,
      required String ingredientId,
      required double totalQuantity,
      Value<bool> isPurchased,
    });
typedef $$ShoppingListItemsTableUpdateCompanionBuilder =
    ShoppingListItemsCompanion Function({
      Value<int> id,
      Value<String> ingredientId,
      Value<double> totalQuantity,
      Value<bool> isPurchased,
    });

class $$ShoppingListItemsTableFilterComposer
    extends Composer<_$AppDatabase, $ShoppingListItemsTable> {
  $$ShoppingListItemsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get ingredientId => $composableBuilder(
    column: $table.ingredientId,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get totalQuantity => $composableBuilder(
    column: $table.totalQuantity,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<bool> get isPurchased => $composableBuilder(
    column: $table.isPurchased,
    builder: (column) => ColumnFilters(column),
  );
}

class $$ShoppingListItemsTableOrderingComposer
    extends Composer<_$AppDatabase, $ShoppingListItemsTable> {
  $$ShoppingListItemsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get ingredientId => $composableBuilder(
    column: $table.ingredientId,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get totalQuantity => $composableBuilder(
    column: $table.totalQuantity,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<bool> get isPurchased => $composableBuilder(
    column: $table.isPurchased,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$ShoppingListItemsTableAnnotationComposer
    extends Composer<_$AppDatabase, $ShoppingListItemsTable> {
  $$ShoppingListItemsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get ingredientId => $composableBuilder(
    column: $table.ingredientId,
    builder: (column) => column,
  );

  GeneratedColumn<double> get totalQuantity => $composableBuilder(
    column: $table.totalQuantity,
    builder: (column) => column,
  );

  GeneratedColumn<bool> get isPurchased => $composableBuilder(
    column: $table.isPurchased,
    builder: (column) => column,
  );
}

class $$ShoppingListItemsTableTableManager
    extends
        RootTableManager<
          _$AppDatabase,
          $ShoppingListItemsTable,
          ShoppingListItem,
          $$ShoppingListItemsTableFilterComposer,
          $$ShoppingListItemsTableOrderingComposer,
          $$ShoppingListItemsTableAnnotationComposer,
          $$ShoppingListItemsTableCreateCompanionBuilder,
          $$ShoppingListItemsTableUpdateCompanionBuilder,
          (
            ShoppingListItem,
            BaseReferences<
              _$AppDatabase,
              $ShoppingListItemsTable,
              ShoppingListItem
            >,
          ),
          ShoppingListItem,
          PrefetchHooks Function()
        > {
  $$ShoppingListItemsTableTableManager(
    _$AppDatabase db,
    $ShoppingListItemsTable table,
  ) : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer:
              () => $$ShoppingListItemsTableFilterComposer(
                $db: db,
                $table: table,
              ),
          createOrderingComposer:
              () => $$ShoppingListItemsTableOrderingComposer(
                $db: db,
                $table: table,
              ),
          createComputedFieldComposer:
              () => $$ShoppingListItemsTableAnnotationComposer(
                $db: db,
                $table: table,
              ),
          updateCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                Value<String> ingredientId = const Value.absent(),
                Value<double> totalQuantity = const Value.absent(),
                Value<bool> isPurchased = const Value.absent(),
              }) => ShoppingListItemsCompanion(
                id: id,
                ingredientId: ingredientId,
                totalQuantity: totalQuantity,
                isPurchased: isPurchased,
              ),
          createCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                required String ingredientId,
                required double totalQuantity,
                Value<bool> isPurchased = const Value.absent(),
              }) => ShoppingListItemsCompanion.insert(
                id: id,
                ingredientId: ingredientId,
                totalQuantity: totalQuantity,
                isPurchased: isPurchased,
              ),
          withReferenceMapper:
              (p0) =>
                  p0
                      .map(
                        (e) => (
                          e.readTable(table),
                          BaseReferences(db, table, e),
                        ),
                      )
                      .toList(),
          prefetchHooksCallback: null,
        ),
      );
}

typedef $$ShoppingListItemsTableProcessedTableManager =
    ProcessedTableManager<
      _$AppDatabase,
      $ShoppingListItemsTable,
      ShoppingListItem,
      $$ShoppingListItemsTableFilterComposer,
      $$ShoppingListItemsTableOrderingComposer,
      $$ShoppingListItemsTableAnnotationComposer,
      $$ShoppingListItemsTableCreateCompanionBuilder,
      $$ShoppingListItemsTableUpdateCompanionBuilder,
      (
        ShoppingListItem,
        BaseReferences<
          _$AppDatabase,
          $ShoppingListItemsTable,
          ShoppingListItem
        >,
      ),
      ShoppingListItem,
      PrefetchHooks Function()
    >;
typedef $$ImageScanHistoriesTableCreateCompanionBuilder =
    ImageScanHistoriesCompanion Function({
      required String id,
      required String scanType,
      required DateTime requestTimestamp,
      required String cachedImageUrl,
      Value<String?> userConfirmedResultJson,
      Value<String?> linkedRecipeId,
      Value<int> rowid,
    });
typedef $$ImageScanHistoriesTableUpdateCompanionBuilder =
    ImageScanHistoriesCompanion Function({
      Value<String> id,
      Value<String> scanType,
      Value<DateTime> requestTimestamp,
      Value<String> cachedImageUrl,
      Value<String?> userConfirmedResultJson,
      Value<String?> linkedRecipeId,
      Value<int> rowid,
    });

class $$ImageScanHistoriesTableFilterComposer
    extends Composer<_$AppDatabase, $ImageScanHistoriesTable> {
  $$ImageScanHistoriesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get scanType => $composableBuilder(
    column: $table.scanType,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get requestTimestamp => $composableBuilder(
    column: $table.requestTimestamp,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get cachedImageUrl => $composableBuilder(
    column: $table.cachedImageUrl,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get userConfirmedResultJson => $composableBuilder(
    column: $table.userConfirmedResultJson,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get linkedRecipeId => $composableBuilder(
    column: $table.linkedRecipeId,
    builder: (column) => ColumnFilters(column),
  );
}

class $$ImageScanHistoriesTableOrderingComposer
    extends Composer<_$AppDatabase, $ImageScanHistoriesTable> {
  $$ImageScanHistoriesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get scanType => $composableBuilder(
    column: $table.scanType,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get requestTimestamp => $composableBuilder(
    column: $table.requestTimestamp,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get cachedImageUrl => $composableBuilder(
    column: $table.cachedImageUrl,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get userConfirmedResultJson => $composableBuilder(
    column: $table.userConfirmedResultJson,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get linkedRecipeId => $composableBuilder(
    column: $table.linkedRecipeId,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$ImageScanHistoriesTableAnnotationComposer
    extends Composer<_$AppDatabase, $ImageScanHistoriesTable> {
  $$ImageScanHistoriesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get scanType =>
      $composableBuilder(column: $table.scanType, builder: (column) => column);

  GeneratedColumn<DateTime> get requestTimestamp => $composableBuilder(
    column: $table.requestTimestamp,
    builder: (column) => column,
  );

  GeneratedColumn<String> get cachedImageUrl => $composableBuilder(
    column: $table.cachedImageUrl,
    builder: (column) => column,
  );

  GeneratedColumn<String> get userConfirmedResultJson => $composableBuilder(
    column: $table.userConfirmedResultJson,
    builder: (column) => column,
  );

  GeneratedColumn<String> get linkedRecipeId => $composableBuilder(
    column: $table.linkedRecipeId,
    builder: (column) => column,
  );
}

class $$ImageScanHistoriesTableTableManager
    extends
        RootTableManager<
          _$AppDatabase,
          $ImageScanHistoriesTable,
          ImageScanHistory,
          $$ImageScanHistoriesTableFilterComposer,
          $$ImageScanHistoriesTableOrderingComposer,
          $$ImageScanHistoriesTableAnnotationComposer,
          $$ImageScanHistoriesTableCreateCompanionBuilder,
          $$ImageScanHistoriesTableUpdateCompanionBuilder,
          (
            ImageScanHistory,
            BaseReferences<
              _$AppDatabase,
              $ImageScanHistoriesTable,
              ImageScanHistory
            >,
          ),
          ImageScanHistory,
          PrefetchHooks Function()
        > {
  $$ImageScanHistoriesTableTableManager(
    _$AppDatabase db,
    $ImageScanHistoriesTable table,
  ) : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer:
              () => $$ImageScanHistoriesTableFilterComposer(
                $db: db,
                $table: table,
              ),
          createOrderingComposer:
              () => $$ImageScanHistoriesTableOrderingComposer(
                $db: db,
                $table: table,
              ),
          createComputedFieldComposer:
              () => $$ImageScanHistoriesTableAnnotationComposer(
                $db: db,
                $table: table,
              ),
          updateCompanionCallback:
              ({
                Value<String> id = const Value.absent(),
                Value<String> scanType = const Value.absent(),
                Value<DateTime> requestTimestamp = const Value.absent(),
                Value<String> cachedImageUrl = const Value.absent(),
                Value<String?> userConfirmedResultJson = const Value.absent(),
                Value<String?> linkedRecipeId = const Value.absent(),
                Value<int> rowid = const Value.absent(),
              }) => ImageScanHistoriesCompanion(
                id: id,
                scanType: scanType,
                requestTimestamp: requestTimestamp,
                cachedImageUrl: cachedImageUrl,
                userConfirmedResultJson: userConfirmedResultJson,
                linkedRecipeId: linkedRecipeId,
                rowid: rowid,
              ),
          createCompanionCallback:
              ({
                required String id,
                required String scanType,
                required DateTime requestTimestamp,
                required String cachedImageUrl,
                Value<String?> userConfirmedResultJson = const Value.absent(),
                Value<String?> linkedRecipeId = const Value.absent(),
                Value<int> rowid = const Value.absent(),
              }) => ImageScanHistoriesCompanion.insert(
                id: id,
                scanType: scanType,
                requestTimestamp: requestTimestamp,
                cachedImageUrl: cachedImageUrl,
                userConfirmedResultJson: userConfirmedResultJson,
                linkedRecipeId: linkedRecipeId,
                rowid: rowid,
              ),
          withReferenceMapper:
              (p0) =>
                  p0
                      .map(
                        (e) => (
                          e.readTable(table),
                          BaseReferences(db, table, e),
                        ),
                      )
                      .toList(),
          prefetchHooksCallback: null,
        ),
      );
}

typedef $$ImageScanHistoriesTableProcessedTableManager =
    ProcessedTableManager<
      _$AppDatabase,
      $ImageScanHistoriesTable,
      ImageScanHistory,
      $$ImageScanHistoriesTableFilterComposer,
      $$ImageScanHistoriesTableOrderingComposer,
      $$ImageScanHistoriesTableAnnotationComposer,
      $$ImageScanHistoriesTableCreateCompanionBuilder,
      $$ImageScanHistoriesTableUpdateCompanionBuilder,
      (
        ImageScanHistory,
        BaseReferences<
          _$AppDatabase,
          $ImageScanHistoriesTable,
          ImageScanHistory
        >,
      ),
      ImageScanHistory,
      PrefetchHooks Function()
    >;
typedef $$UserTemporaryStatesTableCreateCompanionBuilder =
    UserTemporaryStatesCompanion Function({
      Value<int> id,
      Value<String?> stateIdFromServer,
      required String userId,
      required String stateTag,
      Value<String?> stateDescription,
      required DateTime appliesFrom,
      Value<DateTime?> appliesUntil,
      Value<bool> isActive,
      Value<String> syncStatus,
    });
typedef $$UserTemporaryStatesTableUpdateCompanionBuilder =
    UserTemporaryStatesCompanion Function({
      Value<int> id,
      Value<String?> stateIdFromServer,
      Value<String> userId,
      Value<String> stateTag,
      Value<String?> stateDescription,
      Value<DateTime> appliesFrom,
      Value<DateTime?> appliesUntil,
      Value<bool> isActive,
      Value<String> syncStatus,
    });

class $$UserTemporaryStatesTableFilterComposer
    extends Composer<_$AppDatabase, $UserTemporaryStatesTable> {
  $$UserTemporaryStatesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get stateIdFromServer => $composableBuilder(
    column: $table.stateIdFromServer,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get userId => $composableBuilder(
    column: $table.userId,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get stateTag => $composableBuilder(
    column: $table.stateTag,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get stateDescription => $composableBuilder(
    column: $table.stateDescription,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get appliesFrom => $composableBuilder(
    column: $table.appliesFrom,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get appliesUntil => $composableBuilder(
    column: $table.appliesUntil,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<bool> get isActive => $composableBuilder(
    column: $table.isActive,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get syncStatus => $composableBuilder(
    column: $table.syncStatus,
    builder: (column) => ColumnFilters(column),
  );
}

class $$UserTemporaryStatesTableOrderingComposer
    extends Composer<_$AppDatabase, $UserTemporaryStatesTable> {
  $$UserTemporaryStatesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get stateIdFromServer => $composableBuilder(
    column: $table.stateIdFromServer,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get userId => $composableBuilder(
    column: $table.userId,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get stateTag => $composableBuilder(
    column: $table.stateTag,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get stateDescription => $composableBuilder(
    column: $table.stateDescription,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get appliesFrom => $composableBuilder(
    column: $table.appliesFrom,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get appliesUntil => $composableBuilder(
    column: $table.appliesUntil,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<bool> get isActive => $composableBuilder(
    column: $table.isActive,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get syncStatus => $composableBuilder(
    column: $table.syncStatus,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$UserTemporaryStatesTableAnnotationComposer
    extends Composer<_$AppDatabase, $UserTemporaryStatesTable> {
  $$UserTemporaryStatesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get stateIdFromServer => $composableBuilder(
    column: $table.stateIdFromServer,
    builder: (column) => column,
  );

  GeneratedColumn<String> get userId =>
      $composableBuilder(column: $table.userId, builder: (column) => column);

  GeneratedColumn<String> get stateTag =>
      $composableBuilder(column: $table.stateTag, builder: (column) => column);

  GeneratedColumn<String> get stateDescription => $composableBuilder(
    column: $table.stateDescription,
    builder: (column) => column,
  );

  GeneratedColumn<DateTime> get appliesFrom => $composableBuilder(
    column: $table.appliesFrom,
    builder: (column) => column,
  );

  GeneratedColumn<DateTime> get appliesUntil => $composableBuilder(
    column: $table.appliesUntil,
    builder: (column) => column,
  );

  GeneratedColumn<bool> get isActive =>
      $composableBuilder(column: $table.isActive, builder: (column) => column);

  GeneratedColumn<String> get syncStatus => $composableBuilder(
    column: $table.syncStatus,
    builder: (column) => column,
  );
}

class $$UserTemporaryStatesTableTableManager
    extends
        RootTableManager<
          _$AppDatabase,
          $UserTemporaryStatesTable,
          UserTemporaryState,
          $$UserTemporaryStatesTableFilterComposer,
          $$UserTemporaryStatesTableOrderingComposer,
          $$UserTemporaryStatesTableAnnotationComposer,
          $$UserTemporaryStatesTableCreateCompanionBuilder,
          $$UserTemporaryStatesTableUpdateCompanionBuilder,
          (
            UserTemporaryState,
            BaseReferences<
              _$AppDatabase,
              $UserTemporaryStatesTable,
              UserTemporaryState
            >,
          ),
          UserTemporaryState,
          PrefetchHooks Function()
        > {
  $$UserTemporaryStatesTableTableManager(
    _$AppDatabase db,
    $UserTemporaryStatesTable table,
  ) : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer:
              () => $$UserTemporaryStatesTableFilterComposer(
                $db: db,
                $table: table,
              ),
          createOrderingComposer:
              () => $$UserTemporaryStatesTableOrderingComposer(
                $db: db,
                $table: table,
              ),
          createComputedFieldComposer:
              () => $$UserTemporaryStatesTableAnnotationComposer(
                $db: db,
                $table: table,
              ),
          updateCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                Value<String?> stateIdFromServer = const Value.absent(),
                Value<String> userId = const Value.absent(),
                Value<String> stateTag = const Value.absent(),
                Value<String?> stateDescription = const Value.absent(),
                Value<DateTime> appliesFrom = const Value.absent(),
                Value<DateTime?> appliesUntil = const Value.absent(),
                Value<bool> isActive = const Value.absent(),
                Value<String> syncStatus = const Value.absent(),
              }) => UserTemporaryStatesCompanion(
                id: id,
                stateIdFromServer: stateIdFromServer,
                userId: userId,
                stateTag: stateTag,
                stateDescription: stateDescription,
                appliesFrom: appliesFrom,
                appliesUntil: appliesUntil,
                isActive: isActive,
                syncStatus: syncStatus,
              ),
          createCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                Value<String?> stateIdFromServer = const Value.absent(),
                required String userId,
                required String stateTag,
                Value<String?> stateDescription = const Value.absent(),
                required DateTime appliesFrom,
                Value<DateTime?> appliesUntil = const Value.absent(),
                Value<bool> isActive = const Value.absent(),
                Value<String> syncStatus = const Value.absent(),
              }) => UserTemporaryStatesCompanion.insert(
                id: id,
                stateIdFromServer: stateIdFromServer,
                userId: userId,
                stateTag: stateTag,
                stateDescription: stateDescription,
                appliesFrom: appliesFrom,
                appliesUntil: appliesUntil,
                isActive: isActive,
                syncStatus: syncStatus,
              ),
          withReferenceMapper:
              (p0) =>
                  p0
                      .map(
                        (e) => (
                          e.readTable(table),
                          BaseReferences(db, table, e),
                        ),
                      )
                      .toList(),
          prefetchHooksCallback: null,
        ),
      );
}

typedef $$UserTemporaryStatesTableProcessedTableManager =
    ProcessedTableManager<
      _$AppDatabase,
      $UserTemporaryStatesTable,
      UserTemporaryState,
      $$UserTemporaryStatesTableFilterComposer,
      $$UserTemporaryStatesTableOrderingComposer,
      $$UserTemporaryStatesTableAnnotationComposer,
      $$UserTemporaryStatesTableCreateCompanionBuilder,
      $$UserTemporaryStatesTableUpdateCompanionBuilder,
      (
        UserTemporaryState,
        BaseReferences<
          _$AppDatabase,
          $UserTemporaryStatesTable,
          UserTemporaryState
        >,
      ),
      UserTemporaryState,
      PrefetchHooks Function()
    >;

class $AppDatabaseManager {
  final _$AppDatabase _db;
  $AppDatabaseManager(this._db);
  $$LocalUsersTableTableManager get localUsers =>
      $$LocalUsersTableTableManager(_db, _db.localUsers);
  $$LocalUserProfilesTableTableManager get localUserProfiles =>
      $$LocalUserProfilesTableTableManager(_db, _db.localUserProfiles);
  $$LocalRecipesTableTableManager get localRecipes =>
      $$LocalRecipesTableTableManager(_db, _db.localRecipes);
  $$LocalIngredientsTableTableManager get localIngredients =>
      $$LocalIngredientsTableTableManager(_db, _db.localIngredients);
  $$LocalRecipeIngredientsTableTableManager get localRecipeIngredients =>
      $$LocalRecipeIngredientsTableTableManager(
        _db,
        _db.localRecipeIngredients,
      );
  $$MealPlansTableTableManager get mealPlans =>
      $$MealPlansTableTableManager(_db, _db.mealPlans);
  $$ShoppingListItemsTableTableManager get shoppingListItems =>
      $$ShoppingListItemsTableTableManager(_db, _db.shoppingListItems);
  $$ImageScanHistoriesTableTableManager get imageScanHistories =>
      $$ImageScanHistoriesTableTableManager(_db, _db.imageScanHistories);
  $$UserTemporaryStatesTableTableManager get userTemporaryStates =>
      $$UserTemporaryStatesTableTableManager(_db, _db.userTemporaryStates);
}
