import 'package:banachef/core/services/logger/logger.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'app.dart';
import 'core/di/injection_container.dart';

void main() async {
  AppLogger.initialize(true);
  await AppLogger.runZoned(() async {
    WidgetsFlutterBinding.ensureInitialized();

    await configureDependencies();


    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
    runApp(const App());
  });
}
