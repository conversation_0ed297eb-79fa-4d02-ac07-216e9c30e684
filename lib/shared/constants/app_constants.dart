class AppConstants {
  // App Information
  static const String appName = 'BanaChef';
  static const String appVersion = '1.0.0';
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double extraLargePadding = 32.0;
  
  static const double defaultBorderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;
  
  static const double defaultElevation = 4.0;
  static const double smallElevation = 2.0;
  static const double largeElevation = 8.0;
  
  // Image Constants
  static const double defaultImageHeight = 200.0;
  static const double smallImageHeight = 100.0;
  static const double largeImageHeight = 300.0;
  
  // Text Constants
  static const int maxTitleLength = 100;
  static const int maxDescriptionLength = 500;
  static const int maxCommentLength = 200;
  
  // Validation Constants
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 50;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;
  
  // Recipe Constants
  static const int minPrepTime = 1;
  static const int maxPrepTime = 1440; // 24 hours
  static const int minServings = 1;
  static const int maxServings = 20;
  
  // Difficulty Levels
  static const String difficultyEasy = 'easy';
  static const String difficultyMedium = 'medium';
  static const String difficultyHard = 'hard';
  
  static const List<String> difficultyLevels = [
    difficultyEasy,
    difficultyMedium,
    difficultyHard,
  ];
  
  // Common Tags
  static const List<String> commonTags = [
    'vegetarian',
    'vegan',
    'gluten-free',
    'dairy-free',
    'low-carb',
    'keto',
    'paleo',
    'healthy',
    'quick',
    'easy',
    'comfort-food',
    'dessert',
    'breakfast',
    'lunch',
    'dinner',
    'snack',
  ];
  
  // Error Messages
  static const String networkErrorMessage = 'Please check your internet connection';
  static const String serverErrorMessage = 'Something went wrong. Please try again later';
  static const String unknownErrorMessage = 'An unknown error occurred';
  static const String validationErrorMessage = 'Please check your input';
}
