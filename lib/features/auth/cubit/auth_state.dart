import '../../../core/bloc/bloc_exports.dart';
import '../models/auth_user.dart';

/// Authentication states
abstract class AuthState extends BaseState {
  const AuthState();
}

/// Initial state
class AuthInitial extends AuthState {
  const AuthInitial();

  @override
  List<Object?> get props => [];
}

/// Loading state during authentication
class AuthLoading extends AuthState {
  final String? message;

  const AuthLoading({this.message});

  @override
  List<Object?> get props => [message];
}

/// User is authenticated
class AuthAuthenticated extends AuthState {
  final AuthUser user;

  const AuthAuthenticated({required this.user});

  @override
  List<Object?> get props => [user];
}

/// User is not authenticated
class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated();

  @override
  List<Object?> get props => [];
}

/// Authentication error
class AuthError extends AuthState {
  final String message;
  final String? errorCode;

  const AuthError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

/// Google sign in loading
class GoogleSignInLoading extends AuthLoading {
  const GoogleSignInLoading() : super(message: 'Signing in with Google...');
}

/// Apple sign in loading
class AppleSignInLoading extends AuthLoading {
  const AppleSignInLoading() : super(message: 'Signing in with Apple...');
}

/// Token refresh loading
class TokenRefreshLoading extends AuthLoading {
  const TokenRefreshLoading() : super(message: 'Refreshing session...');
}

/// Logout loading
class LogoutLoading extends AuthLoading {
  const LogoutLoading() : super(message: 'Signing out...');
}

/// Authentication success (temporary state before moving to authenticated)
class AuthSuccess extends AuthState {
  final AuthUser user;
  final String message;

  const AuthSuccess({
    required this.user,
    this.message = 'Authentication successful',
  });

  @override
  List<Object?> get props => [user, message];
}

/// Logout success
class LogoutSuccess extends AuthState {
  final String message;

  const LogoutSuccess({
    this.message = 'Signed out successfully',
  });

  @override
  List<Object?> get props => [message];
}
