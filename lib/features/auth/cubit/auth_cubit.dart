import 'package:injectable/injectable.dart';
import '../../../core/bloc/bloc_exports.dart';
import '../../../core/errors/exceptions.dart';
import '../repositories/auth_repository.dart';
import '../services/google_auth_service.dart';
import '../services/apple_auth_service.dart';
import 'auth_state.dart';

/// Cubit for managing authentication state
@singleton
class AuthCubit extends BaseCubit<AuthState> {
  final AuthRepository _authRepository;
  final GoogleAuthService _googleAuthService;
  final AppleAuthService _appleAuthService;

  AuthCubit(
    this._authRepository,
    this._googleAuthService,
    this._appleAuthService,
  ) : super(const AuthInitial());

  /// Check initial authentication status
  Future<void> checkAuthStatus() async {
    await executeWithErrorHandling(
      () async {
        final isAuthenticated = await _authRepository.isAuthenticated();
        if (isAuthenticated) {
          final user = await _authRepository.getCurrentUser();
          if (user != null) {
            emit(AuthAuthenticated(user: user));
          } else {
            emit(const AuthUnauthenticated());
          }
        } else {
          emit(const AuthUnauthenticated());
        }
      },
      loadingMessage: 'Checking authentication...',
    );
  }

  /// Sign in with Google
  Future<void> signInWithGoogle() async {
    emit(const GoogleSignInLoading());

    try {
      // Get Google ID token
      final idToken = await _googleAuthService.signIn();

      // Authenticate with server
      final authResponse = await _authRepository.loginWithGoogle(idToken);

      // Emit success state
      emit(AuthSuccess(user: authResponse.user));

      // Move to authenticated state
      emit(AuthAuthenticated(user: authResponse.user));
    } catch (e) {
      emit(AuthError(
        message: e.toString(),
        errorCode: 'GOOGLE_SIGN_IN_ERROR',
      ));
    }
  }

  /// Sign in with Apple
  Future<void> signInWithApple() async {
    emit(const AppleSignInLoading());

    try {
      // Get Apple ID token
      final idToken = await _appleAuthService.signIn();

      // Authenticate with server
      final authResponse = await _authRepository.loginWithApple(idToken);

      // Emit success state
      emit(AuthSuccess(user: authResponse.user));

      // Move to authenticated state
      emit(AuthAuthenticated(user: authResponse.user));
    } catch (e) {
      emit(AuthError(
        message: e.toString(),
        errorCode: 'APPLE_SIGN_IN_ERROR',
      ));
    }
  }

  /// Refresh authentication token
  Future<void> refreshToken() async {
    emit(const TokenRefreshLoading());

    try {
      final refreshToken = await _authRepository.getCurrentUser();
      if (refreshToken == null) {
        throw const AuthenticationException('No refresh token available');
      }

      // This would need the actual refresh token from storage
      // For now, just check auth status
      await checkAuthStatus();
    } catch (e) {
      emit(AuthError(
        message: e.toString(),
        errorCode: 'TOKEN_REFRESH_ERROR',
      ));
    }
  }

  /// Sign out
  Future<void> signOut() async {
    emit(const LogoutLoading());

    try {
      // Sign out from Google if signed in
      if (await _googleAuthService.isSignedIn()) {
        await _googleAuthService.signOut();
      }

      // Clear authentication data
      await _authRepository.logout();

      // Emit success state
      emit(const LogoutSuccess());

      // Move to unauthenticated state
      emit(const AuthUnauthenticated());
    } catch (e) {
      emit(AuthError(
        message: e.toString(),
        errorCode: 'LOGOUT_ERROR',
      ));
    }
  }

  /// Clear error state
  void clearError() {
    if (state is AuthError) {
      emit(const AuthUnauthenticated());
    }
  }

  /// Reset to initial state
  @override
  void reset() {
    emit(const AuthInitial());
  }

  @override
  BaseErrorState createErrorState({
    required String message,
    String? errorCode,
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    // Return a generic error state since AuthError extends AuthState, not BaseErrorState
    return GenericErrorState(
      message: message,
      errorCode: errorCode,
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }
}
