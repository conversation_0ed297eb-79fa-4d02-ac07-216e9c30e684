import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'auth_user.g.dart';

/// User data from authentication response
@JsonSerializable()
class AuthUser extends Equatable {
  @Json<PERSON>ey(name: 'user_id')
  final String userId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'email')
  final String email;

  @<PERSON>son<PERSON>ey(name: 'display_name')
  final String? displayName;

  @Json<PERSON>ey(name: 'photo_url')
  final String? photoUrl;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'auth_provider')
  final String authProvider;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final String createdAt;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_login_at')
  final String lastLoginAt;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'referral_code')
  final String? referralCode;

  const AuthUser({
    required this.userId,
    required this.email,
    this.displayName,
    this.photoUrl,
    required this.authProvider,
    required this.createdAt,
    required this.lastLoginAt,
    this.referralCode,
  });

  factory AuthUser.fromJson(Map<String, dynamic> json) =>
      _$AuthUserFromJson(json);

  Map<String, dynamic> toJson() => _$AuthUserToJson(this);

  @override
  List<Object?> get props => [
        userId,
        email,
        displayName,
        photoUrl,
        authProvider,
        createdAt,
        lastLoginAt,
        referralCode,
      ];

  /// Convert to domain User entity
  AuthUser copyWith({
    String? userId,
    String? email,
    String? displayName,
    String? photoUrl,
    String? authProvider,
    String? createdAt,
    String? lastLoginAt,
    String? referralCode,
  }) {
    return AuthUser(
      userId: userId ?? this.userId,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoUrl: photoUrl ?? this.photoUrl,
      authProvider: authProvider ?? this.authProvider,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      referralCode: referralCode ?? this.referralCode,
    );
  }
}

/// Authentication provider enum
enum AuthProvider {
  @JsonValue('google')
  google,
  @JsonValue('apple')
  apple,
}

extension AuthProviderExtension on AuthProvider {
  String get value {
    switch (this) {
      case AuthProvider.google:
        return 'google';
      case AuthProvider.apple:
        return 'apple';
    }
  }
}
