import 'dart:convert';
import 'package:injectable/injectable.dart';
import 'package:drift/drift.dart';
import '../../../core/services/secure_storage_service.dart';
import '../../../core/services/database_service.dart';
import '../../../core/database/app_database.dart';
import '../../../core/errors/exceptions.dart';
import '../models/auth_user.dart';
import '../models/auth_response.dart';

/// Service for managing authentication tokens and user data
@singleton
class TokenService {
  final SecureStorageService _secureStorage;
  final DatabaseService _databaseService;

  TokenService(this._secureStorage, this._databaseService);

  /// Save authentication response data
  Future<void> saveAuthData(AuthResponse authResponse) async {
    try {
      // Save tokens using existing SecureStorageService methods
      await _secureStorage.saveAccessToken(authResponse.accessToken);
      await _secureStorage.saveRefreshToken(authResponse.refreshToken);

      // Save user data as JSON string
      final userJson = jsonEncode(authResponse.user.toJson());
      await _secureStorage.saveUserData(userJson);

      // Save user data to local database
      await _saveUserToLocalDatabase(authResponse.user);

    } catch (e) {
      throw CacheException('Failed to save authentication data: ${e.toString()}');
    }
  }

  /// Get stored access token
  Future<String?> getAccessToken() async {
    try {
      return await _secureStorage.getAccessToken();
    } catch (e) {
      throw CacheException('Failed to get access token: ${e.toString()}');
    }
  }

  /// Get stored refresh token
  Future<String?> getRefreshToken() async {
    try {
      return await _secureStorage.getRefreshToken();
    } catch (e) {
      throw CacheException('Failed to get refresh token: ${e.toString()}');
    }
  }

  /// Get stored user data
  Future<AuthUser?> getUserData() async {
    try {
      final userJson = await _secureStorage.getUserData();
      if (userJson == null) return null;
      
      final userMap = jsonDecode(userJson) as Map<String, dynamic>;
      return AuthUser.fromJson(userMap);
    } catch (e) {
      throw CacheException('Failed to get user data: ${e.toString()}');
    }
  }

  /// Check if user is authenticated (has valid tokens)
  Future<bool> isAuthenticated() async {
    try {
      final accessToken = await getAccessToken();
      final refreshToken = await getRefreshToken();
      return accessToken != null && refreshToken != null;
    } catch (e) {
      return false;
    }
  }

  /// Clear all authentication data
  Future<void> clearAuthData() async {
    try {
      await _secureStorage.clearAuthTokens();
      await _secureStorage.clearUserData();
    } catch (e) {
      throw CacheException('Failed to clear authentication data: ${e.toString()}');
    }
  }

  /// Update access token (used by refresh token flow)
  Future<void> updateAccessToken(String newAccessToken) async {
    try {
      await _secureStorage.saveAccessToken(newAccessToken);
    } catch (e) {
      throw CacheException('Failed to update access token: ${e.toString()}');
    }
  }

  /// Update both tokens (used by refresh token flow)
  Future<void> updateTokens({
    required String accessToken,
    required String refreshToken,
  }) async {
    try {
      await _secureStorage.saveAccessToken(accessToken);
      await _secureStorage.saveRefreshToken(refreshToken);
    } catch (e) {
      throw CacheException('Failed to update tokens: ${e.toString()}');
    }
  }

  /// Check if tokens exist (without validation)
  Future<bool> hasTokens() async {
    try {
      final accessToken = await _secureStorage.getAccessToken();
      final refreshToken = await _secureStorage.getRefreshToken();
      return accessToken != null && refreshToken != null;
    } catch (e) {
      return false;
    }
  }

  /// Save user data to local database
  Future<void> _saveUserToLocalDatabase(AuthUser user) async {
    try {
      final database = _databaseService.database;

      // Insert or update user in LocalUsers table
      await database.into(database.localUsers).insertOnConflictUpdate(
        LocalUsersCompanion(
          id: Value(user.userId),
          email: Value(user.email),
          displayName: Value(user.displayName),
          photoUrl: Value(user.photoUrl),
          referralCode: Value(user.referralCode),
          lastSyncedAt: Value(DateTime.now()),
        ),
      );
    } catch (e) {
      throw CacheException('Failed to save user to local database: ${e.toString()}');
    }
  }
}
