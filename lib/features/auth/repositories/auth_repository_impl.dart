import 'package:injectable/injectable.dart';
import '../../../core/api/api_client.dart';
import '../../../core/errors/exceptions.dart';
import '../models/auth_request.dart';
import '../models/auth_response.dart';
import '../models/auth_user.dart';
import '../services/token_service.dart';
import 'auth_repository.dart';

/// Implementation of AuthRepository
@Singleton(as: AuthRepository)
class AuthRepositoryImpl implements AuthRepository {
  final ApiClient _apiClient;
  final TokenService _tokenService;

  AuthRepositoryImpl(this._apiClient, this._tokenService);

  @override
  Future<AuthResponse> loginWithGoogle(String idToken) async {
    try {
      final request = GoogleAuthRequest(idToken: idToken);

      final response = await _apiClient.post<Map<String, dynamic>>(
        'auth/login/google',
        data: request.toJson(),
      );

      if (response.data == null) {
        throw const ServerException('Empty response from server');
      }

      final authResponse = AuthResponse.fromJson(response.data!);
      // Save authentication data
      await _tokenService.saveAuthData(authResponse);

      return authResponse;
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('Google login failed: ${e.toString()}');
    }
  }

  @override
  Future<AuthResponse> loginWithApple(String idToken) async {
    try {
      final request = AppleAuthRequest(idToken: idToken);
      
      final response = await _apiClient.post<Map<String, dynamic>>(
        'auth/login/apple',
        data: request.toJson(),
      );

      if (response.data == null) {
        throw const ServerException('Empty response from server');
      }

      final authResponse = AuthResponse.fromJson(response.data!);
      
      // Save authentication data
      await _tokenService.saveAuthData(authResponse);
      
      return authResponse;
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('Apple login failed: ${e.toString()}');
    }
  }

  @override
  Future<AuthResponse> refreshToken(String refreshToken) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        'auth/refresh',
        data: {'refresh_token': refreshToken},
      );

      if (response.data == null) {
        throw const ServerException('Empty response from server');
      }

      final authResponse = AuthResponse.fromJson(response.data!);
      
      // Update tokens
      await _tokenService.updateTokens(
        accessToken: authResponse.accessToken,
        refreshToken: authResponse.refreshToken,
      );
      
      return authResponse;
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('Token refresh failed: ${e.toString()}');
    }
  }

  @override
  Future<void> logout() async {
    try {
      // Clear local authentication data
      await _tokenService.clearAuthData();
      
      // Optionally call server logout endpoint
      // await _apiClient.post('/api/v1/auth/logout');
    } catch (e) {
      throw ServerException('Logout failed: ${e.toString()}');
    }
  }

  @override
  Future<bool> isAuthenticated() async {
    try {
      return await _tokenService.isAuthenticated();
    } catch (e) {
      return false;
    }
  }

  @override
  Future<AuthUser?> getCurrentUser() async {
    try {
      return await _tokenService.getUserData();
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> clearAuthData() async {
    try {
      await _tokenService.clearAuthData();
    } catch (e) {
      throw CacheException('Failed to clear auth data: ${e.toString()}');
    }
  }
}
