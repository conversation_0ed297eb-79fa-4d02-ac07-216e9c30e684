import 'dart:async';
import 'package:banachef/core/bloc/bloc_exports.dart';
import 'splash_state.dart';

/// Cubit for managing splash screen state and navigation logic
class SplashCubit extends BaseCubit<SplashState> {
  SplashCubit() : super(const SplashInitialState());

  /// Duration for splash screen display
  static const Duration _splashDuration = Duration(seconds: 3);

  /// Start splash screen sequence
  void startSplash() {
    emit(const SplashAnimatingState());
    
    // Auto-navigate after splash duration
    Timer(_splashDuration, () {
      emit(const SplashCompletedState());
    });
  }

  /// Complete splash immediately (for testing or user interaction)
  void completeSplash() {
    emit(const SplashCompletedState());
  }

  @override
  void reset() {
    emit(const SplashInitialState());
  }
}
