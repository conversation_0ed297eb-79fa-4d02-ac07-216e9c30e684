import 'package:banachef/core/bloc/bloc_exports.dart';

/// States for splash screen
abstract class SplashState extends BaseState {
  const SplashState();
}

/// Initial state when splash screen starts
class SplashInitialState extends SplashState {
  const SplashInitialState();

  @override
  String get stateName => 'SplashInitialState';
}

/// State when splash animation is playing
class SplashAnimatingState extends SplashState {
  const SplashAnimatingState();

  @override
  String get stateName => 'SplashAnimatingState';
}

/// State when splash is completed and ready to navigate
class SplashCompletedState extends SplashState {
  const SplashCompletedState();

  @override
  String get stateName => 'SplashCompletedState';
}
