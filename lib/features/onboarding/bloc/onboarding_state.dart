import '../../../core/bloc/bloc_exports.dart';
import '../models/onboarding_data.dart';
import '../models/onboarding_constants.dart';

/// Base class for onboarding states
abstract class OnboardingState extends BaseState {
  const OnboardingState();
}

/// Initial state
class OnboardingInitial extends OnboardingState implements BaseInitialState {
  const OnboardingInitial();

  @override
  String toString() => 'OnboardingInitial';
}

/// Loading state
class OnboardingLoading extends OnboardingState implements BaseLoadingState {
  const OnboardingLoading();

  @override
  bool get isLoading => true;

  @override
  String toString() => 'OnboardingLoading';
}

/// State when onboarding is in progress
class OnboardingInProgress extends OnboardingState {
  final OnboardingData data;
  final int currentStepIndex;
  final List<OnboardingStep> allSteps;
  final bool canGoNext;
  final bool canGoPrevious;

  const OnboardingInProgress({
    required this.data,
    required this.currentStepIndex,
    required this.allSteps,
    this.canGoNext = false,
    this.canGoPrevious = false,
  });

  /// Get current step
  OnboardingStep get currentStep => allSteps[currentStepIndex];

  /// Get current segment index
  int get currentSegment => currentStep.segment;

  /// Get progress for current segment (0.0 to 1.0)
  double get currentSegmentProgress {
    final segmentSteps = OnboardingConstants.getStepsForSegment(currentSegment);
    final currentSegmentStepIndex = segmentSteps.indexWhere((step) => step.id == currentStep.id);
    return (currentSegmentStepIndex + 1) / segmentSteps.length;
  }

  /// Get overall progress (0.0 to 1.0)
  double get overallProgress => (currentStepIndex + 1) / allSteps.length;

  /// Get segment progress for each segment
  List<double> get segmentProgresses {
    final progresses = <double>[];
    
    for (int segment = 0; segment < OnboardingConstants.totalSegments; segment++) {
      if (segment < currentSegment) {
        // Completed segments
        progresses.add(1.0);
      } else if (segment == currentSegment) {
        // Current segment
        progresses.add(currentSegmentProgress);
      } else {
        // Future segments
        progresses.add(0.0);
      }
    }
    
    return progresses;
  }

  /// Check if current step is conditional and should be shown
  bool get shouldShowCurrentStep {
    if (!currentStep.conditional) return true;
    
    // Pregnancy status step only for females
    if (currentStep.id == 'pregnancy_status') {
      return data.gender == Gender.female;
    }
    
    return true;
  }

  /// Get filtered steps (excluding conditional steps that shouldn't be shown)
  List<OnboardingStep> get filteredSteps {
    return allSteps.where((step) {
      if (!step.conditional) return true;
      
      // Pregnancy status step only for females
      if (step.id == 'pregnancy_status') {
        return data.gender == Gender.female;
      }
      
      return true;
    }).toList();
  }

  /// Copy with method
  OnboardingInProgress copyWith({
    OnboardingData? data,
    int? currentStepIndex,
    List<OnboardingStep>? allSteps,
    bool? canGoNext,
    bool? canGoPrevious,
  }) {
    return OnboardingInProgress(
      data: data ?? this.data,
      currentStepIndex: currentStepIndex ?? this.currentStepIndex,
      allSteps: allSteps ?? this.allSteps,
      canGoNext: canGoNext ?? this.canGoNext,
      canGoPrevious: canGoPrevious ?? this.canGoPrevious,
    );
  }

  @override
  bool get isSuccess => false;

  @override
  List<Object?> get props => [
        data,
        currentStepIndex,
        allSteps,
        canGoNext,
        canGoPrevious,
      ];

  @override
  String toString() => 'OnboardingInProgress(step: ${currentStep.id}, segment: $currentSegment)';
}

/// State when onboarding is completed but needs authentication
class OnboardingAwaitingAuth extends OnboardingState {
  final OnboardingData onboardingData;

  const OnboardingAwaitingAuth(this.onboardingData);

  @override
  List<Object?> get props => [onboardingData];

  @override
  String toString() => 'OnboardingAwaitingAuth(onboardingData: $onboardingData)';
}

/// State when authentication is in progress
class OnboardingAuthenticating extends OnboardingState {
  final OnboardingData data;
  final String authMethod; // 'apple', 'google'

  const OnboardingAuthenticating(this.data, this.authMethod);

  @override
  List<Object?> get props => [data, authMethod];

  @override
  String toString() => 'OnboardingAuthenticating(data: $data, authMethod: $authMethod)';
}

/// State when authentication fails
class OnboardingAuthError extends OnboardingState {
  final OnboardingData data;
  final String error;

  const OnboardingAuthError(this.data, this.error);

  @override
  List<Object?> get props => [data, error];

  @override
  String toString() => 'OnboardingAuthError(data: $data, error: $error)';
}

/// State when onboarding is completed with successful authentication
class OnboardingCompleted extends OnboardingState implements BaseSuccessState<OnboardingData> {
  final OnboardingData _data;
  final String userId;
  final String email;
  final String? displayName;
  @override
  final String? message;

  const OnboardingCompleted(
    this._data, {
    required this.userId,
    required this.email,
    this.displayName,
    this.message,
  });

  @override
  OnboardingData? get data => _data;

  // Getter for non-nullable access
  OnboardingData get onboardingData => _data;

  @override
  bool get isSuccess => true;

  @override
  List<Object?> get props => [_data, userId, email, displayName, message];

  @override
  String toString() => 'OnboardingCompleted(data: $_data, userId: $userId, email: $email, displayName: $displayName)';
}

/// Error state
class OnboardingError extends OnboardingState implements BaseErrorState {
  @override
  final String message;
  @override
  final String? errorCode;
  @override
  final dynamic originalError;
  @override
  final StackTrace? stackTrace;

  const OnboardingError({
    required this.message,
    this.errorCode,
    this.originalError,
    this.stackTrace,
  });

  @override
  bool get isError => true;

  @override
  List<Object?> get props => [message, errorCode, originalError];

  @override
  String toString() => 'OnboardingError(message: $message)';
}
