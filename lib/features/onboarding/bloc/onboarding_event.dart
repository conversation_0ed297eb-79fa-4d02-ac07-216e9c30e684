import '../../../core/bloc/bloc_exports.dart';
import '../models/onboarding_data.dart';

/// Base class for onboarding events
abstract class OnboardingEvent extends BaseEvent {
  const OnboardingEvent();
}

/// Event to start onboarding
class StartOnboardingEvent extends OnboardingEvent {
  const StartOnboardingEvent();

  @override
  String toString() => 'StartOnboardingEvent';
}

/// Event to go to next step
class NextStepEvent extends OnboardingEvent {
  const NextStepEvent();

  @override
  String toString() => 'NextStepEvent';
}

/// Event to go to previous step
class PreviousStepEvent extends OnboardingEvent {
  const PreviousStepEvent();

  @override
  String toString() => 'PreviousStepEvent';
}

/// Event to skip current step
class SkipStepEvent extends OnboardingEvent {
  const SkipStepEvent();

  @override
  String toString() => 'SkipStepEvent';
}

/// Event to go to specific step
class GoToStepEvent extends OnboardingEvent {
  final int stepIndex;

  const GoToStepEvent(this.stepIndex);

  @override
  List<Object?> get props => [stepIndex];

  @override
  String toString() => 'GoToStepEvent(stepIndex: $stepIndex)';
}

/// Event to update gender
class UpdateGenderEvent extends OnboardingEvent {
  final Gender gender;

  const UpdateGenderEvent(this.gender);

  @override
  List<Object?> get props => [gender];

  @override
  String toString() => 'UpdateGenderEvent(gender: $gender)';
}

/// Event to update height and weight
class UpdateHeightWeightEvent extends OnboardingEvent {
  final double? height;
  final double? weight;

  const UpdateHeightWeightEvent({this.height, this.weight});

  @override
  List<Object?> get props => [height, weight];

  @override
  String toString() => 'UpdateHeightWeightEvent(height: $height, weight: $weight)';
}

/// Event to update birth date
class UpdateBirthDateEvent extends OnboardingEvent {
  final DateTime birthDate;

  const UpdateBirthDateEvent(this.birthDate);

  @override
  List<Object?> get props => [birthDate];

  @override
  String toString() => 'UpdateBirthDateEvent(birthDate: $birthDate)';
}

/// Event to update allergies
class UpdateAllergiesEvent extends OnboardingEvent {
  final List<String> allergies;

  const UpdateAllergiesEvent(this.allergies);

  @override
  List<Object?> get props => [allergies];

  @override
  String toString() => 'UpdateAllergiesEvent(allergies: $allergies)';
}

/// Event to update medical conditions
class UpdateMedicalConditionsEvent extends OnboardingEvent {
  final List<String> medicalConditions;

  const UpdateMedicalConditionsEvent(this.medicalConditions);

  @override
  List<Object?> get props => [medicalConditions];

  @override
  String toString() => 'UpdateMedicalConditionsEvent(medicalConditions: $medicalConditions)';
}

/// Event to update pregnancy status
class UpdatePregnancyStatusEvent extends OnboardingEvent {
  final PregnancyStatus pregnancyStatus;

  const UpdatePregnancyStatusEvent(this.pregnancyStatus);

  @override
  List<Object?> get props => [pregnancyStatus];

  @override
  String toString() => 'UpdatePregnancyStatusEvent(pregnancyStatus: $pregnancyStatus)';
}

/// Event to update favorite food
class UpdateFavoriteFoodEvent extends OnboardingEvent {
  final List<String> favoriteFood;

  const UpdateFavoriteFoodEvent(this.favoriteFood);

  @override
  List<Object?> get props => [favoriteFood];

  @override
  String toString() => 'UpdateFavoriteFoodEvent(favoriteFood: $favoriteFood)';
}

/// Event to update disliked food
class UpdateDislikedFoodEvent extends OnboardingEvent {
  final List<String> dislikedFood;

  const UpdateDislikedFoodEvent(this.dislikedFood);

  @override
  List<Object?> get props => [dislikedFood];

  @override
  String toString() => 'UpdateDislikedFoodEvent(dislikedFood: $dislikedFood)';
}

/// Event to update spice level (0-5)
class UpdateSpiceLevelEvent extends OnboardingEvent {
  final int spiceLevel;

  const UpdateSpiceLevelEvent(this.spiceLevel);

  @override
  List<Object?> get props => [spiceLevel];

  @override
  String toString() => 'UpdateSpiceLevelEvent(spiceLevel: $spiceLevel)';
}

/// Event to update texture preferences
class UpdateTexturePreferencesEvent extends OnboardingEvent {
  final List<String> texturePreferences;

  const UpdateTexturePreferencesEvent(this.texturePreferences);

  @override
  List<Object?> get props => [texturePreferences];

  @override
  String toString() => 'UpdateTexturePreferencesEvent(texturePreferences: $texturePreferences)';
}

/// Event to update cuisine preferences
class UpdateCuisinePreferencesEvent extends OnboardingEvent {
  final List<String> cuisinePreferences;

  const UpdateCuisinePreferencesEvent(this.cuisinePreferences);

  @override
  List<Object?> get props => [cuisinePreferences];

  @override
  String toString() => 'UpdateCuisinePreferencesEvent(cuisinePreferences: $cuisinePreferences)';
}

/// Event to update dietary lifestyles (multi-selection)
class UpdateDietaryLifestyleEvent extends OnboardingEvent {
  final List<String> dietaryLifestyles;

  const UpdateDietaryLifestyleEvent(this.dietaryLifestyles);

  @override
  List<Object?> get props => [dietaryLifestyles];

  @override
  String toString() => 'UpdateDietaryLifestyleEvent(dietaryLifestyles: $dietaryLifestyles)';
}

/// Event to update cooking skill and time
class UpdateCookingSkillTimeEvent extends OnboardingEvent {
  final CookingSkill? cookingSkill;
  final CookingTime? cookingTime;

  const UpdateCookingSkillTimeEvent({this.cookingSkill, this.cookingTime});

  @override
  List<Object?> get props => [cookingSkill, cookingTime];

  @override
  String toString() => 'UpdateCookingSkillTimeEvent(cookingSkill: $cookingSkill, cookingTime: $cookingTime)';
}

/// Event to update kitchen equipment
class UpdateKitchenEquipmentEvent extends OnboardingEvent {
  final List<String> kitchenEquipment;

  const UpdateKitchenEquipmentEvent(this.kitchenEquipment);

  @override
  List<Object?> get props => [kitchenEquipment];

  @override
  String toString() => 'UpdateKitchenEquipmentEvent(kitchenEquipment: $kitchenEquipment)';
}

/// Event to update meal suggestions
class UpdateMealSuggestionsEvent extends OnboardingEvent {
  final List<String> mealSuggestions;

  const UpdateMealSuggestionsEvent(this.mealSuggestions);

  @override
  List<Object?> get props => [mealSuggestions];

  @override
  String toString() => 'UpdateMealSuggestionsEvent(mealSuggestions: $mealSuggestions)';
}

/// Event to show authentication bottom sheet
class ShowAuthBottomSheetEvent extends OnboardingEvent {
  const ShowAuthBottomSheetEvent();

  @override
  List<Object?> get props => [];

  @override
  String toString() => 'ShowAuthBottomSheetEvent()';
}

/// Event for Apple Sign In
class AppleSignInEvent extends OnboardingEvent {
  const AppleSignInEvent();

  @override
  List<Object?> get props => [];

  @override
  String toString() => 'AppleSignInEvent()';
}

/// Event for Google Sign In
class GoogleSignInEvent extends OnboardingEvent {
  const GoogleSignInEvent();

  @override
  List<Object?> get props => [];

  @override
  String toString() => 'GoogleSignInEvent()';
}

/// Event for Email Sign In
class EmailSignInEvent extends OnboardingEvent {
  const EmailSignInEvent();

  @override
  List<Object?> get props => [];

  @override
  String toString() => 'EmailSignInEvent()';
}

/// Event for authentication success
class AuthSuccessEvent extends OnboardingEvent {
  final String userId;
  final String email;
  final String? displayName;

  const AuthSuccessEvent({
    required this.userId,
    required this.email,
    this.displayName,
  });

  @override
  List<Object?> get props => [userId, email, displayName];

  @override
  String toString() => 'AuthSuccessEvent(userId: $userId, email: $email, displayName: $displayName)';
}

/// Event for authentication failure
class AuthFailureEvent extends OnboardingEvent {
  final String error;

  const AuthFailureEvent(this.error);

  @override
  List<Object?> get props => [error];

  @override
  String toString() => 'AuthFailureEvent(error: $error)';
}

/// Event to complete onboarding
class CompleteOnboardingEvent extends OnboardingEvent {
  const CompleteOnboardingEvent();

  @override
  String toString() => 'CompleteOnboardingEvent';
}
