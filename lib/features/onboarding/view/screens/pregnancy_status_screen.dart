import 'package:flutter/material.dart';
import '../../../../ui/responsive/responsive.dart';
import '../../models/onboarding_data.dart';
import '../../widgets/selection_card.dart';

/// Screen for pregnancy status selection (conditional - only for females)
class PregnancyStatusScreen extends StatelessWidget {
  final PregnancyStatus? selectedStatus;
  final void Function(PregnancyStatus status) onStatusSelected;

  const PregnancyStatusScreen({
    super.key,
    this.selectedStatus,
    required this.onStatusSelected,
  });

  @override
  Widget build(BuildContext context) {
    final options = [
      SelectionCardOption(
        value: PregnancyStatus.pregnant.name,
        text: PregnancyStatus.pregnant.displayName,
        icon: '🤰',
      ),
      SelectionCardOption(
        value: PregnancyStatus.breastfeeding.name,
        text: PregnancyStatus.breastfeeding.displayName,
        icon: '🤱',
      ),
      SelectionCardOption(
        value: PregnancyStatus.none.name,
        text: PregnancyStatus.none.displayName,
        icon: '👩',
      ),
    ];

    return Column(
      children: [
        // Description
        Text(
          'Thông tin này giúp chúng tôi đề xuất các món ăn an toàn và bổ dưỡng phù hợp với tình trạng của bạn.',
          style: context.bodyLarge.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
          textAlign: TextAlign.center,
        ),
        
        VSpace.xl(),
        
        // Selection cards
        Expanded(
          child: Center(
            child: SelectionCardGrid(
              options: options,
              selectedValue: selectedStatus?.name,
              onSelectionChanged: (value) {
                final status = PregnancyStatus.values.firstWhere((s) => s.name == value);
                onStatusSelected(status);
              },
              columns: 3,
            ),
          ),
        ),
      ],
    );
  }
}
