import 'package:flutter/material.dart';
import '../../models/onboarding_constants.dart';
import '../../widgets/chip_selector.dart';

class CuisinePreferencesScreen extends StatelessWidget {
  final List<String> selectedCuisines;
  final void Function(List<String> cuisines) onCuisinesChanged;

  const CuisinePreferencesScreen({super.key, required this.selectedCuisines, required this.onCuisinesChanged});

  @override
  Widget build(BuildContext context) {
    final cuisineNames = OnboardingConstants.cuisinePreferences.map((c) => c['name']!).toList();
    return SingleChildScrollView(
      child: ChipSelector(
        options: cuisineNames,
        selectedValues: selectedCuisines,
        onSelectionChanged: onCuisinesChanged,
        allowCustom: true,
        hasNoneOption: false,
      ),
    );
  }
}
