import 'package:flutter/material.dart';
import '../../models/onboarding_constants.dart';
import '../../widgets/chip_selector.dart';

class DietaryLifestyleScreen extends StatelessWidget {
  final List<String> selectedLifestyles;
  final void Function(List<String> lifestyles) onLifestylesChanged;

  const DietaryLifestyleScreen({
    super.key,
    required this.selectedLifestyles,
    required this.onLifestylesChanged,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: ChipSelector(
        options: OnboardingConstants.dietaryLifestyles,
        selectedValues: selectedLifestyles,
        onSelectionChanged: onLifestylesChanged,
        allowCustom: true,
        hasNoneOption: false, // Không có "Không có" vì mọi người đều có phong cách ăn uống
        customPlaceholder: 'Nhập phong cách ăn uống khác...',
      ),
    );
  }
}
