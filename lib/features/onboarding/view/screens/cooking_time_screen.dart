import 'package:flutter/material.dart';
import '../../models/onboarding_data.dart';
import '../../widgets/selection_card.dart';

/// Screen for cooking time preference selection
class CookingTimeScreen extends StatelessWidget {
  final CookingTime? selectedTime;
  final void Function(CookingTime time) onTimeSelected;

  const CookingTimeScreen({
    super.key,
    this.selectedTime,
    required this.onTimeSelected,
  });

  @override
  Widget build(BuildContext context) {
    final options = [
      SelectionCardOption(
        value: CookingTime.quick.name,
        text: CookingTime.quick.displayName,
        icon: CookingTime.quick.icon,
      ),
      SelectionCardOption(
        value: CookingTime.moderate.name,
        text: CookingTime.moderate.displayName,
        icon: CookingTime.moderate.icon,
      ),
      SelectionCardOption(
        value: CookingTime.long.name,
        text: CookingTime.long.displayName,
        icon: CookingTime.long.icon,
      ),
      SelectionCardOption(
        value: CookingTime.noPreference.name,
        text: CookingTime.noPreference.displayName,
        icon: CookingTime.noPreference.icon,
      ),
    ];

    return Center(
      child: SelectionCardGrid(
        options: options,
        selectedValue: selectedTime?.name,
        onSelectionChanged: (value) {
          final time = CookingTime.values.firstWhere((t) => t.name == value);
          onTimeSelected(time);
        },
        columns: 2,
      ),
    );
  }
}
