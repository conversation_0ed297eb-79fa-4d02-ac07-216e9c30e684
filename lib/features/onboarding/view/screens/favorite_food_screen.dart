import 'package:flutter/material.dart';
import '../../models/onboarding_constants.dart';
import '../../widgets/chip_selector.dart';

class FavoriteFoodScreen extends StatelessWidget {
  final List<String> selectedFood;
  final void Function(List<String> food) onFoodChanged;

  const FavoriteFoodScreen({
    super.key,
    required this.selectedFood,
    required this.onFoodChanged,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: ChipSelector(
        options: OnboardingConstants.commonFavoriteFood,
        selectedValues: selectedFood,
        onSelectionChanged: onFoodChanged,
        allowCustom: true,
        hasNoneOption: false,
      ),
    );
  }
}
