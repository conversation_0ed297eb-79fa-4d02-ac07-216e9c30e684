import 'package:flutter/material.dart';
import '../../models/onboarding_constants.dart';
import '../../widgets/chip_selector.dart';

class KitchenEquipmentScreen extends StatelessWidget {
  final List<String> selectedEquipment;
  final void Function(List<String> equipment) onEquipmentChanged;

  const KitchenEquipmentScreen({super.key, required this.selectedEquipment, required this.onEquipmentChanged});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: ChipSelector(
        options: OnboardingConstants.kitchenEquipment,
        selectedValues: selectedEquipment,
        onSelectionChanged: onEquipmentChanged,
        allowCustom: true,
        hasNoneOption: false,
      ),
    );
  }
}
