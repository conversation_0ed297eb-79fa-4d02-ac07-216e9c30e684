import 'package:flutter/material.dart';
import '../../../../ui/responsive/responsive_spacing.dart';
import '../../models/onboarding_constants.dart';

class MealSuggestionsScreen extends StatefulWidget {
  final List<String> selectedSuggestions;
  final void Function(List<String> suggestions) onSuggestionsChanged;

  const MealSuggestionsScreen({
    super.key,
    required this.selectedSuggestions,
    required this.onSuggestionsChanged,
  });

  @override
  State<MealSuggestionsScreen> createState() => _MealSuggestionsScreenState();
}

class _MealSuggestionsScreenState extends State<MealSuggestionsScreen> {
  final TextEditingController _customController = TextEditingController();
  bool _showCustomInput = false;
  List<String> _allOptions = [];

  @override
  void initState() {
    super.initState();
    _allOptions = List.from(OnboardingConstants.mealSuggestions);

    // Add any custom selections that aren't in the default options
    for (final selection in widget.selectedSuggestions) {
      if (!_allOptions.contains(selection)) {
        _allOptions.add(selection);
      }
    }
  }

  @override
  void dispose() {
    _customController.dispose();
    super.dispose();
  }

  void _handleSelection(String value) {
    final currentValues = List<String>.from(widget.selectedSuggestions);

    // Toggle the selected value
    if (currentValues.contains(value)) {
      currentValues.remove(value);
    } else {
      currentValues.add(value);
    }

    widget.onSuggestionsChanged(currentValues);
  }

  void _addCustomOption() {
    final customValue = _customController.text.trim();
    if (customValue.isNotEmpty && !_allOptions.contains(customValue)) {
      setState(() {
        _allOptions.add(customValue);
        _showCustomInput = false;
        _customController.clear();
      });
      _handleSelection(customValue);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          ..._allOptions.map((option) => _buildMealButton(option, theme)),

          // Add custom button or input
          if (_showCustomInput)
            _buildCustomInput(theme)
          else
            _buildAddCustomButton(theme),

          // Add some bottom padding
          SizedBox(height: context.spacingXL),
        ],
      ),
    );
  }

  Widget _buildMealButton(String option, ThemeData theme) {
    final isSelected = widget.selectedSuggestions.contains(option);

    return Padding(
      padding: EdgeInsets.only(bottom: context.spacingSM),
      child: GestureDetector(
        onTap: () => _handleSelection(option),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: EdgeInsets.symmetric(
            horizontal: context.spacingLG,
            vertical: context.spacingMD,
          ),
          decoration: BoxDecoration(
            color: isSelected
                ? const Color(OnboardingConstants.completedColor)
                : theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(25), // Pill shape
            border: Border.all(
              color: isSelected
                  ? const Color(OnboardingConstants.completedColor)
                  : theme.colorScheme.outline.withValues(alpha: 0.3),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: theme.shadowColor.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Text(
            option,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: isSelected
                  ? Colors.white
                  : theme.colorScheme.onSurface,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _buildAddCustomButton(ThemeData theme) {
    return Padding(
      padding: EdgeInsets.only(top: context.spacingSM),
      child: GestureDetector(
        onTap: () => setState(() => _showCustomInput = true),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: context.spacingLG,
            vertical: context.spacingMD,
          ),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(25), // Pill shape
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.4), // Nhạt hơn như placeholder
              width: 1.0, // Mỏng hơn
              style: BorderStyle.solid,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.add,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5), // Nhạt như placeholder
                size: 16, // Nhỏ hơn
              ),
              SizedBox(width: context.spacingSM),
              Text(
                'Thêm gợi ý khác',
                style: theme.textTheme.bodySmall?.copyWith( // Nhỏ hơn từ bodyLarge → bodySmall
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.5), // Nhạt như placeholder
                  fontWeight: FontWeight.w400, // Nhẹ hơn
                  fontSize: 13, // Font size nhỏ hơn
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomInput(ThemeData theme) {
    return Padding(
      padding: EdgeInsets.only(top: context.spacingSM),
      child: Container(
        padding: EdgeInsets.all(context.spacingMD),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(12), // Rounded rectangle
          border: Border.all(
            color: theme.colorScheme.outline.withValues(alpha: 0.3),
            width: 1.5,
          ),
        ),
        child: Column(
          children: [
            TextField(
              controller: _customController,
              style: theme.textTheme.bodySmall?.copyWith( // Nhỏ hơn cho input text
                fontSize: 13,
              ),
              decoration: InputDecoration(
                hintText: 'Nhập gợi ý món ăn khác...',
                hintStyle: theme.textTheme.bodySmall?.copyWith( // Styling cho placeholder
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                  fontSize: 13,
                  fontWeight: FontWeight.w400,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: theme.colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: theme.colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: theme.colorScheme.primary,
                    width: 1.5,
                  ),
                ),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: context.spacingMD,
                  vertical: context.spacingSM,
                ),
                isDense: true, // Compact layout
              ),
              onSubmitted: (_) => _addCustomOption(),
            ),
            SizedBox(height: context.spacingSM),
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => setState(() {
                      _showCustomInput = false;
                      _customController.clear();
                    }),
                    child: Text('Hủy'),
                  ),
                ),
                SizedBox(width: context.spacingSM),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _addCustomOption,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(OnboardingConstants.completedColor),
                      foregroundColor: Colors.white,
                    ),
                    child: Text('Thêm'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
