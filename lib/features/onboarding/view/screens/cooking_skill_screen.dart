import 'package:flutter/material.dart';
import '../../models/onboarding_data.dart';
import '../../widgets/selection_card.dart';

/// Screen for cooking skill selection
class CookingSkillScreen extends StatelessWidget {
  final CookingSkill? selectedSkill;
  final void Function(CookingSkill skill) onSkillSelected;

  const CookingSkillScreen({
    super.key,
    this.selectedSkill,
    required this.onSkillSelected,
  });

  @override
  Widget build(BuildContext context) {
    final options = [
      SelectionCardOption(
        value: CookingSkill.beginner.name,
        text: CookingSkill.beginner.displayName,
        icon: CookingSkill.beginner.icon,
      ),
      SelectionCardOption(
        value: CookingSkill.intermediate.name,
        text: CookingSkill.intermediate.displayName,
        icon: CookingSkill.intermediate.icon,
      ),
      SelectionCardOption(
        value: CookingSkill.advanced.name,
        text: CookingSkill.advanced.displayName,
        icon: CookingSkill.advanced.icon,
      ),
      SelectionCardOption(
        value: CookingSkill.expert.name,
        text: CookingSkill.expert.displayName,
        icon: CookingSkill.expert.icon,
      ),
    ];

    return Center(
      child: SelectionCardGrid(
        options: options,
        selectedValue: selectedSkill?.name,
        onSelectionChanged: (value) {
          final skill = CookingSkill.values.firstWhere((s) => s.name == value);
          onSkillSelected(skill);
        },
        columns: 2,
      ),
    );
  }
}
