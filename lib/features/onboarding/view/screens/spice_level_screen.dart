import 'package:flutter/material.dart';
import '../../../../ui/responsive/responsive.dart';
import '../../models/onboarding_constants.dart';

/// Screen for selecting spice level preference (0-5)
class SpiceLevelScreen extends StatelessWidget {
  final int spiceLevel;
  final void Function(int level) onSpiceLevelChanged;

  const SpiceLevelScreen({
    super.key,
    required this.spiceLevel,
    required this.onSpiceLevelChanged,
  });

  /// Get color for spice level (0: green -> 5: red)
  Color _getSpiceLevelColor(int level) {
    switch (level) {
      case 0: return const Color(0xFF4CAF50); // Green - Không cay
      case 1: return const Color(0xFF8BC34A); // Light Green - Rất nhẹ
      case 2: return const Color(0xFFFFEB3B); // Yellow - Nhẹ
      case 3: return const Color(0xFFFF9800); // Orange - Vừa
      case 4: return const Color(0xFFFF5722); // Deep Orange - Cay
      case 5: return const Color(0xFFF44336); // Red - Rất cay
      default: return const Color(0xFFFFEB3B);
    }
  }

  /// Get emoji for spice level
  String _getSpiceLevelEmoji(int level) {
    switch (level) {
      case 0: return '😌'; // Không cay
      case 1: return '🙂'; // Rất nhẹ
      case 2: return '😊'; // Nhẹ
      case 3: return '😅'; // Vừa
      case 4: return '🥵'; // Cay
      case 5: return '🔥'; // Rất cay
      default: return '😊';
    }
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveContainer(
      padding: context.contentPadding,
      child: Column(
        children: [
          // Current selection display
          Container(
            padding: EdgeInsets.all(context.spacingLG),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  _getSpiceLevelColor(spiceLevel).withValues(alpha: 0.1),
                  _getSpiceLevelColor(spiceLevel).withValues(alpha: 0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: _getSpiceLevelColor(spiceLevel).withValues(alpha: 0.3),
                width: 1.5,
              ),
            ),
            child: Column(
              children: [
                Text(
                  'Mức độ hiện tại',
                  style: context.bodyMedium.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                VSpace.sm(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _getSpiceLevelEmoji(spiceLevel),
                      style: TextStyle(fontSize: 24),
                    ),
                    HSpace.sm(),
                    Text(
                      OnboardingConstants.spiceLevelNames[spiceLevel],
                      style: context.headlineSmall.copyWith(
                        fontWeight: FontWeight.w600,
                        color: _getSpiceLevelColor(spiceLevel),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        
          
          // Slider
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Modern segmented slider
                _ModernSpiceSlider(
                  spiceLevel: spiceLevel,
                  onChanged: onSpiceLevelChanged,
                  getColor: _getSpiceLevelColor,
                  getEmoji: _getSpiceLevelEmoji,
                )
                
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Modern custom spice slider widget
class _ModernSpiceSlider extends StatelessWidget {
  final int spiceLevel;
  final void Function(int) onChanged;
  final Color Function(int) getColor;
  final String Function(int) getEmoji;

  const _ModernSpiceSlider({
    required this.spiceLevel,
    required this.onChanged,
    required this.getColor,
    required this.getEmoji,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: context.responsive(
        ResponsiveValue(mobile: 80.0, tablet: 90.0, desktop: 100.0),
      ),
      padding: EdgeInsets.symmetric(horizontal: context.spacingLG),
      child: Column(
        children: [
          // Segmented track
          Container(
            height: context.responsive(
              ResponsiveValue(mobile: 60.0, tablet: 70.0, desktop: 80.0),
            ),
            child: Row(
              children: List.generate(6, (index) {
                final isSelected = index == spiceLevel;
                final isActive = index <= spiceLevel;

                return Expanded(
                  child: GestureDetector(
                    onTap: () => onChanged(index),
                    child: Container(
                      margin: EdgeInsets.symmetric(
                        horizontal: context.spacingXS / 2,
                      ),
                      child: Column(
                        children: [
                          // Segment indicator
                          Expanded(
                            child: AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                              decoration: BoxDecoration(
                                gradient: isActive ? LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    getColor(index),
                                    getColor(index).withValues(alpha: 0.7),
                                  ],
                                ) : null,
                                color: !isActive ? Theme.of(context).colorScheme.surfaceContainerHighest : null,
                                borderRadius: BorderRadius.circular(
                                  context.responsive(
                                    ResponsiveValue(mobile: 8.0, tablet: 10.0, desktop: 12.0),
                                  ),
                                ),
                                border: Border.all(
                                  color: isSelected
                                      ? getColor(index)
                                      : Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                                  width: isSelected ? 2.5 : 1,
                                ),
                                boxShadow: isSelected ? [
                                  BoxShadow(
                                    color: getColor(index).withValues(alpha: 0.3),
                                    blurRadius: 8,
                                    spreadRadius: 1,
                                    offset: const Offset(0, 2),
                                  ),
                                ] : null,
                              ),
                              child: Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    // Emoji
                                    Text(
                                      getEmoji(index),
                                      style: TextStyle(
                                        fontSize: context.responsive(
                                          ResponsiveValue(mobile: 16.0, tablet: 18.0, desktop: 20.0),
                                        ),
                                      ),
                                    ),
                                    VSpace.xs(),
                                    // Level number
                                    Text(
                                      index.toString(),
                                      style: context.bodySmall.copyWith(
                                        color: isActive
                                            ? Colors.white
                                            : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                                        fontWeight: isSelected ? FontWeight.w700 : FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }),
            ),
          ),

          VSpace.sm(),

          // Level names
          Row(
            children: List.generate(6, (index) {
              final isSelected = index == spiceLevel;
              return Expanded(
                child: Text(
                  OnboardingConstants.spiceLevelNames[index],
                  style: context.bodySmall.copyWith(
                    color: isSelected
                        ? getColor(index)
                        : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                    fontSize: context.responsive(
                      ResponsiveValue(mobile: 10.0, tablet: 11.0, desktop: 12.0),
                    ),
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
}
