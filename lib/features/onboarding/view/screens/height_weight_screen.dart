import 'package:flutter/material.dart';
import '../../../../ui/responsive/responsive.dart';
import '../../../../core/theme/app_colors.dart';
import '../../widgets/responsive_wheel_picker.dart';
import '../../widgets/imperial_height_picker.dart';
import '../../models/measurement_unit.dart';

/// Screen for height and weight selection using wheel pickers
class HeightWeightScreen extends StatefulWidget {
  final double? height;
  final double? weight;
  final void Function(double height) onHeightChanged;
  final void Function(double weight) onWeightChanged;

  const HeightWeightScreen({
    super.key,
    this.height,
    this.weight,
    required this.onHeightChanged,
    required this.onWeightChanged,
  });

  @override
  State<HeightWeightScreen> createState() => _HeightWeightScreenState();
}

class _HeightWeightScreenState extends State<HeightWeightScreen> {
  MeasurementUnit _currentUnit = MeasurementUnit.metric;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentHeight = widget.height ?? 170.0;
    final currentWeight = widget.weight ?? 60.0;

    final (heightMin, heightMax, heightStep) = UnitConverter.getHeightRange(_currentUnit);
    final (weightMin, weightMax, weightStep) = UnitConverter.getWeightRange(_currentUnit);

    return Column(
      children: [
        // Unit toggle
        Container(
          margin: EdgeInsets.only(bottom: context.spacingLG),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildUnitToggle(context, theme),
            ],
          ),
        ),

        // Height and Weight sections side by side
        Expanded(
          child: Row(
            children: [
              // Height section
              Expanded(
                child: _currentUnit == MeasurementUnit.metric
                  ? NumericWheelPicker(
                      title: 'Chiều cao',
                      minValue: heightMin,
                      maxValue: heightMax,
                      step: heightStep,
                      selectedValue: currentHeight,
                      unit: _currentUnit.heightUnit,
                      onValueChanged: widget.onHeightChanged,
                      enableHapticFeedback: true,
                    )
                  : ImperialHeightPicker(
                      title: 'Chiều cao',
                      heightInCm: currentHeight,
                      onHeightChanged: widget.onHeightChanged,
                      enableHapticFeedback: true,
                    ),
              ),

              HSpace.lg(),

              // Weight section
              Expanded(
                child: _buildWeightPicker(context, currentWeight, weightMin, weightMax, weightStep),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildUnitToggle(BuildContext context, ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildUnitButton(
            context,
            theme,
            MeasurementUnit.metric,
            'Metric (kg, cm)',
          ),
          _buildUnitButton(
            context,
            theme,
            MeasurementUnit.imperial,
            'Imperial (lbs, ft)',
          ),
        ],
      ),
    );
  }

  Widget _buildUnitButton(
    BuildContext context,
    ThemeData theme,
    MeasurementUnit unit,
    String label,
  ) {
    final isSelected = _currentUnit == unit;

    return GestureDetector(
      onTap: () {
        setState(() {
          _currentUnit = unit;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: context.spacingMD,
          vertical: context.spacingSM,
        ),
        decoration: BoxDecoration(
          color: isSelected
            ? theme.colorScheme.primary
            : Colors.transparent,
          // Thêm background nhạt cho selected state
          gradient: isSelected ? LinearGradient(
            colors: [
              AppColors.primary.withValues(alpha: 0.1),
              AppColors.primary.withValues(alpha: 0.05),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ) : null,
          borderRadius: BorderRadius.circular(8),
          // Thêm border để làm rõ hơn selection
          border: Border.all(
            color: isSelected
              ? theme.colorScheme.primary
              : theme.colorScheme.outline.withValues(alpha: 0.3),
            width: isSelected ? 2.0 : 1.0,
          ),
        ),
        child: Text(
          label,
          style: context.bodyMedium.copyWith(
            color: isSelected
              ? theme.colorScheme.onPrimary
              : theme.colorScheme.onSurface.withValues(alpha: 0.7), // Giảm opacity cho unselected
            fontWeight: isSelected ? FontWeight.w700 : FontWeight.w500, // Tăng contrast weight
          ),
        ),
      ),
    );
  }

  Widget _buildWeightPicker(
    BuildContext context,
    double currentWeight,
    double weightMin,
    double weightMax,
    double weightStep,
  ) {
    if (_currentUnit == MeasurementUnit.imperial) {
      // Convert weight to lbs for display but keep internal value in kg
      final weightInLbs = UnitConverter.kgToLbs(currentWeight);
      final minLbs = UnitConverter.kgToLbs(weightMin);
      final maxLbs = UnitConverter.kgToLbs(weightMax);

      return NumericWheelPicker(
        title: 'Cân nặng',
        minValue: minLbs,
        maxValue: maxLbs,
        step: 0.2, // 0.2 lbs step
        selectedValue: weightInLbs,
        unit: _currentUnit.weightUnit,
        onValueChanged: (lbsValue) {
          final kgValue = UnitConverter.lbsToKg(lbsValue);
          widget.onWeightChanged(kgValue);
        },
        enableHapticFeedback: true,
      );
    } else {
      return NumericWheelPicker(
        title: 'Cân nặng',
        minValue: weightMin,
        maxValue: weightMax,
        step: weightStep, // 0.1 kg for better precision
        selectedValue: currentWeight,
        unit: _currentUnit.weightUnit,
        onValueChanged: widget.onWeightChanged,
        enableHapticFeedback: true,
      );
    }
  }

}
