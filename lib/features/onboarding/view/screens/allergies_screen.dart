import 'package:flutter/material.dart';
import '../../../../ui/responsive/responsive.dart';
import '../../models/onboarding_constants.dart';
import '../../widgets/chip_selector.dart';

/// Screen for food allergies selection
class AllergiesScreen extends StatelessWidget {
  final List<String> selectedAllergies;
  final void Function(List<String> allergies) onAllergiesChanged;

  const AllergiesScreen({
    super.key,
    required this.selectedAllergies,
    required this.onAllergiesChanged,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Description
          Text(
            '<PERSON>ui lòng chọn các loại thực phẩm mà bạn bị dị ứng để chúng tôi có thể đề xuất món ăn phù hợp.',
            style: context.bodyLarge.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
          
          VSpace.xl(),
          
          // Chip selector
          ChipSelector(
            options: OnboardingConstants.commonAllergies,
            selectedValues: selectedAllergies,
            onSelectionChanged: onAllergiesChanged,
            allowCustom: true,
            customPlaceholder: 'Nhập loại dị ứng khác...',
            hasNoneOption: true,
            noneValue: 'Không có',
          ),
        ],
      ),
    );
  }
}
