import 'package:flutter/material.dart';
import '../../models/onboarding_data.dart';
import '../../widgets/selection_card.dart';

/// Screen for gender selection
class GenderScreen extends StatelessWidget {
  final Gender? selectedGender;
  final void Function(Gender gender) onGenderSelected;

  const GenderScreen({
    super.key,
    this.selectedGender,
    required this.onGenderSelected,
  });

  @override
  Widget build(BuildContext context) {
    final options = [
      SelectionCardOption(
        value: Gender.male.name,
        text: Gender.male.displayName,
        icon: Gender.male.icon,
      ),
      SelectionCardOption(
        value: Gender.female.name,
        text: Gender.female.displayName,
        icon: Gender.female.icon,
      ),
      SelectionCardOption(
        value: Gender.other.name,
        text: Gender.other.displayName,
        icon: Gender.other.icon,
      ),
    ];

    return Center(
      child: SelectionCardGrid(
        options: options,
        selectedValue: selectedGender?.name,
        onSelectionChanged: (value) {
          final gender = Gender.values.firstWhere((g) => g.name == value);
          onGenderSelected(gender);
        },
        columns: 3,
      ),
    );
  }
}
