import 'package:flutter/material.dart';
import '../../widgets/date_wheel_picker.dart';

/// Screen for birth date selection using date wheel picker
class BirthDateScreen extends StatelessWidget {
  final DateTime? selectedDate;
  final void Function(DateTime date) onDateSelected;

  const BirthDateScreen({
    super.key,
    this.selectedDate,
    required this.onDateSelected,
  });

  @override
  Widget build(BuildContext context) {
    return DateWheelPicker(
      selectedDate: selectedDate,
      onDateSelected: onDateSelected,
      enableHapticFeedback: true,
    );
  }
}
