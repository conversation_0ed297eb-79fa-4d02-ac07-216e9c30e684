import 'package:flutter/material.dart';
import '../../../../ui/responsive/responsive.dart';
import '../../models/onboarding_constants.dart';
import '../../widgets/chip_selector.dart';

/// Screen for medical conditions selection
class MedicalConditionsScreen extends StatelessWidget {
  final List<String> selectedConditions;
  final void Function(List<String> conditions) onConditionsChanged;

  const MedicalConditionsScreen({
    super.key,
    required this.selectedConditions,
    required this.onConditionsChanged,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thông tin về tình trạng sức khỏe giúp chúng tôi đề xuất chế độ ăn phù hợp.',
            style: context.bodyLarge.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
          VSpace.xl(),
          ChipSelector(
            options: OnboardingConstants.commonMedicalConditions,
            selectedValues: selectedConditions,
            onSelectionChanged: onConditionsChanged,
            allowCustom: true,
            customPlaceholder: 'Nhập bệnh lý khác...',
            hasNoneOption: true,
            noneValue: 'Không có',
          ),
        ],
      ),
    );
  }
}
