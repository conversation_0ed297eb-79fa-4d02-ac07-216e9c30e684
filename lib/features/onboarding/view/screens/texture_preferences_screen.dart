import 'package:flutter/material.dart';
import '../../../../ui/responsive/responsive.dart';
import '../../models/onboarding_constants.dart';
import '../../widgets/chip_selector.dart';

/// Screen for selecting texture preferences
class TexturePreferencesScreen extends StatelessWidget {
  final List<String> selectedTextures;
  final void Function(List<String> textures) onTexturesChanged;

  const TexturePreferencesScreen({
    super.key,
    required this.selectedTextures,
    required this.onTexturesChanged,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveContainer(
      padding: context.contentPadding,
      child: Column(
        children: [
          // Selection count display
          if (selectedTextures.isNotEmpty) ...[
            Container(
              padding: EdgeInsets.all(context.spacingMD),
              decoration: BoxDecoration(
                color: const Color(OnboardingConstants.completedColor).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: const Color(OnboardingConstants.completedColor).withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.check_circle,
                    size: 16,
                    color: const Color(OnboardingConstants.completedColor),
                  ),
                  HSpace.sm(),
                  Text(
                    'Đã chọn ${selectedTextures.length} kết cấu',
                    style: context.bodyMedium.copyWith(
                      color: const Color(OnboardingConstants.completedColor),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            VSpace.lg(),
          ],
          
          // Chip selector
          Expanded(
            child: ChipSelector(
              options: OnboardingConstants.texturePreferences,
              selectedValues: selectedTextures,
              onSelectionChanged: onTexturesChanged,
              hasNoneOption: true,
              noneValue: 'Không quan trọng',
              allowCustom: false,
            ),
          ),
        ],
      ),
    );
  }
}
