import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/bloc/base_view.dart';

import '../../../ui/responsive/responsive.dart';
import '../bloc/onboarding_bloc.dart';
import '../bloc/onboarding_event.dart';
import '../bloc/onboarding_state.dart';
import '../widgets/onboarding_layout.dart';
import '../widgets/auth_bottom_sheet.dart';
import '../models/onboarding_data.dart';
import '../models/onboarding_constants.dart';
import 'screens/gender_screen.dart';
import 'screens/height_weight_screen.dart';
import 'screens/birth_date_screen.dart';
import 'screens/allergies_screen.dart';
import 'screens/medical_conditions_screen.dart';
import 'screens/pregnancy_status_screen.dart';
import 'screens/favorite_food_screen.dart';
import 'screens/disliked_food_screen.dart';
import 'screens/spice_level_screen.dart';
import 'screens/texture_preferences_screen.dart';
import 'screens/cuisine_preferences_screen.dart';
import 'screens/dietary_lifestyle_screen.dart';
import 'screens/cooking_skill_screen.dart';
import 'screens/cooking_time_screen.dart';
import 'screens/kitchen_equipment_screen.dart';
import 'screens/meal_suggestions_screen.dart';

@RoutePage( name: 'OnboardingViewRoute')
class OnboardingView extends BaseView<OnboardingBloc> {
  const OnboardingView({super.key});

  @override
  Widget buildContent(BuildContext context, dynamic state) {
    return BlocListener<OnboardingBloc, OnboardingState>(
      listener: (context, state) {
        if (state is OnboardingCompleted) {
          // Navigate to main app or show completion
          _handleOnboardingCompleted(context, state.onboardingData);
        } else if (state is OnboardingError) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      },
      child: _buildContent(context, state as OnboardingState),
    );
  }

  Widget _buildContent(BuildContext context, OnboardingState state) {
    if (state is OnboardingInitial) {
      // Start onboarding automatically
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.read<OnboardingBloc>().add(const StartOnboardingEvent());
      });
      return const _LoadingScreen();
    }

    if (state is OnboardingLoading) {
      return const _LoadingScreen();
    }

    if (state is OnboardingInProgress) {
      return _buildOnboardingScreen(context, state);
    }

    if (state is OnboardingAwaitingAuth) {
      return _buildAwaitingAuthScreen(context, state);
    }

    if (state is OnboardingAuthenticating) {
      return _buildAuthenticatingScreen(context, state);
    }

    if (state is OnboardingAuthError) {
      return _buildAuthErrorScreen(context, state);
    }

    if (state is OnboardingCompleted) {
      return _buildCompletionScreen(context, state);
    }

    if (state is OnboardingError) {
      return _buildErrorScreen(context, state);
    }

    return const _LoadingScreen();
  }

  Widget _buildOnboardingScreen(BuildContext context, OnboardingInProgress state) {
    // Skip conditional steps that shouldn't be shown
    if (!state.shouldShowCurrentStep) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.read<OnboardingBloc>().add(const NextStepEvent());
      });
      return const _LoadingScreen();
    }

    final currentStep = state.currentStep;
    
    return OnboardingLayout(
      segmentProgresses: state.segmentProgresses,
      currentSegment: state.currentSegment,
      title: currentStep.title,
      description: currentStep.description,
      showSkip: true,
      onSkip: () => context.read<OnboardingBloc>().add(const SkipStepEvent()),
      canGoNext: state.canGoNext,
      onNext: () {
        // Check if this is the last step
        if (state.currentStepIndex == state.filteredSteps.length - 1) {
          // Show auth bottom sheet directly
          _showAuthBottomSheet(context, state.data);
        } else {
          context.read<OnboardingBloc>().add(const NextStepEvent());
        }
      },
      canGoPrevious: state.canGoPrevious,
      onPrevious: () => context.read<OnboardingBloc>().add(const PreviousStepEvent()),
      nextButtonText: state.currentStepIndex == state.filteredSteps.length - 1
          ? 'Hoàn thành'
          : 'Tiếp theo',
      child: _buildStepContent(context, state),
    );
  }

  Widget _buildStepContent(BuildContext context, OnboardingInProgress state) {
    final stepId = state.currentStep.id;
    final data = state.data;

    switch (stepId) {
      case 'gender':
        return GenderScreen(
          selectedGender: data.gender,
          onGenderSelected: (gender) {
            context.read<OnboardingBloc>().add(UpdateGenderEvent(gender));
          },
        );
        
      case 'height_weight':
        return HeightWeightScreen(
          height: data.height,
          weight: data.weight,
          onHeightChanged: (height) {
            context.read<OnboardingBloc>().add(UpdateHeightWeightEvent(height: height));
          },
          onWeightChanged: (weight) {
            context.read<OnboardingBloc>().add(UpdateHeightWeightEvent(weight: weight));
          },
        );
        
      case 'birth_date':
        return BirthDateScreen(
          selectedDate: data.birthDate,
          onDateSelected: (date) {
            context.read<OnboardingBloc>().add(UpdateBirthDateEvent(date));
          },
        );
        
      case 'allergies':
        return AllergiesScreen(
          selectedAllergies: data.allergies,
          onAllergiesChanged: (allergies) {
            context.read<OnboardingBloc>().add(UpdateAllergiesEvent(allergies));
          },
        );
        
      case 'medical_conditions':
        return MedicalConditionsScreen(
          selectedConditions: data.medicalConditions,
          onConditionsChanged: (conditions) {
            context.read<OnboardingBloc>().add(UpdateMedicalConditionsEvent(conditions));
          },
        );
        
      case 'pregnancy_status':
        return PregnancyStatusScreen(
          selectedStatus: data.pregnancyStatus,
          onStatusSelected: (status) {
            context.read<OnboardingBloc>().add(UpdatePregnancyStatusEvent(status));
          },
        );
        
      case 'favorite_food':
        return FavoriteFoodScreen(
          selectedFood: data.favoriteFood,
          onFoodChanged: (food) {
            context.read<OnboardingBloc>().add(UpdateFavoriteFoodEvent(food));
          },
        );
        
      case 'disliked_food':
        return DislikedFoodScreen(
          selectedFood: data.dislikedFood,
          onFoodChanged: (food) {
            context.read<OnboardingBloc>().add(UpdateDislikedFoodEvent(food));
          },
        );
        
      case 'spice_level':
        return SpiceLevelScreen(
          spiceLevel: data.spiceLevel,
          onSpiceLevelChanged: (level) {
            context.read<OnboardingBloc>().add(UpdateSpiceLevelEvent(level));
          },
        );

      case 'texture_preferences':
        return TexturePreferencesScreen(
          selectedTextures: data.texturePreferences,
          onTexturesChanged: (textures) {
            context.read<OnboardingBloc>().add(UpdateTexturePreferencesEvent(textures));
          },
        );
        
      case 'cuisine_preferences':
        return CuisinePreferencesScreen(
          selectedCuisines: data.cuisinePreferences,
          onCuisinesChanged: (cuisines) {
            context.read<OnboardingBloc>().add(UpdateCuisinePreferencesEvent(cuisines));
          },
        );
        
      case 'dietary_lifestyle':
        return DietaryLifestyleScreen(
          selectedLifestyles: data.dietaryLifestyles,
          onLifestylesChanged: (lifestyles) {
            context.read<OnboardingBloc>().add(UpdateDietaryLifestyleEvent(lifestyles));
          },
        );
        
      case 'cooking_skill':
        return CookingSkillScreen(
          selectedSkill: data.cookingSkill,
          onSkillSelected: (skill) {
            context.read<OnboardingBloc>().add(UpdateCookingSkillTimeEvent(cookingSkill: skill));
          },
        );

      case 'cooking_time':
        return CookingTimeScreen(
          selectedTime: data.cookingTime,
          onTimeSelected: (time) {
            context.read<OnboardingBloc>().add(UpdateCookingSkillTimeEvent(cookingTime: time));
          },
        );
        
      case 'kitchen_equipment':
        return KitchenEquipmentScreen(
          selectedEquipment: data.kitchenEquipment,
          onEquipmentChanged: (equipment) {
            context.read<OnboardingBloc>().add(UpdateKitchenEquipmentEvent(equipment));
          },
        );
        
      case 'meal_suggestions':
        return MealSuggestionsScreen(
          selectedSuggestions: data.mealSuggestions,
          onSuggestionsChanged: (suggestions) {
            context.read<OnboardingBloc>().add(UpdateMealSuggestionsEvent(suggestions));
          },
        );
        
      default:
        return Center(
          child: Text(
            'Unknown step: $stepId',
            style: context.bodyLarge,
          ),
        );
    }
  }

  Widget _buildCompletionScreen(BuildContext context, OnboardingCompleted state) {
    return Scaffold(
      body: Center(
        child: ResponsiveContainer(
          padding: context.pagePadding,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.check_circle,
                size: 80,
                color: const Color(OnboardingConstants.completedColor),
              ),
              VSpace.xl(),
              Text(
                'Hoàn thành!',
                style: context.headlineLarge.copyWith(
                  fontWeight: FontWeight.w700,
                  color: const Color(OnboardingConstants.completedColor),
                ),
                textAlign: TextAlign.center,
              ),
              VSpace.md(),
              Text(
                'Cảm ơn bạn đã hoàn thành quá trình thiết lập. Chúng tôi sẽ cá nhân hóa trải nghiệm dựa trên thông tin bạn đã cung cấp.',
                style: context.bodyLarge.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
              VSpace.xl(),
              ElevatedButton(
                onPressed: () => _handleOnboardingCompleted(context, state.onboardingData),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(OnboardingConstants.currentColor),
                  foregroundColor: Colors.black,
                  padding: EdgeInsets.symmetric(
                    horizontal: context.spacingXXL,
                    vertical: context.spacingLG,
                  ),
                ),
                child: Text(
                  'Bắt đầu sử dụng',
                  style: context.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorScreen(BuildContext context, OnboardingError state) {
    return Scaffold(
      body: Center(
        child: ResponsiveContainer(
          padding: context.pagePadding,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 80,
                color: Theme.of(context).colorScheme.error,
              ),
              VSpace.xl(),
              Text(
                'Đã xảy ra lỗi',
                style: context.headlineMedium.copyWith(
                  fontWeight: FontWeight.w700,
                  color: Theme.of(context).colorScheme.error,
                ),
                textAlign: TextAlign.center,
              ),
              VSpace.md(),
              Text(
                state.message,
                style: context.bodyLarge,
                textAlign: TextAlign.center,
              ),
              VSpace.xl(),
              ElevatedButton(
                onPressed: () {
                  context.read<OnboardingBloc>().add(const StartOnboardingEvent());
                },
                child: const Text('Thử lại'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAwaitingAuthScreen(BuildContext context, OnboardingAwaitingAuth state) {
    // Show auth bottom sheet automatically
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showAuthBottomSheet(context, state.onboardingData);
    });

    return Scaffold(
      body: Center(
        child: ResponsiveContainer(
          padding: context.pagePadding,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.check_circle_outline,
                size: 80,
                color: const Color(OnboardingConstants.completedColor),
              ),
              VSpace.xl(),
              Text(
                'Hoàn thành thiết lập!',
                style: context.headlineLarge.copyWith(
                  fontWeight: FontWeight.w700,
                  color: const Color(OnboardingConstants.completedColor),
                ),
                textAlign: TextAlign.center,
              ),
              VSpace.md(),
              Text(
                'Hãy đăng nhập để lưu thông tin và bắt đầu sử dụng BanaChef.',
                style: context.bodyLarge.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAuthenticatingScreen(BuildContext context, OnboardingAuthenticating state) {
    return Scaffold(
      body: Center(
        child: ResponsiveContainer(
          padding: context.pagePadding,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              VSpace.xl(),
              Text(
                'Đang đăng nhập...',
                style: context.headlineMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              VSpace.md(),
              Text(
                'Vui lòng chờ trong giây lát.',
                style: context.bodyLarge.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAuthErrorScreen(BuildContext context, OnboardingAuthError state) {
    return Scaffold(
      body: Center(
        child: ResponsiveContainer(
          padding: context.pagePadding,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 80,
                color: Theme.of(context).colorScheme.error,
              ),
              VSpace.xl(),
              Text(
                'Đăng nhập thất bại',
                style: context.headlineMedium.copyWith(
                  fontWeight: FontWeight.w700,
                  color: Theme.of(context).colorScheme.error,
                ),
                textAlign: TextAlign.center,
              ),
              VSpace.md(),
              Text(
                state.error,
                style: context.bodyLarge,
                textAlign: TextAlign.center,
              ),
              VSpace.xl(),
              ElevatedButton(
                onPressed: () {
                  context.read<OnboardingBloc>().add(const ShowAuthBottomSheetEvent());
                },
                child: const Text('Thử lại'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAuthBottomSheet(BuildContext context, OnboardingData data) {
    AuthBottomSheet.show(
      context,
      onAuthSuccess: () {
      context.router.replacePath('/referral');
      },
      onTermsPressed: () {
        // TODO: Navigate to terms of service
        debugPrint('Terms of service pressed');
      },
      onPrivacyPressed: () {
        // TODO: Navigate to privacy policy
        debugPrint('Privacy policy pressed');
      },
    );
  }

  void _handleOnboardingCompleted(BuildContext context, OnboardingData data) {
    // Navigate to referral code screen after successful authentication
    debugPrint('Onboarding completed with data: $data');

    // Navigate to referral code screen
    context.router.replacePath('/referral');
  }

  @override
  OnboardingBloc createBloc(BuildContext context) {
    return OnboardingBloc();
  }
}

/// Loading screen widget
class _LoadingScreen extends StatelessWidget {
  const _LoadingScreen();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            VSpace.lg(),
            Text(
              'Đang tải...',
              style: context.bodyLarge,
            ),
          ],
        ),
      ),
    );
  }
}
