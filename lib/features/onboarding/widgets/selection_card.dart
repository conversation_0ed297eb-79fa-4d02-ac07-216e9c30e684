import 'package:flutter/material.dart';
import '../../../ui/responsive/responsive.dart';
import '../../../core/theme/app_colors.dart';
import '../models/onboarding_constants.dart';

/// Reusable selection card widget for onboarding
/// Used for gender selection, pregnancy status, etc.
class SelectionCard extends StatelessWidget {
  /// The text to display
  final String text;
  
  /// The icon to display (emoji or IconData)
  final dynamic icon;
  
  /// Whether this card is selected
  final bool isSelected;
  
  /// Callback when card is tapped
  final VoidCallback? onTap;
  
  /// Custom width for the card
  final double? width;
  
  /// Custom height for the card
  final double? height;

  const SelectionCard({
    super.key,
    required this.text,
    this.icon,
    required this.isSelected,
    this.onTap,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Responsive dimensions
    final cardWidth = width ?? context.responsive(
      ResponsiveValue(mobile: 100.0, tablet: 120.0, desktop: 140.0),
    );
    
    final cardHeight = height ?? context.responsive(
      ResponsiveValue(mobile: 100.0, tablet: 120.0, desktop: 140.0),
    );
    
    final iconSize = context.responsive(
      ResponsiveValue(mobile: 32.0, tablet: 40.0, desktop: 48.0),
    );

    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: cardWidth,
        height: cardHeight,
        decoration: BoxDecoration(
          // Thêm background highlight cho selected state
          color: isSelected
              ? AppColors.selectionBackground
              : theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(context.responsive(
            ResponsiveValue(mobile: 12.0, tablet: 16.0, desktop: 20.0),
          )),
          border: Border.all(
            color: isSelected
                ? const Color(OnboardingConstants.completedColor)
                : theme.colorScheme.outline.withValues(alpha: 0.3),
            width: isSelected ? 2.5 : 1.0,
          ),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon
            if (icon != null) ...[
              if (icon is String)
                Text(
                  icon as String,
                  style: TextStyle(fontSize: iconSize),
                )
              else if (icon is IconData)
                Icon(
                  icon as IconData,
                  size: iconSize,
                  color: isSelected
                      ? const Color(OnboardingConstants.completedColor)
                      : theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              VSpace.sm(),
            ],
            
            // Text
            Text(
              text,
              style: context.bodyMedium.copyWith(
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected 
                    ? const Color(OnboardingConstants.completedColor)
                    : theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}

/// Grid of selection cards
class SelectionCardGrid extends StatelessWidget {
  /// List of options to display
  final List<SelectionCardOption> options;
  
  /// Currently selected option
  final String? selectedValue;
  
  /// Callback when an option is selected
  final void Function(String value)? onSelectionChanged;
  
  /// Number of columns in the grid
  final int? columns;
  
  /// Whether to allow multiple selections
  final bool multiSelect;
  
  /// Currently selected values (for multi-select)
  final List<String>? selectedValues;
  
  /// Callback for multi-select
  final void Function(List<String> values)? onMultiSelectionChanged;

  const SelectionCardGrid({
    super.key,
    required this.options,
    this.selectedValue,
    this.onSelectionChanged,
    this.columns,
    this.multiSelect = false,
    this.selectedValues,
    this.onMultiSelectionChanged,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveColumns = columns ?? context.responsive(
      ResponsiveValue(mobile: 2, tablet: 3, desktop: 4),
    );

    return ResponsiveGrid(
      mobileColumns: effectiveColumns,
      tabletColumns: effectiveColumns,
      desktopColumns: effectiveColumns,
      spacing: context.spacingMD,
      runSpacing: context.spacingMD,
      children: options.map((option) {
        final isSelected = multiSelect
            ? selectedValues?.contains(option.value) ?? false
            : selectedValue == option.value;

        return SelectionCard(
          text: option.text,
          icon: option.icon,
          isSelected: isSelected,
          onTap: () {
            if (multiSelect) {
              final currentValues = List<String>.from(selectedValues ?? []);
              if (isSelected) {
                currentValues.remove(option.value);
              } else {
                currentValues.add(option.value);
              }
              onMultiSelectionChanged?.call(currentValues);
            } else {
              onSelectionChanged?.call(option.value);
            }
          },
        );
      }).toList(),
    );
  }
}

/// Model for selection card option
class SelectionCardOption {
  final String value;
  final String text;
  final dynamic icon;

  const SelectionCardOption({
    required this.value,
    required this.text,
    this.icon,
  });
}
