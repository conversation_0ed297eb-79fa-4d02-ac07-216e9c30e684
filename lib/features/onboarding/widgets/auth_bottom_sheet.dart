import 'dart:io';
import 'package:flutter/material.dart';
import '../../../ui/responsive/responsive.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/bloc/bloc_exports.dart';
import '../../../core/di/injection_container.dart';
import '../../auth/cubit/auth_cubit.dart';
import '../../auth/cubit/auth_state.dart';

/// Bottom sheet for authentication with SNS login
class AuthBottomSheet extends StatelessWidget {
  final VoidCallback? onAuthSuccess;
  final VoidCallback? onTermsPressed;
  final VoidCallback? onPrivacyPressed;

  const AuthBottomSheet({
    super.key,
    this.onAuthSuccess,
    this.onTermsPressed,
    this.onPrivacyPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isIOS = Platform.isIOS;

    return BlocProvider(
      create: (context) => getIt<AuthCubit>(),
      child: BlocListener<AuthCubit, AuthState>(
        listener: (context, state) {
          if (state is AuthSuccess || state is AuthAuthenticated) {
            // Close bottom sheet and call success callback
            Navigator.of(context).pop();
            onAuthSuccess?.call();
          } else if (state is AuthError) {
            // Show error message
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              _buildHandle(context),

              // Content
              Padding(
                padding: EdgeInsets.fromLTRB(
                  context.spacingXL,
                  context.spacingMD,
                  context.spacingXL,
                  context.spacingXL,
                ),
                child: Column(
                  children: [
                    // Title
                    _buildTitle(context),

                    VSpace.xl(),

                    // Action buttons
                    _buildActionButtons(context, isIOS),

                    VSpace.xl(),

                    // Legal text
                    _buildLegalText(context),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHandle(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: context.spacingSM),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Text(
      'Chào mừng đến với BanaChef!',
      style: const TextStyle(
        fontFamily: 'Nunito',
        fontSize: 24,
        fontWeight: FontWeight.bold,
      ).copyWith(
        color: Theme.of(context).colorScheme.onSurface,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildActionButtons(BuildContext context, bool isIOS) {
    return Column(
      children: [
        if (isIOS) ...[
          // Apple Sign In (Primary on iOS)
          _buildAppleSignInButton(context),
          VSpace.md(),
          // Google Sign In (Secondary on iOS)
          _buildGoogleSignInButton(context, isPrimary: false),
        ] else ...[
          // Google Sign In (Only option on Android)
          _buildGoogleSignInButton(context, isPrimary: true),
        ],
      ],
    );
  }

  Widget _buildAppleSignInButton(BuildContext context) {
    return BlocBuilder<AuthCubit, AuthState>(
      builder: (context, state) {
        final isLoading = state is AppleSignInLoading;

        return SizedBox(
          width: double.infinity,
          height: context.responsive(
            ResponsiveValue(mobile: 52.0, tablet: 56.0, desktop: 60.0),
          ),
          child: ElevatedButton.icon(
            onPressed: isLoading
              ? null
              : () => context.read<AuthCubit>().signInWithApple(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            icon: isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Icon(Icons.apple, size: 20),
            label: Text(
              isLoading ? 'Đang đăng nhập...' : 'Đăng nhập bằng Apple',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildGoogleSignInButton(BuildContext context, {required bool isPrimary}) {
    return BlocBuilder<AuthCubit, AuthState>(
      builder: (context, state) {
        final isLoading = state is GoogleSignInLoading;

        return SizedBox(
          width: double.infinity,
          height: context.responsive(
            ResponsiveValue(mobile: 52.0, tablet: 56.0, desktop: 60.0),
          ),
          child: ElevatedButton.icon(
            onPressed: isLoading
              ? null
              : () => context.read<AuthCubit>().signInWithGoogle(),
            style: ElevatedButton.styleFrom(
              backgroundColor: isPrimary
                ? Colors.white
                : AppColors.lightSurface,
              foregroundColor: AppColors.lightText,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              elevation: isPrimary ? 2 : 0,
              shadowColor: isPrimary ? Colors.black.withValues(alpha: 0.1) : null,
            ),
            icon: isLoading
              ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.lightText),
                  ),
                )
              : _buildGoogleIcon(),
            label: Text(
              isLoading ? 'Đang đăng nhập...' : 'Đăng nhập bằng Google',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        );
      },
    );
  }



  Widget _buildGoogleIcon() {
    // Simplified Google "G" icon using Container with gradient
    return Container(
      width: 20,
      height: 20,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(2),
        gradient: const LinearGradient(
          colors: [
            Color(0xFF4285F4), // Google Blue
            Color(0xFF34A853), // Google Green
            Color(0xFFFBBC05), // Google Yellow
            Color(0xFFEA4335), // Google Red
          ],
          stops: [0.0, 0.33, 0.66, 1.0],
        ),
      ),
      child: const Center(
        child: Text(
          'G',
          style: TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildLegalText(BuildContext context) {
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        style: const TextStyle(
          fontFamily: 'Inter',
          fontSize: 12,
        ).copyWith(
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
        ),
        children: [
          const TextSpan(text: 'Bằng việc tiếp tục, bạn đồng ý với '),
          TextSpan(
            text: 'Điều khoản Dịch vụ',
            style: TextStyle(
              color: AppColors.primary,
              decoration: TextDecoration.underline,
            ),
            // Note: Add GestureRecognizer for tap handling in real implementation
          ),
          const TextSpan(text: ' và '),
          TextSpan(
            text: 'Chính sách Bảo mật',
            style: TextStyle(
              color: AppColors.primary,
              decoration: TextDecoration.underline,
            ),
            // Note: Add GestureRecognizer for tap handling in real implementation
          ),
          const TextSpan(text: ' của BanaChef.'),
        ],
      ),
    );
  }

  /// Show the auth bottom sheet
  static Future<T?> show<T>(
    BuildContext context, {
    VoidCallback? onAuthSuccess,
    VoidCallback? onTermsPressed,
    VoidCallback? onPrivacyPressed,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AuthBottomSheet(
        onAuthSuccess: onAuthSuccess,
        onTermsPressed: onTermsPressed,
        onPrivacyPressed: onPrivacyPressed,
      ),
    );
  }
}
