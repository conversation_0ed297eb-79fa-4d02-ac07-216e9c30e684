import '../../../ui/widgets/progress/progress_exports.dart';
import '../models/onboarding_constants.dart';

/// Custom segmented progress bar for onboarding with specific colors
class OnboardingProgressBar extends StatelessWidget {
  /// Progress for each segment (0.0 to 1.0)
  final List<double> segmentProgresses;
  
  /// Current active segment index
  final int currentSegment;
  
  /// Height of the progress bar
  final double? height;

  const OnboardingProgressBar({
    super.key,
    required this.segmentProgresses,
    required this.currentSegment,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    // Create segments with onboarding-specific colors
    final segments = segmentProgresses.asMap().entries.map((entry) {
      final index = entry.key;
      final progress = entry.value;
      
      Color segmentColor;
      if (index < currentSegment) {
        // Completed segments - Green
        segmentColor = const Color(OnboardingConstants.completedColor);
      } else if (index == currentSegment) {
        // Current segment - Yellow
        segmentColor = const Color(OnboardingConstants.currentColor);
      } else {
        // Pending segments - Gray
        segmentColor = const Color(OnboardingConstants.pendingColor);
      }
      
      return ProgressSegment.withProgress(
        id: 'segment_$index',
        progress: progress,
        color: segmentColor,
        label: OnboardingConstants.segmentNames[index],
      );
    }).toList();

    return SegmentedProgressBar(
      segments: segments,
      height: height ?? context.responsive(
        ResponsiveValue(mobile: 6.0, tablet: 8.0, desktop: 10.0),
      ),
      borderRadius: context.responsive(
        ResponsiveValue(mobile: 3.0, tablet: 4.0, desktop: 5.0),
      ),
      segmentSpacing: context.responsive(
        ResponsiveValue(mobile: 4.0, tablet: 6.0, desktop: 8.0),
      ),
      backgroundColor: const Color(0xFFF5F5F5),
      animated: true,
      animationDuration: const Duration(milliseconds: 400),
      animationCurve: Curves.easeInOut,
    );
  }
}
