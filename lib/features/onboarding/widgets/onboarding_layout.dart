import 'package:flutter/material.dart';
import '../../../ui/responsive/responsive.dart';
import '../../../core/theme/app_colors.dart';
import '../models/onboarding_constants.dart';
import 'onboarding_progress_bar.dart';

/// Base layout for onboarding screens
class OnboardingLayout extends StatelessWidget {
  /// Progress for each segment
  final List<double> segmentProgresses;

  /// Current segment index
  final int currentSegment;

  /// Title/question for the current screen
  final String title;

  /// Optional subtitle or description
  final String? subtitle;

  /// Description text below title
  final String? description;

  /// Main content widget
  final Widget child;

  /// Whether to show skip button
  final bool showSkip;

  /// Skip button callback
  final VoidCallback? onSkip;

  /// Whether next button is enabled
  final bool canGoNext;

  /// Next button callback
  final VoidCallback? onNext;

  /// Whether previous button is enabled
  final bool canGoPrevious;

  /// Previous button callback
  final VoidCallback? onPrevious;

  /// Custom next button text
  final String? nextButtonText;

  const OnboardingLayout({
    super.key,
    required this.segmentProgresses,
    required this.currentSegment,
    required this.title,
    this.subtitle,
    this.description,
    required this.child,
    this.showSkip = true,
    this.onSkip,
    this.canGoNext = false,
    this.onNext,
    this.canGoPrevious = false,
    this.onPrevious,
    this.nextButtonText,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        // Gradient background cho onboarding
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.onboardingGradientStart, // Xanh lá rất nhạt
              AppColors.lightBackground,         // Trắng kem
            ],
            stops: [0.0, 0.3], // Gradient chỉ ở 30% trên cùng
          ),
        ),
        child: Column(
          children: [
            // App Bar
            _buildAppBar(context),

            // Main content
            Expanded(
              child: ResponsiveContainer(
                padding: context.pagePadding,
                child: Column(
                  children: [
                    // Title, description and skip button
                    _buildHeader(context),

                    VSpace.xl(),

                    // Main content
                    Expanded(
                      child: child,
                    ),
                  ],
                ),
              ),
            ),

            // Bottom navigation
            _buildBottomNavigation(context),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      surfaceTintColor: Colors.transparent,
      leading: canGoPrevious
          ? Padding(
              padding: EdgeInsets.only(left: context.spacingMD),
              child: IconButton(
                onPressed: onPrevious,
                icon: const Icon(Icons.arrow_back_ios),
                style: IconButton.styleFrom(
                  backgroundColor: Colors.white.withValues(alpha: 0.9), // Đơn giản hóa
                  foregroundColor: Theme.of(context).colorScheme.onSurface,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: EdgeInsets.zero,
                ),
              ),
            )
          : null,
      title: Container(
        constraints: BoxConstraints(
          maxWidth: context.responsive(
            ResponsiveValue(mobile: 200.0, tablet: 300.0, desktop: 400.0),
          ),
        ),
        child: OnboardingProgressBar(
          segmentProgresses: segmentProgresses,
          currentSegment: currentSegment,
          height: context.responsive(
            ResponsiveValue(mobile: 6.0, tablet: 8.0, desktop: 10.0),
          ),
        ),
      ),
      centerTitle: true,
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: context.spacingMD),
      child: Column(
        children: [
          // Title with skip button
          Stack(
            children: [
              // Centered title
              Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: showSkip ? context.spacingXXL : 0,
                  ),
                  child: Text(
                    title,
                    style: context.headlineMedium.copyWith(
                      fontWeight: FontWeight.w700,
                      color: Theme.of(context).colorScheme.onSurface,
                      height: 1.2,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              // Skip button positioned absolutely
              if (showSkip)
                Positioned(
                  top: 0,
                  right: 0,
                  child: TextButton(
                    onPressed: onSkip,
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(
                        horizontal: context.spacingSM,
                        vertical: context.spacingXS,
                      ),
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: Text(
                      'Bỏ qua',
                      style: context.bodyMedium.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
            ],
          ),

          // Description
          if (description != null) ...[
            VSpace.md(),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: context.spacingMD),
              child: Text(
                description!,
                style: context.bodyLarge.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],

          // Subtitle (legacy support)
          if (subtitle != null) ...[
            VSpace.sm(),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: context.spacingMD),
              child: Text(
                subtitle!,
                style: context.bodyLarge.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBottomNavigation(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(
        context.spacingLG,
        context.spacingMD,
        context.spacingLG,
        context.spacingLG,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: SizedBox(
          width: double.infinity,
          height: context.responsive(
            ResponsiveValue(mobile: 52.0, tablet: 56.0, desktop: 60.0),
          ),
          child: ElevatedButton(
            onPressed: canGoNext ? onNext : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(OnboardingConstants.currentColor),
              foregroundColor: Colors.black,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(
                  context.responsive(
                    ResponsiveValue(mobile: 16.0, tablet: 18.0, desktop: 20.0),
                  ),
                ),
              ),
              elevation: 0,
              shadowColor: Colors.transparent,
              disabledBackgroundColor: AppColors.disabled, // Đơn giản hóa
              disabledForegroundColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            child: Text(
              nextButtonText ?? 'Tiếp theo',
              style: context.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: context.responsive(
                  ResponsiveValue(mobile: 16.0, tablet: 17.0, desktop: 18.0),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
