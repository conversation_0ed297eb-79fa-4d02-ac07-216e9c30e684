import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../ui/responsive/responsive.dart';
import 'responsive_wheel_picker.dart';

/// Date wheel picker component for selecting birth date
class DateWheelPicker extends StatelessWidget {
  final DateTime? selectedDate;
  final void Function(DateTime date) onDateSelected;
  final bool enableHapticFeedback;

  const DateWheelPicker({
    super.key,
    this.selectedDate,
    required this.onDateSelected,
    this.enableHapticFeedback = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentDate = selectedDate ?? DateTime(1990, 1, 1);
    
    // Calculate ranges
    final currentYear = DateTime.now().year;
    final minYear = 1920;
    final maxYear = currentYear;
    
    final selectedDay = currentDate.day;
    final selectedMonth = currentDate.month;
    final selectedYear = currentDate.year;
    
    // Get days in selected month/year
    final daysInMonth = DateTime(selectedYear, selectedMonth + 1, 0).day;

    return Column(
      children: [

        Container(
          padding: EdgeInsets.symmetric(
            horizontal: context.spacingXL,
            vertical: context.spacingLG,
          ),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Text(
            _formatDate(context, currentDate),
            style: context.headlineMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.primary,
            ),
          ),
        ),
        
        VSpace.xl(),
        
        // Date pickers row
        Expanded(
          child: Row(
            children: [
              // Day picker
              Expanded(
                child: _buildDayPicker(
                  context,
                  selectedDay,
                  daysInMonth,
                  (day) => _updateDate(currentDate, day: day),
                ),
              ),
              
              HSpace.md(),
              
              // Month picker
              Expanded(
                child: _buildMonthPicker(
                  context,
                  selectedMonth,
                  (month) => _updateDate(currentDate, month: month),
                ),
              ),
              
              HSpace.md(),
              
              // Year picker
              Expanded(
                child: _buildYearPicker(
                  context,
                  selectedYear,
                  minYear,
                  maxYear,
                  (year) => _updateDate(currentDate, year: year),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDayPicker(
    BuildContext context,
    int selectedDay,
    int daysInMonth,
    void Function(int) onDayChanged,
  ) {
    return ResponsiveWheelPicker(
      itemCount: daysInMonth,
      selectedIndex: selectedDay - 1,
      builder: (context, index) {
        final day = index + 1;
        return Text(
          day.toString().padLeft(2, '0'),
          style: context.headlineSmall.copyWith(
            fontWeight: FontWeight.w600,
          ),
        );
      },
      onIndexChanged: (index) {
        onDayChanged(index + 1);
      },
      enableHapticFeedback: enableHapticFeedback,
      height: context.responsive(
        ResponsiveValue(mobile: 180.0, tablet: 200.0, desktop: 220.0),
      ),
    );
  }

  Widget _buildMonthPicker(
    BuildContext context,
    int selectedMonth,
    void Function(int) onMonthChanged,
  ) {
    return ResponsiveWheelPicker(
      itemCount: 12,
      selectedIndex: selectedMonth - 1,
      builder: (context, index) {
        final monthName = _getLocalizedMonthName(context, index + 1);
        return Text(
          monthName,
          style: context.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        );
      },
      onIndexChanged: (index) {
        onMonthChanged(index + 1);
      },
      enableHapticFeedback: enableHapticFeedback,
      height: context.responsive(
        ResponsiveValue(mobile: 180.0, tablet: 200.0, desktop: 220.0),
      ),
    );
  }

  Widget _buildYearPicker(
    BuildContext context,
    int selectedYear,
    int minYear,
    int maxYear,
    void Function(int) onYearChanged,
  ) {
    final yearCount = maxYear - minYear + 1;
    final selectedIndex = selectedYear - minYear;

    return ResponsiveWheelPicker(
      itemCount: yearCount,
      selectedIndex: selectedIndex,
      builder: (context, index) {
        final year = minYear + index;
        return Text(
          year.toString(),
          style: context.headlineSmall.copyWith(
            fontWeight: FontWeight.w600,
          ),
        );
      },
      onIndexChanged: (index) {
        onYearChanged(minYear + index);
      },
      enableHapticFeedback: enableHapticFeedback,
      height: context.responsive(
        ResponsiveValue(mobile: 180.0, tablet: 200.0, desktop: 220.0),
      ),
    );
  }

  void _updateDate(DateTime currentDate, {int? day, int? month, int? year}) {
    final newYear = year ?? currentDate.year;
    final newMonth = month ?? currentDate.month;
    var newDay = day ?? currentDate.day;
    
    // Ensure day is valid for the new month/year
    final daysInNewMonth = DateTime(newYear, newMonth + 1, 0).day;
    if (newDay > daysInNewMonth) {
      newDay = daysInNewMonth;
    }
    
    final newDate = DateTime(newYear, newMonth, newDay);
    onDateSelected(newDate);
  }

  String _formatDate(BuildContext context, DateTime date) {
    // Try to use system locale first, fallback to Vietnamese
    try {
      final locale = Localizations.localeOf(context);
      final formatter = DateFormat.yMMMd(locale.toString());
      return formatter.format(date);
    } catch (e) {
      // Fallback to manual formatting
      final monthName = _getLocalizedMonthName(context, date.month);
      return '${date.day.toString().padLeft(2, '0')} $monthName ${date.year}';
    }
  }

  String _getLocalizedMonthName(BuildContext context, int month) {
    // Try to get localized month name
    try {
      final locale = Localizations.localeOf(context);
      final date = DateTime(2024, month, 1);
      final formatter = DateFormat.MMM(locale.toString());
      return formatter.format(date);
    } catch (e) {
      // Fallback to Vietnamese month names
      const vietnameseMonths = [
        'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4',
        'Tháng 5', 'Tháng 6', 'Tháng 7', 'Tháng 8',
        'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12',
      ];
      return vietnameseMonths[month - 1];
    }
  }
}
