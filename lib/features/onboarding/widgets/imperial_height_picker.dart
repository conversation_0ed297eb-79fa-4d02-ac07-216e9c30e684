import 'package:flutter/material.dart';
import '../../../ui/responsive/responsive.dart';
import '../models/measurement_unit.dart';
import 'responsive_wheel_picker.dart';

/// Imperial height picker with separate feet and inches wheels
class ImperialHeightPicker extends StatelessWidget {
  final double heightInCm;
  final void Function(double heightInCm) onHeightChanged;
  final String? title;
  final bool enableHapticFeedback;

  const ImperialHeightPicker({
    super.key,
    required this.heightInCm,
    required this.onHeightChanged,
    this.title,
    this.enableHapticFeedback = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final feet = UnitConverter.cmToFeetPart(heightInCm);
    final inches = UnitConverter.cmToInchesPart(heightInCm);

    return Column(
      children: [
        if (title != null) ...[
          Text(
            title!,
            style: context.titleLarge.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          VSpace.lg(),
        ],
        // Feet and inches pickers
        Expanded(
          child: Row(
            children: [
              // Feet picker
              Expanded(
                child: ResponsiveWheelPicker(
                  itemCount: 4, // 4'6" to 7'6" (4, 5, 6, 7 feet)
                  selectedIndex: feet - 4, // Start from 4 feet
                  builder: (context, index) {
                    final feetValue = index + 4;
                    return Text(
                      '$feetValue\'',
                      style: context.bodyLarge.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    );
                  },
                  onIndexChanged: (index) {
                    final newFeet = index + 4;
                    final newHeightCm = UnitConverter.feetInchesToCm(newFeet, inches);
                    onHeightChanged(newHeightCm);
                  },
                  enableHapticFeedback: enableHapticFeedback,
                  height: context.responsive(
                    ResponsiveValue(mobile: 160.0, tablet: 180.0, desktop: 200.0),
                  ),
                ),
              ),

              HSpace.xs(),

              // Inches picker
              Expanded(
                child: ResponsiveWheelPicker(
                  itemCount: 12, // 0-11 inches
                  selectedIndex: inches,
                  builder: (context, index) {
                    return Text(
                      '$index"',
                      style: context.bodyLarge.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    );
                  },
                  onIndexChanged: (index) {
                    final newHeightCm = UnitConverter.feetInchesToCm(feet, index);
                    onHeightChanged(newHeightCm);
                  },
                  enableHapticFeedback: enableHapticFeedback,
                  height: context.responsive(
                    ResponsiveValue(mobile: 160.0, tablet: 180.0, desktop: 200.0),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
