import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:wheel_picker/wheel_picker.dart';
import '../../../ui/responsive/responsive.dart';
import '../models/onboarding_constants.dart';

/// Responsive wheel picker widget that follows the design system
class ResponsiveWheelPicker extends StatelessWidget {
  final int itemCount;
  final Widget Function(BuildContext context, int index) builder;
  final int selectedIndex;
  final void Function(int index) onIndexChanged;
  final String? title;
  final String? unit;
  final bool enableHapticFeedback;
  final double? height;

  const ResponsiveWheelPicker({
    super.key,
    required this.itemCount,
    required this.builder,
    required this.selectedIndex,
    required this.onIndexChanged,
    this.title,
    this.unit,
    this.enableHapticFeedback = true,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        if (title != null) ...[
          // Title
          Text(
            title!,
            style: context.titleLarge.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          VSpace.lg(),
        ],
        
        // Wheel picker container
        Container(
          height: height ?? context.responsive(
            ResponsiveValue(mobile: 200.0, tablet: 240.0, desktop: 280.0),
          ),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(16),
          ),
          child: WheelPicker(
            itemCount: itemCount,
            builder: (context, index) {
              return Center(
                child: _buildPickerItem(context, index),
              );
            },
            initialIndex: selectedIndex,
            onIndexChanged: (index, interactionType) {
              if (enableHapticFeedback) {
                HapticFeedback.selectionClick();
              }
              onIndexChanged(index);
            },
            style: WheelPickerStyle(
              itemExtent: context.responsive(
                ResponsiveValue(mobile: 50.0, tablet: 55.0, desktop: 60.0),
              ),
              squeeze: 1.1,
              diameterRatio: 1.5,
              surroundingOpacity: 0.4,
              magnification: 1.15,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPickerItem(BuildContext context, int index) {
    final isSelected = index == selectedIndex;

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: context.spacingMD,
        vertical: context.spacingSM,
      ),
      decoration: BoxDecoration(
        color: isSelected
          ? const Color(OnboardingConstants.currentColor).withValues(alpha: 0.1)
          : Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        border: isSelected
          ? Border.all(
              color: const Color(OnboardingConstants.currentColor),
              width: 2,
            )
          : null,
      ),
      child: builder(context, index),
    );
  }
}

/// Specialized wheel picker for numeric values
class NumericWheelPicker extends StatelessWidget {
  final double minValue;
  final double maxValue;
  final double step;
  final double selectedValue;
  final void Function(double value) onValueChanged;
  final String? title;
  final String? unit;
  final bool enableHapticFeedback;
  final double? height;

  const NumericWheelPicker({
    super.key,
    required this.minValue,
    required this.maxValue,
    required this.step,
    required this.selectedValue,
    required this.onValueChanged,
    this.title,
    this.unit,
    this.enableHapticFeedback = true,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final itemCount = ((maxValue - minValue) / step).round() + 1;
    final selectedIndex = ((selectedValue - minValue) / step).round();

    return ResponsiveWheelPicker(
      itemCount: itemCount,
      selectedIndex: selectedIndex,
      title: title,
      enableHapticFeedback: enableHapticFeedback,
      height: height,
      builder: (context, index) {
        final value = minValue + (index * step);
        String displayValue;

        if (step == 1.0) {
          displayValue = value.round().toString();
        } else if (step >= 0.1) {
          displayValue = value.toStringAsFixed(1);
        } else {
          displayValue = value.toStringAsFixed(2);
        }

        return Text(
          '$displayValue${unit != null ? ' $unit' : ''}',
          style: context.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
            color: index == selectedIndex
              ? const Color(OnboardingConstants.currentColor)
              : theme.colorScheme.onSurface.withValues(alpha: 0.6), // Giảm opacity xuống 60%
          ),
        );
      },
      onIndexChanged: (index) {
        final value = minValue + (index * step);
        onValueChanged(value);
      },
    );
  }
}
