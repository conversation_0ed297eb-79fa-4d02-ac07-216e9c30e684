/// Constants for onboarding flow
class OnboardingConstants {
  OnboardingConstants._();

  /// Onboarding colors
  static const int completedColor = 0xFF6ECB63; // Xanh Lá Tươi
  static const int currentColor = 0xFFFFD700;   // Vàng Chuối Chín
  static const int pendingColor = 0xFFE0E0E0;   // Xám nhạt

  /// Common allergies data
  static const List<String> commonAllergies = [
    'Không có',
    '<PERSON><PERSON><PERSON> sản',
    '<PERSON><PERSON>u phộng',
    '<PERSON>ữa',
    'Trứng',
    'Gluten',
    '<PERSON><PERSON>u nành',
    '<PERSON>ạt cây',
    '<PERSON><PERSON>',
    'Tôm cua',
    '<PERSON><PERSON>',
    '<PERSON>wi',
    '<PERSON><PERSON> chua',
  ];

  /// Common medical conditions
  static const List<String> commonMedicalConditions = [
    'Không có',
    'Tiểu đường',
    '<PERSON> huyết áp',
    '<PERSON> mạch',
    '<PERSON><PERSON> dày',
    '<PERSON><PERSON>',
    '<PERSON>h<PERSON><PERSON>',
    'Gout',
    'Cholesterol cao',
    '<PERSON><PERSON><PERSON> phì',
    '<PERSON><PERSON> dinh dưỡng',
    '<PERSON><PERSON><PERSON> loạn tiêu hóa',
  ];

  /// Common favorite foods
  static const List<String> commonFavoriteFood = [
    'Thịt bò',
    'Thịt heo',
    'Thịt gà',
    'Cá',
    'Tôm',
    'Cua',
    'Rau xanh',
    'Trái cây',
    'Gạo',
    'Mì',
    'Phở',
    'Bún',
    'Bánh mì',
    'Chả cá',
    'Nem',
    'Bánh cuốn',
    'Cơm tấm',
    'Bún bò Huế',
  ];

  /// Common disliked foods
  static const List<String> commonDislikedFood = [
    'Đồ chua',
    'Đồ cay',
    'Nội tạng',
    'Mắm tôm',
    'Tỏi',
    'Hành',
    'Rau mùi',
    'Đồ chiên',
    'Đồ ngọt',
    'Đồ mặn',
    'Cà chua',
    'Ớt',
    'Gừng',
    'Sả',
  ];

  /// Spice level names (0-5)
  static const List<String> spiceLevelNames = [
    'Không cay',      // 0
    'Rất nhẹ',        // 1
    'Nhẹ',            // 2
    'Vừa',            // 3
    'Cay',            // 4
    'Rất cay',        // 5
  ];

  /// Texture preferences
  static const List<String> texturePreferences = [
    'Không quan trọng',
    'Giòn',
    'Mềm',
    'Dai',
    'Mịn',
    'Thô',
    'Sệt',
    'Lỏng',
    'Khô',
    'Ướt',
    'Nóng',
    'Lạnh',
  ];

  /// Dietary lifestyle options
  static const List<String> dietaryLifestyles = [
    'Ăn đa dạng',
    'Ăn chay',
    'Thuần chay (Vegan)',
    'Keto',
    'Paleo',
    'Địa Trung Hải',
    'Low-carb',
    'Intermittent Fasting',
    'Gluten-free',
    'Dairy-free',
    'Raw Food',
    'Pescatarian',
  ];

  /// Cuisine preferences
  static const List<Map<String, String>> cuisinePreferences = [
    {'name': 'Việt Nam', 'flag': '🇻🇳'},
    {'name': 'Trung Quốc', 'flag': '🇨🇳'},
    {'name': 'Nhật Bản', 'flag': '🇯🇵'},
    {'name': 'Hàn Quốc', 'flag': '🇰🇷'},
    {'name': 'Thái Lan', 'flag': '🇹🇭'},
    {'name': 'Ý', 'flag': '🇮🇹'},
    {'name': 'Pháp', 'flag': '🇫🇷'},
    {'name': 'Mỹ', 'flag': '🇺🇸'},
    {'name': 'Mexico', 'flag': '🇲🇽'},
    {'name': 'Ấn Độ', 'flag': '🇮🇳'},
    {'name': 'Địa Trung Hải', 'flag': '🌊'},
    {'name': 'Fusion', 'flag': '🌍'},
  ];

  /// Kitchen equipment
  static const List<String> kitchenEquipment = [
    'Bếp gas',
    'Bếp từ',
    'Lò vi sóng',
    'Lò nướng',
    'Nồi cơm điện',
    'Máy xay sinh tố',
    'Máy ép trái cây',
    'Nồi áp suất',
    'Chảo chống dính',
    'Nồi inox',
    'Dao bếp',
    'Thớt',
    'Máy đánh trứng',
    'Cân điện tử',
    'Nhiệt kế thực phẩm',
  ];

  /// Meal suggestions
  static const List<String> mealSuggestions = [
    'Bữa sáng',
    'Bữa trưa',
    'Bữa tối',
    'Món ăn vặt',
    'Đồ uống',
    'Tráng miệng',
  ];

  /// Onboarding steps configuration
  static const List<OnboardingStep> steps = [
    // Segment 1: Basic Information (3 screens) - Nhân cách hóa với Chuối Đầu Bếp
    OnboardingStep(
      id: 'gender',
      title: 'Để hiểu rõ hơn về bạn, cho mình biết...',
      description: 'Mình sẽ cá nhân hóa những gợi ý món ăn tuyệt vời nhất dành riêng cho bạn! 👨‍🍳',
      segment: 0,
      screenIndex: 0,
    ),
    OnboardingStep(
      id: 'height_weight',
      title: 'Cùng mình ghi nhận thông số cơ thể nhé!',
      description: 'Với chiều cao và cân nặng, mình sẽ tính toán chính xác lượng calo và dinh dưỡng tốt nhất cho bạn.',
      segment: 0,
      screenIndex: 1,
    ),
    OnboardingStep(
      id: 'birth_date',
      title: 'Ngày sinh của bạn là khi nào?',
      description: 'Mỗi độ tuổi có nhu cầu dinh dưỡng khác nhau, mình muốn đề xuất chế độ ăn phù hợp nhất!',
      segment: 0,
      screenIndex: 2,
    ),

    // Segment 2: Health Conditions (2-3 screens) - Quan tâm sức khỏe
    OnboardingStep(
      id: 'allergies',
      title: 'Có thực phẩm nào bạn bị dị ứng không?',
      description: 'Mình sẽ cẩn thận loại bỏ hoàn toàn những thành phần này khỏi mọi công thức nấu ăn! 🛡️',
      segment: 1,
      screenIndex: 0,
    ),
    OnboardingStep(
      id: 'medical_conditions',
      title: 'Tình trạng sức khỏe hiện tại của bạn',
      description: 'Mình muốn đề xuất những món ăn không chỉ ngon mà còn tốt cho sức khỏe của bạn.',
      segment: 1,
      screenIndex: 1,
    ),
    OnboardingStep(
      id: 'pregnancy_status',
      title: 'Bạn có đang mang thai không?',
      description: 'Mình sẽ đặc biệt chăm sóc dinh dưỡng cho cả mẹ và bé! 🤱',
      segment: 1,
      screenIndex: 2,
      conditional: true, // Only for females
    ),

    // Segment 3: Taste & Preferences (4 screens) - Khám phá khẩu vị
    OnboardingStep(
      id: 'favorite_food',
      title: 'Những thực phẩm nào làm bạn thích thú?',
      description: 'Hãy cho mình biết sở thích của bạn, mình sẽ ưu tiên chúng trong mọi công thức! ❤️',
      segment: 2,
      screenIndex: 0,
    ),
    OnboardingStep(
      id: 'disliked_food',
      title: 'Có thực phẩm nào bạn không ưa không?',
      description: 'Đừng lo, mình sẽ tránh hoàn toàn những thứ này trong gợi ý của bạn.',
      segment: 2,
      screenIndex: 1,
    ),
    OnboardingStep(
      id: 'spice_level',
      title: 'Bạn thích ăn cay ở mức độ nào?',
      description: 'Từ nhẹ nhàng đến "lửa thiêu", mình sẽ điều chỉnh độ cay vừa vặn cho bạn! 🌶️',
      segment: 2,
      screenIndex: 2,
    ),
    OnboardingStep(
      id: 'texture_preferences',
      title: 'Kết cấu món ăn bạn yêu thích?',
      description: 'Giòn, mềm, dai hay mịn? Mình sẽ gợi ý những món có kết cấu bạn thích nhất.',
      segment: 2,
      screenIndex: 3,
    ),

    // Segment 4: Culture & Lifestyle (6 screens) - Phong cách bếp núc
    OnboardingStep(
      id: 'cuisine_preferences',
      title: 'Nền ẩm thực nào khiến bạn say mê?',
      description: 'Từ Việt Nam đến Ý, Nhật, Hàn... Hãy cùng mình khám phá thế giới ẩm thực! 🌍',
      segment: 3,
      screenIndex: 0,
    ),
    OnboardingStep(
      id: 'dietary_lifestyle',
      title: 'Phong cách ăn uống của bạn ra sao?',
      description: 'Chay, keto, low-carb... Mình sẽ lọc những công thức phù hợp với lối sống của bạn.',
      segment: 3,
      screenIndex: 1,
    ),
    OnboardingStep(
      id: 'cooking_skill',
      title: 'Bạn tự đánh giá kỹ năng nấu ăn như thế nào?',
      description: 'Từ người mới bắt đầu đến bậc thầy, mình có công thức phù hợp cho mọi trình độ! 👨‍🍳',
      segment: 3,
      screenIndex: 2,
    ),
    OnboardingStep(
      id: 'cooking_time',
      title: 'Bạn thường dành bao nhiêu thời gian để nấu ăn?',
      description: '15 phút hay 2 tiếng? Mình sẽ gợi ý những món vừa ngon vừa phù hợp với thời gian của bạn.',
      segment: 3,
      screenIndex: 3,
    ),
    OnboardingStep(
      id: 'kitchen_equipment',
      title: 'Nhà bếp của bạn có những thiết bị gì?',
      description: 'Cho mình biết "kho vũ khí" trong bếp để gợi ý những công thức tuyệt vời nhất! 🔪',
      segment: 3,
      screenIndex: 4,
    ),
    OnboardingStep(
      id: 'meal_suggestions',
      title: 'Bạn muốn nhận gợi ý cho bữa ăn nào?',
      description: 'Sáng, trưa, tối hay tất cả? Hãy hoàn tất thiết lập để bắt đầu hành trình ẩm thực! 🎉',
      segment: 3,
      screenIndex: 5,
    ),
  ];

  /// Get steps for a specific segment
  static List<OnboardingStep> getStepsForSegment(int segment) {
    return steps.where((step) => step.segment == segment).toList();
  }

  /// Get total number of segments
  static int get totalSegments => 4;

  /// Get segment names - Nhân cách hóa với Chuối Đầu Bếp
  static const List<String> segmentNames = [
    'Làm quen với bạn',
    'Chăm sóc sức khỏe',
    'Khám phá khẩu vị',
    'Phong cách bếp núc',
  ];
}

/// Model for onboarding step configuration
class OnboardingStep {
  final String id;
  final String title;
  final String? description;
  final int segment;
  final int screenIndex;
  final bool conditional;

  const OnboardingStep({
    required this.id,
    required this.title,
    this.description,
    required this.segment,
    required this.screenIndex,
    this.conditional = false,
  });
}
