import 'package:equatable/equatable.dart';

/// Enum for gender selection
enum Gender { male, female, other }

/// Enum for pregnancy status
enum PregnancyStatus { pregnant, breastfeeding, none }

/// Enum for dietary lifestyle
enum DietaryLifestyle { diverse, vegetarian, vegan, keto, paleo, mediterranean }

/// Enum for cooking skill level
enum CookingSkill { beginner, intermediate, advanced, expert }

/// Enum for cooking time preference
enum CookingTime { quick, moderate, long, noPreference }

/// Model for onboarding data that will be collected and used in profile
class OnboardingData extends Equatable {
  // Basic Information (Segment 1)
  final Gender? gender;
  final double? height; // in cm
  final double? weight; // in kg
  final DateTime? birthDate;

  // Health Conditions (Segment 2)
  final List<String> allergies;
  final List<String> medicalConditions;
  final PregnancyStatus? pregnancyStatus;

  // Taste & Preferences (Segment 3)
  final List<String> favoriteFood;
  final List<String> dislikedFood;
  final int spiceLevel; // 0 to 5 (0: <PERSON><PERSON><PERSON><PERSON> cay, 1: <PERSON><PERSON><PERSON> nhẹ, 2: Nhẹ, 3: Vừa, 4: <PERSON><PERSON>, 5: <PERSON><PERSON>t cay)
  final List<String> texturePreferences;

  // Culture & Lifestyle (Segment 4)
  final List<String> cuisinePreferences;
  final List<String> dietaryLifestyles; // Changed to List<String> for multi-selection
  final CookingSkill? cookingSkill;
  final CookingTime? cookingTime;
  final List<String> kitchenEquipment;
  final List<String> mealSuggestions;

  const OnboardingData({
    // Basic Information
    this.gender,
    this.height,
    this.weight,
    this.birthDate,
    
    // Health Conditions
    this.allergies = const [],
    this.medicalConditions = const [],
    this.pregnancyStatus,
    
    // Taste & Preferences
    this.favoriteFood = const [],
    this.dislikedFood = const [],
    this.spiceLevel = 2, // Default to "Nhẹ"
    this.texturePreferences = const [],
    
    // Culture & Lifestyle
    this.cuisinePreferences = const [],
    this.dietaryLifestyles = const [],
    this.cookingSkill,
    this.cookingTime,
    this.kitchenEquipment = const [],
    this.mealSuggestions = const [],
  });

  /// Copy with method for updating data
  OnboardingData copyWith({
    Gender? gender,
    double? height,
    double? weight,
    DateTime? birthDate,
    List<String>? allergies,
    List<String>? medicalConditions,
    PregnancyStatus? pregnancyStatus,
    List<String>? favoriteFood,
    List<String>? dislikedFood,
    int? spiceLevel,
    List<String>? texturePreferences,
    List<String>? cuisinePreferences,
    List<String>? dietaryLifestyles,
    CookingSkill? cookingSkill,
    CookingTime? cookingTime,
    List<String>? kitchenEquipment,
    List<String>? mealSuggestions,
  }) {
    return OnboardingData(
      gender: gender ?? this.gender,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      birthDate: birthDate ?? this.birthDate,
      allergies: allergies ?? this.allergies,
      medicalConditions: medicalConditions ?? this.medicalConditions,
      pregnancyStatus: pregnancyStatus ?? this.pregnancyStatus,
      favoriteFood: favoriteFood ?? this.favoriteFood,
      dislikedFood: dislikedFood ?? this.dislikedFood,
      spiceLevel: spiceLevel ?? this.spiceLevel,
      texturePreferences: texturePreferences ?? this.texturePreferences,
      cuisinePreferences: cuisinePreferences ?? this.cuisinePreferences,
      dietaryLifestyles: dietaryLifestyles ?? this.dietaryLifestyles,
      cookingSkill: cookingSkill ?? this.cookingSkill,
      cookingTime: cookingTime ?? this.cookingTime,
      kitchenEquipment: kitchenEquipment ?? this.kitchenEquipment,
      mealSuggestions: mealSuggestions ?? this.mealSuggestions,
    );
  }

  /// Check if basic information is complete
  bool get isBasicInfoComplete => 
      gender != null && height != null && weight != null && birthDate != null;

  /// Check if health conditions are complete
  bool get isHealthConditionsComplete => 
      allergies.isNotEmpty || medicalConditions.isNotEmpty || 
      (gender == Gender.female ? pregnancyStatus != null : true);

  /// Check if taste preferences are complete
  bool get isTastePreferencesComplete => 
      favoriteFood.isNotEmpty || dislikedFood.isNotEmpty || 
      texturePreferences.isNotEmpty;

  /// Check if lifestyle info is complete
  bool get isLifestyleComplete =>
      cuisinePreferences.isNotEmpty && dietaryLifestyles.isNotEmpty &&
      cookingSkill != null && cookingTime != null;

  /// Check if all onboarding is complete
  bool get isComplete => 
      isBasicInfoComplete && isHealthConditionsComplete && 
      isTastePreferencesComplete && isLifestyleComplete;

  /// Get completion percentage (0.0 to 1.0)
  double get completionPercentage {
    int completedSections = 0;
    if (isBasicInfoComplete) completedSections++;
    if (isHealthConditionsComplete) completedSections++;
    if (isTastePreferencesComplete) completedSections++;
    if (isLifestyleComplete) completedSections++;
    return completedSections / 4.0;
  }

  @override
  List<Object?> get props => [
        gender,
        height,
        weight,
        birthDate,
        allergies,
        medicalConditions,
        pregnancyStatus,
        favoriteFood,
        dislikedFood,
        spiceLevel,
        texturePreferences,
        cuisinePreferences,
        dietaryLifestyles,
        cookingSkill,
        cookingTime,
        kitchenEquipment,
        mealSuggestions,
      ];

  @override
  String toString() => 'OnboardingData(completion: ${(completionPercentage * 100).toStringAsFixed(1)}%)';
}

/// Extension methods for enum display
extension GenderExtension on Gender {
  String get displayName {
    switch (this) {
      case Gender.male:
        return 'Nam';
      case Gender.female:
        return 'Nữ';
      case Gender.other:
        return 'Khác';
    }
  }

  String get icon {
    switch (this) {
      case Gender.male:
        return '👨';
      case Gender.female:
        return '👩';
      case Gender.other:
        return '🧑';
    }
  }
}

extension PregnancyStatusExtension on PregnancyStatus {
  String get displayName {
    switch (this) {
      case PregnancyStatus.pregnant:
        return 'Mang thai';
      case PregnancyStatus.breastfeeding:
        return 'Cho con bú';
      case PregnancyStatus.none:
        return 'Không có';
    }
  }
}

extension DietaryLifestyleExtension on DietaryLifestyle {
  String get displayName {
    switch (this) {
      case DietaryLifestyle.diverse:
        return 'Ăn đa dạng';
      case DietaryLifestyle.vegetarian:
        return 'Ăn chay';
      case DietaryLifestyle.vegan:
        return 'Thuần chay';
      case DietaryLifestyle.keto:
        return 'Keto';
      case DietaryLifestyle.paleo:
        return 'Paleo';
      case DietaryLifestyle.mediterranean:
        return 'Địa Trung Hải';
    }
  }
}

extension CookingSkillExtension on CookingSkill {
  String get displayName {
    switch (this) {
      case CookingSkill.beginner:
        return 'Mới bắt đầu';
      case CookingSkill.intermediate:
        return 'Trung bình';
      case CookingSkill.advanced:
        return 'Khá giỏi';
      case CookingSkill.expert:
        return 'Chuyên nghiệp';
    }
  }

  String get icon {
    switch (this) {
      case CookingSkill.beginner:
        return '🥄';
      case CookingSkill.intermediate:
        return '🍳';
      case CookingSkill.advanced:
        return '👨‍🍳';
      case CookingSkill.expert:
        return '⭐';
    }
  }
}

extension CookingTimeExtension on CookingTime {
  String get displayName {
    switch (this) {
      case CookingTime.quick:
        return 'Nhanh (< 30 phút)';
      case CookingTime.moderate:
        return 'Vừa phải (30-60 phút)';
      case CookingTime.long:
        return 'Lâu (> 60 phút)';
      case CookingTime.noPreference:
        return 'Không quan trọng';
    }
  }

  String get icon {
    switch (this) {
      case CookingTime.quick:
        return '⚡';
      case CookingTime.moderate:
        return '⏰';
      case CookingTime.long:
        return '🕐';
      case CookingTime.noPreference:
        return '🤷‍♂️';
    }
  }
}
