/// Enum for measurement unit systems
enum MeasurementUnit {
  metric,
  imperial;

  String get displayName {
    switch (this) {
      case MeasurementUnit.metric:
        return 'Metric';
      case MeasurementUnit.imperial:
        return 'Imperial';
    }
  }

  String get heightUnit {
    switch (this) {
      case MeasurementUnit.metric:
        return 'cm';
      case MeasurementUnit.imperial:
        return 'ft';
    }
  }

  String get weightUnit {
    switch (this) {
      case MeasurementUnit.metric:
        return 'kg';
      case MeasurementUnit.imperial:
        return 'lbs';
    }
  }
}

/// Utility class for unit conversions
class UnitConverter {
  // Height conversions
  static double cmToFeet(double cm) => cm / 30.48;
  static double feetToCm(double feet) => feet * 30.48;
  
  static int cmToFeetPart(double cm) => (cm / 30.48).floor();
  static int cmToInchesPart(double cm) => ((cm / 2.54) % 12).round();
  
  static double feetInchesToCm(int feet, int inches) => (feet * 30.48) + (inches * 2.54);

  // Weight conversions
  static double kgToLbs(double kg) => kg * 2.20462;
  static double lbsToKg(double lbs) => lbs / 2.20462;

  // Format display values
  static String formatHeight(double value, MeasurementUnit unit) {
    switch (unit) {
      case MeasurementUnit.metric:
        return '${value.round()} cm';
      case MeasurementUnit.imperial:
        final feet = cmToFeetPart(value);
        final inches = cmToInchesPart(value);
        return '$feet\' $inches"';
    }
  }

  static String formatWeight(double value, MeasurementUnit unit) {
    switch (unit) {
      case MeasurementUnit.metric:
        return '${value.toStringAsFixed(1)} kg';
      case MeasurementUnit.imperial:
        final lbs = kgToLbs(value);
        return '${lbs.toStringAsFixed(1)} lbs';
    }
  }

  // Get ranges for different units
  static (double min, double max, double step) getHeightRange(MeasurementUnit unit) {
    switch (unit) {
      case MeasurementUnit.metric:
        return (140.0, 220.0, 1.0); // cm
      case MeasurementUnit.imperial:
        return (140.0, 220.0, 1.0); // Still store as cm internally
    }
  }

  static (double min, double max, double step) getWeightRange(MeasurementUnit unit) {
    switch (unit) {
      case MeasurementUnit.metric:
        return (30.0, 150.0, 0.1); // kg with 0.1 precision
      case MeasurementUnit.imperial:
        return (30.0, 150.0, 0.1); // Still store as kg internally
    }
  }
}
