import '../../../core/bloc/base_state.dart';
import '../models/referral_models.dart';

/// Base state for referral feature
abstract class ReferralState extends BaseState {
  const ReferralState();
}

/// Initial state
class ReferralInitial extends ReferralState {
  const ReferralInitial();

  @override
  String get stateName => 'ReferralInitial';

  @override
  List<Object?> get props => [];
}

/// Loading state when applying referral code
class ReferralLoading extends ReferralState implements BaseLoadingState {
  @override
  final String? loadingMessage;

  const ReferralLoading({this.loadingMessage});

  @override
  String get stateName => 'ReferralLoading';

  @override
  List<Object?> get props => [loadingMessage];
}

/// Success state when referral code applied successfully
class ReferralSuccess extends ReferralState implements BaseSuccessState<ReferralResponse> {
  @override
  final ReferralResponse data;
  
  @override
  final String? message;

  const ReferralSuccess({
    required this.data,
    this.message,
  });

  @override
  String get stateName => 'ReferralSuccess';

  @override
  List<Object?> get props => [data, message];
}

/// State when user already has a referral code from login
class ReferralAlreadyHasCode extends ReferralState {
  final String referralCode;
  final String message;

  const ReferralAlreadyHasCode({
    required this.referralCode,
    required this.message,
  });

  @override
  String get stateName => 'ReferralAlreadyHasCode';

  @override
  List<Object?> get props => [referralCode, message];
}

/// Error state when referral code application fails
class ReferralError extends ReferralState implements BaseErrorState {
  @override
  final String message;

  @override
  final String? errorCode;

  @override
  final dynamic originalError;

  @override
  final StackTrace? stackTrace;

  const ReferralError({
    required this.message,
    this.errorCode,
    this.originalError,
    this.stackTrace,
  });

  @override
  String get stateName => 'ReferralError';

  @override
  List<Object?> get props => [message, errorCode, originalError, stackTrace];
}
