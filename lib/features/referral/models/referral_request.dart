import 'package:json_annotation/json_annotation.dart';

part 'referral_request.g.dart';

/// Request model for applying referral code
@JsonSerializable()
class ReferralRequest {
  @JsonKey(name: 'referral_code')
  final String referralCode;

  const ReferralRequest({
    required this.referralCode,
  });

  /// Create from JSON
  factory ReferralRequest.fromJson(Map<String, dynamic> json) =>
      _$ReferralRequestFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$ReferralRequestToJson(this);

  @override
  String toString() => 'ReferralRequest(referralCode: $referralCode)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ReferralRequest && other.referralCode == referralCode;
  }

  @override
  int get hashCode => referralCode.hashCode;
}
