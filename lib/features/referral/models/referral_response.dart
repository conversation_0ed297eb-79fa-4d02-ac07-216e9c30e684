import 'package:json_annotation/json_annotation.dart';

part 'referral_response.g.dart';

/// Response model for referral code application
@JsonSerializable()
class ReferralResponse {
  /// Success status
  final bool success;

  /// Response message
  final String message;

  /// Discount amount (if applicable)
  @JsonKey(name: 'discount_amount')
  final double? discountAmount;

  /// Discount type (percentage, fixed, etc.)
  @JsonKey(name: 'discount_type')
  final String? discountType;

  /// Referrer information
  @JsonKey(name: 'referrer_name')
  final String? referrerName;

  const ReferralResponse({
    required this.success,
    required this.message,
    this.discountAmount,
    this.discountType,
    this.referrerName,
  });

  /// Create from JSON
  factory ReferralResponse.fromJson(Map<String, dynamic> json) =>
      _$ReferralResponseFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$ReferralResponseToJson(this);

  @override
  String toString() => 'ReferralResponse(success: $success, message: $message, '
      'discountAmount: $discountAmount, discountType: $discountType, '
      'referrerName: $referrerName)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ReferralResponse &&
        other.success == success &&
        other.message == message &&
        other.discountAmount == discountAmount &&
        other.discountType == discountType &&
        other.referrerName == referrerName;
  }

  @override
  int get hashCode => Object.hash(
        success,
        message,
        discountAmount,
        discountType,
        referrerName,
      );
}
