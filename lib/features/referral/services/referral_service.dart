import 'package:banachef/core/services/logger/logger.dart';
import 'package:injectable/injectable.dart';
import 'package:logger/logger.dart';
import '../../../core/api/api_client.dart';
import '../../../core/errors/exceptions.dart';
import '../models/referral_models.dart';

/// Abstract interface for referral service
abstract class ReferralService {
  /// Apply referral code
  Future<ReferralResponse> applyReferralCode(String referralCode);
}

/// Implementation of referral service
@Singleton(as: ReferralService)
class ReferralServiceImpl implements ReferralService {
  final ApiClient _apiClient;
  final Logger _logger = AppLogger.log;

  ReferralServiceImpl(this._apiClient);

  @override
  Future<ReferralResponse> applyReferralCode(String referralCode) async {
    try {
      _logger.d('Applying referral code: $referralCode');

      final request = ReferralRequest(referralCode: referralCode);
      
      final response = await _apiClient.post<Map<String, dynamic>>(
        'referrals/apply',
        data: request.toJson(),
      );

      if (response.statusCode == 200 && response.data != null) {
        final referralResponse = ReferralResponse.fromJson(response.data!);
        _logger.d('Referral code applied successfully: ${referralResponse.message}');
        return referralResponse;
      } else {
        throw ServerException(
           'Unexpected response format',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      _logger.e('Error applying referral code: $e');
      
      if (e is ServerException) {
        rethrow;
      } else if (e is NetworkException) {
        rethrow;
      } else {
        throw ServerException(
        'Failed to apply referral code: ${e.toString()}',
        );
      }
    }
  }
}
