# Referral Feature

Tính năng mã giới thiệu cho ứng dụng BanaChef, cho phép người dùng nhập mã giới thiệu để nhận ưu đãi.

## 📁 Cấu trúc thư mục

```
lib/features/referral/
├── cubit/
│   ├── referral_cubit.dart      # State management cho referral
│   └── referral_state.dart      # Các states của referral
├── models/
│   ├── referral_request.dart    # Model cho API request
│   ├── referral_response.dart   # Model cho API response
│   └── referral_models.dart     # Export file
├── services/
│   └── referral_service.dart    # Service gọi API
├── view/
│   └── referral_code_screen.dart # Màn hình nhập mã giới thiệu
├── widgets/
│   └── referral_frame_widget.dart # Widget hiển thị frame design
└── README.md
```

## 🚀 Cách sử dụng

### 1. Navigation đến màn hình referral

```dart
// Từ onboarding sau khi login thành công
context.router.pushPath('/referral');

// Hoặc từ bất kỳ đâu trong app
context.router.push('/referral');
```

### 2. Sử dụng ReferralCubit

```dart
// Inject cubit
final referralCubit = getIt<ReferralCubit>();

// Apply referral code
referralCubit.applyReferralCode('REFERRAL123');

// Listen to states
BlocListener<ReferralCubit, ReferralState>(
  listener: (context, state) {
    if (state is ReferralSuccess) {
      // Handle success
      print('Success: ${state.message}');
    } else if (state is ReferralError) {
      // Handle error
      print('Error: ${state.message}');
    }
  },
  child: YourWidget(),
)
```

## 🎨 UI Design

### Responsive Design
- Sử dụng `ResponsiveContainer` và `context.pagePadding`
- Tương thích với mobile, tablet, desktop
- Spacing system nhất quán với app

### Components
- **ReferralFrameWidget**: Hiển thị frame design với dashed border
- **Input Field**: TextField với icon và validation
- **Apply Button**: AppButton với loading states
- **Skip Button**: TextButton để bỏ qua

### States
- **Initial**: Màn hình ban đầu
- **Loading**: Hiển thị loading indicator
- **Success**: Hiển thị thông báo thành công, disable input
- **Error**: Hiển thị error snackbar

## 🔧 API Integration

### Endpoint
```
POST /v1/referrals/apply
```

### Request
```json
{
  "referral_code": "REFERRAL123"
}
```

### Response Success (200)
```json
{
  "success": true,
  "message": "Thành công! Bạn đã nhận được ưu đãi giảm 5$ cho lần mua đầu tiên.",
  "discount_amount": 5.0,
  "discount_type": "fixed",
  "referrer_name": "John Doe"
}
```

### Response Error (4xx/5xx)
```json
{
  "success": false,
  "message": "Mã không hợp lệ. Vui lòng kiểm tra lại.",
  "error_code": "INVALID_CODE"
}
```

## 🧪 Testing

### Unit Tests
```bash
flutter test test/features/referral/
```

### Widget Tests
- Test UI components
- Test user interactions
- Test state changes
- Test error handling

## 🔄 Flow

1. **User Login** → Navigate to referral screen
2. **Enter Code** → Validate input
3. **Apply Code** → Call API
4. **Success** → Show success message, enable continue
5. **Error** → Show error message, allow retry
6. **Skip/Continue** → Navigate to dashboard

## 📱 Screenshots

### Initial State
- Frame design với dashed border
- Input field với placeholder
- Apply button (disabled)
- Skip button

### Loading State
- Loading indicator trong apply button
- Input field disabled

### Success State
- Input field với check icon
- "Đã áp dụng" button (disabled)
- "Tiếp tục" button enabled
- Success snackbar

### Error State
- Error snackbar với message
- Input field enabled để retry

## 🎯 Features

- ✅ Responsive design
- ✅ Loading states
- ✅ Error handling
- ✅ Success feedback
- ✅ Input validation
- ✅ API integration
- ✅ Navigation flow
- ✅ Accessibility support
- ✅ Unit tests
- ✅ Widget tests

## 🔧 Configuration

### Dependency Injection
```dart
// Đã được đăng ký trong injection_container.dart
getIt.registerSingleton<ReferralService>(
  ReferralServiceImpl(getIt<ApiClient>()),
);

getIt.registerFactory<ReferralCubit>(
  () => ReferralCubit(getIt<ReferralService>()),
);
```

### Routing
```dart
// Đã được thêm vào app_router.dart
AutoRoute(page: ReferralCodeRoute.page, path: '/referral'),
```
