import 'package:equatable/equatable.dart';

class Recipe extends Equatable {
  final String id;
  final String title;
  final String description;
  final String imageUrl;
  final List<Ingredient> ingredients;
  final List<String> instructions;
  final int prepTime; // in minutes
  final int cookTime; // in minutes
  final int servings;
  final double rating;
  final int reviewCount;
  final String difficulty; // easy, medium, hard
  final List<String> tags;
  final NutritionInfo nutrition;
  final String authorId;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Recipe({
    required this.id,
    required this.title,
    required this.description,
    required this.imageUrl,
    required this.ingredients,
    required this.instructions,
    required this.prepTime,
    required this.cookTime,
    required this.servings,
    required this.rating,
    required this.reviewCount,
    required this.difficulty,
    required this.tags,
    required this.nutrition,
    required this.authorId,
    required this.createdAt,
    required this.updatedAt,
  });

  int get totalTime => prepTime + cookTime;

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        imageUrl,
        ingredients,
        instructions,
        prepTime,
        cookTime,
        servings,
        rating,
        reviewCount,
        difficulty,
        tags,
        nutrition,
        authorId,
        createdAt,
        updatedAt,
      ];
}

class Ingredient extends Equatable {
  final String name;
  final double amount;
  final String unit;
  final bool isOptional;

  const Ingredient({
    required this.name,
    required this.amount,
    required this.unit,
    this.isOptional = false,
  });

  @override
  List<Object?> get props => [name, amount, unit, isOptional];
}

class NutritionInfo extends Equatable {
  final int calories;
  final double protein; // in grams
  final double carbs; // in grams
  final double fat; // in grams
  final double fiber; // in grams
  final double sugar; // in grams
  final double sodium; // in mg

  const NutritionInfo({
    required this.calories,
    required this.protein,
    required this.carbs,
    required this.fat,
    required this.fiber,
    required this.sugar,
    required this.sodium,
  });

  @override
  List<Object?> get props => [
        calories,
        protein,
        carbs,
        fat,
        fiber,
        sugar,
        sodium,
      ];
}
