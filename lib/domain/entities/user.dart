import 'package:equatable/equatable.dart';

class User extends Equatable {
  final String id;
  final String email;
  final String? name;
  final String? avatar;
  final DateTime createdAt;
  final DateTime updatedAt;
  final UserPreferences preferences;

  const User({
    required this.id,
    required this.email,
    this.name,
    this.avatar,
    required this.createdAt,
    required this.updatedAt,
    required this.preferences,
  });

  @override
  List<Object?> get props => [
        id,
        email,
        name,
        avatar,
        createdAt,
        updatedAt,
        preferences,
      ];

  User copyWith({
    String? id,
    String? email,
    String? name,
    String? avatar,
    DateTime? createdAt,
    DateTime? updatedAt,
    UserPreferences? preferences,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      avatar: avatar ?? this.avatar,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      preferences: preferences ?? this.preferences,
    );
  }
}

class UserPreferences extends Equatable {
  final List<String> dietaryRestrictions;
  final List<String> allergies;
  final String preferredCuisine;
  final int servingSize;
  final bool notificationsEnabled;

  const UserPreferences({
    required this.dietaryRestrictions,
    required this.allergies,
    required this.preferredCuisine,
    required this.servingSize,
    required this.notificationsEnabled,
  });

  @override
  List<Object?> get props => [
        dietaryRestrictions,
        allergies,
        preferredCuisine,
        servingSize,
        notificationsEnabled,
      ];

  UserPreferences copyWith({
    List<String>? dietaryRestrictions,
    List<String>? allergies,
    String? preferredCuisine,
    int? servingSize,
    bool? notificationsEnabled,
  }) {
    return UserPreferences(
      dietaryRestrictions: dietaryRestrictions ?? this.dietaryRestrictions,
      allergies: allergies ?? this.allergies,
      preferredCuisine: preferredCuisine ?? this.preferredCuisine,
      servingSize: servingSize ?? this.servingSize,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
    );
  }
}
