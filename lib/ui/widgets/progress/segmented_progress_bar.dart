import 'package:flutter/material.dart';
import '../../responsive/responsive.dart';
import 'progress_segment.dart';

/// A responsive segmented progress bar widget
///
/// Features:
/// - Multiple segments with individual progress
/// - Responsive design following project patterns
/// - Customizable colors and styling
/// - Accessibility support
/// - Animation support
class SegmentedProgressBar extends StatelessWidget {
  /// List of progress segments to display
  final List<ProgressSegment> segments;

  /// Height of the progress bar
  final double? height;

  /// Border radius for the progress bar
  final double? borderRadius;

  /// Spacing between segments
  final double? segmentSpacing;

  /// Default color for completed segments
  final Color? completedColor;

  /// Default color for incomplete segments
  final Color? incompleteColor;

  /// Default background color for segments
  final Color? backgroundColor;

  /// Whether to show animation when progress changes
  final bool animated;

  /// Duration of animations
  final Duration animationDuration;

  /// Curve for animations
  final Curve animationCurve;

  /// Callback when a segment is tapped
  final void Function(ProgressSegment segment)? onSegmentTap;

  /// Whether to show labels below segments
  final bool showLabels;

  /// Style for segment labels
  final TextStyle? labelStyle;

  const SegmentedProgressBar({
    super.key,
    required this.segments,
    this.height,
    this.borderRadius,
    this.segmentSpacing,
    this.completedColor,
    this.incompleteColor,
    this.backgroundColor,
    this.animated = true,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    this.onSegmentTap,
    this.showLabels = false,
    this.labelStyle,
  });

  @override
  Widget build(BuildContext context) {
    return _buildProgressBar(context, segments);
  }

  Widget _buildProgressBar(BuildContext context, List<ProgressSegment> segments) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    // Responsive values
    final effectiveHeight = height ?? context.responsive(
      ResponsiveValue(mobile: 8.0, tablet: 10.0, desktop: 12.0),
    ).toDouble();

    final effectiveBorderRadius = borderRadius ?? context.responsive(
      ResponsiveValue(mobile: 4.0, tablet: 5.0, desktop: 6.0),
    ).toDouble();

    final effectiveSpacing = segmentSpacing ?? context.responsive(
      ResponsiveValue(mobile: 4.0, tablet: 6.0, desktop: 8.0),
    ).toDouble();

    // Colors
    final defaultCompletedColor = completedColor ?? colorScheme.primary;
    final defaultIncompleteColor = incompleteColor ?? colorScheme.outline;
    final defaultBackgroundColor = backgroundColor ?? colorScheme.surfaceContainerHighest;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Progress bar
        SizedBox(
          height: effectiveHeight,
          child: Row(
            children: _buildSegmentWidgets(
              context,
              segments,
              effectiveHeight,
              effectiveBorderRadius,
              effectiveSpacing,
              defaultCompletedColor,
              defaultIncompleteColor,
              defaultBackgroundColor,
            ),
          ),
        ),
        
        // Labels
        if (showLabels) ...[
          VSpace.sm(),
          _buildLabels(context, segments, effectiveSpacing),
        ],
      ],
    );
  }

  List<Widget> _buildSegmentWidgets(
    BuildContext context,
    List<ProgressSegment> segments,
    double height,
    double borderRadius,
    double spacing,
    Color completedColor,
    Color incompleteColor,
    Color backgroundColor,
  ) {
    final List<Widget> widgets = [];
    
    for (int i = 0; i < segments.length; i++) {
      final segment = segments[i];
      
      // Add spacing between segments
      if (i > 0) {
        widgets.add(SizedBox(width: spacing));
      }
      
      // Calculate segment width based on progress
      final segmentWidth = _calculateSegmentWidth(context, segment);
      
      widgets.add(
        Expanded(
          flex: segmentWidth.round(),
          child: _buildSingleSegment(
            context,
            segment,
            height,
            borderRadius,
            completedColor,
            incompleteColor,
            backgroundColor,
          ),
        ),
      );
    }
    
    return widgets;
  }

  Widget _buildSingleSegment(
    BuildContext context,
    ProgressSegment segment,
    double height,
    double borderRadius,
    Color completedColor,
    Color incompleteColor,
    Color backgroundColor,
  ) {
    final segmentColor = segment.color ?? 
        (segment.progress > 0 ? completedColor : incompleteColor);
    final segmentBackgroundColor = segment.backgroundColor ?? backgroundColor;
    
    Widget segmentWidget = Container(
      height: height,
      decoration: BoxDecoration(
        color: segmentBackgroundColor,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: LinearProgressIndicator(
          value: segment.progress,
          backgroundColor: Colors.transparent,
          valueColor: AlwaysStoppedAnimation<Color>(segmentColor),
        ),
      ),
    );

    // Add animation if enabled
    if (animated) {
      segmentWidget = AnimatedContainer(
        duration: animationDuration,
        curve: animationCurve,
        child: segmentWidget,
      );
    }

    // Add tap handling if callback provided
    if (onSegmentTap != null) {
      segmentWidget = GestureDetector(
        onTap: () => onSegmentTap!(segment),
        child: segmentWidget,
      );
    }

    // Add active indicator if segment is active
    if (segment.isActive) {
      segmentWidget = Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(borderRadius + 2),
          border: Border.all(
            color: Theme.of(context).colorScheme.primary,
            width: 2,
          ),
        ),
        child: segmentWidget,
      );
    }

    return segmentWidget;
  }

  Widget _buildLabels(BuildContext context, List<ProgressSegment> segments, double spacing) {
    final defaultLabelStyle = labelStyle ?? context.bodySmall;
    
    return Row(
      children: segments.asMap().entries.map((entry) {
        final index = entry.key;
        final segment = entry.value;
        
        return Expanded(
          flex: _calculateSegmentWidth(context, segment).round(),
          child: Row(
            children: [
              if (index > 0) SizedBox(width: spacing),
              Expanded(
                child: Text(
                  segment.label ?? '${segment.percentage}%',
                  style: defaultLabelStyle,
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  double _calculateSegmentWidth(BuildContext context, ProgressSegment segment) {
    // Ensure minimum width for visibility
    final minWidth = segment.minWidth;
    final progressWidth = segment.progress * 100; // Convert to percentage
    
    return (progressWidth < minWidth) ? minWidth : progressWidth;
  }
}


