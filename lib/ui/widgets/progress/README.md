# Segmented Progress Bar Component

A responsive segmented progress bar widget designed to match the provided UI design and follow the project's responsive design patterns and BLoC architecture.

## Features

- ✅ **Responsive Design**: Automatically adapts to different screen sizes (mobile, tablet, desktop)
- ✅ **Multiple Segments**: Support for multiple progress segments with individual progress values
- ✅ **Custom Styling**: Customizable colors, sizes, and spacing
- ✅ **Simple State Management**: Uses standard setState for easy state updates
- ✅ **Animation Support**: Smooth animations for progress changes
- ✅ **Accessibility**: Proper labels and semantic structure
- ✅ **Interactive**: Optional tap handling for segments
- ✅ **Flexible Layout**: Variable segment widths based on progress

## Quick Start

### 1. Import

```dart
import 'package:banachef/ui/widgets/progress/progress_exports.dart';
```

### 2. Basic Usage

```dart
// Create segments
final segments = [
  ProgressSegment.completed(id: 'step1', label: 'Step 1'),
  ProgressSegment.completed(id: 'step2', label: 'Step 2'),
  ProgressSegment.withProgress(id: 'step3', progress: 0.6, label: 'Step 3'),
  ProgressSegment.empty(id: 'step4', label: 'Step 4'),
];

// Use the widget
SegmentedProgressBar(
  segments: segments,
  completedColor: const Color(0xFF6B46C1), // Purple from design
  showLabels: true,
)
```

### 3. Matching the Design Image

```dart
// Recreate the exact design from the provided image
final imageSegments = [
  ProgressSegment.completed(id: 'seg1', color: const Color(0xFF6B46C1)),
  ProgressSegment.completed(id: 'seg2', color: const Color(0xFF6B46C1)),
  ProgressSegment.completed(id: 'seg3', color: const Color(0xFF6B46C1)),
  ProgressSegment.withProgress(id: 'seg4', progress: 0.6, color: const Color(0xFF6B46C1)),
  ProgressSegment.empty(id: 'seg5'),
  ProgressSegment.empty(id: 'seg6'),
];

Container(
  padding: EdgeInsets.all(context.spacingLG),
  decoration: BoxDecoration(
    color: const Color(0xFF1F2937), // Dark background
    borderRadius: BorderRadius.circular(12),
  ),
  child: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        'Segmented Progress Bar',
        style: context.titleMedium.copyWith(color: Colors.white),
      ),
      VSpace.lg(),
      SegmentedProgressBar(
        segments: imageSegments,
        height: 16.0,
        borderRadius: 8.0,
        backgroundColor: const Color(0xFF374151),
        incompleteColor: const Color(0xFF6B7280),
      ),
    ],
  ),
)
```

## Advanced Usage

### With setState for Dynamic Updates

```dart
class MyWidget extends StatefulWidget {
  @override
  State<MyWidget> createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  List<ProgressSegment> segments = [
    ProgressSegment.completed(id: 'step1', label: 'Step 1'),
    ProgressSegment.withProgress(id: 'step2', progress: 0.6, label: 'Step 2'),
    ProgressSegment.empty(id: 'step3', label: 'Step 3'),
  ];

  @override
  Widget build(BuildContext context) {
    return SegmentedProgressBar(
      segments: segments,
      showLabels: true,
      onSegmentTap: (segment) {
        setState(() {
          // Update segment progress
          final index = segments.indexWhere((s) => s.id == segment.id);
          if (index != -1) {
            segments[index] = segment.copyWith(progress: 1.0);
          }
        });
      },
    );
  }
}
```

### Interactive Segments

```dart
SegmentedProgressBar(
  segments: segments,
  onSegmentTap: (segment) {
    // Cycle through progress states
    double newProgress;
    if (segment.progress == 0.0) {
      newProgress = 0.5;
    } else if (segment.progress == 0.5) {
      newProgress = 1.0;
    } else {
      newProgress = 0.0;
    }
    
    // Update the segment
    setState(() {
      final index = segments.indexWhere((s) => s.id == segment.id);
      segments[index] = segment.copyWith(progress: newProgress);
    });
  },
  animated: true,
  animationDuration: Duration(milliseconds: 300),
)
```

### Custom Styling

```dart
SegmentedProgressBar(
  segments: segments,
  height: 20.0,
  borderRadius: 10.0,
  segmentSpacing: 8.0,
  completedColor: const Color(0xFF10B981), // Green
  incompleteColor: const Color(0xFFEF4444), // Red
  backgroundColor: const Color(0xFFF3F4F6),
  showLabels: true,
  labelStyle: context.bodySmall.copyWith(
    fontWeight: FontWeight.w600,
  ),
)
```

### Responsive Design

```dart
// Automatic responsive sizing
SegmentedProgressBar(
  segments: segments,
  height: context.responsive(
    ResponsiveValue(mobile: 8.0, tablet: 12.0, desktop: 16.0),
  ),
  segmentSpacing: context.responsive(
    ResponsiveValue(mobile: 4.0, tablet: 6.0, desktop: 8.0),
  ),
)

// Different layouts for different devices
ResponsiveLayoutBuilder(
  mobile: SegmentedProgressBar(
    segments: segments,
    height: 8,
    showLabels: false,
  ),
  tablet: SegmentedProgressBar(
    segments: segments,
    height: 12,
    showLabels: true,
  ),
  desktop: SegmentedProgressBar(
    segments: segments,
    height: 16,
    showLabels: true,
    labelStyle: context.bodyLarge,
  ),
)
```

## API Reference

### ProgressSegment

```dart
class ProgressSegment {
  final String id;              // Unique identifier
  final double progress;        // 0.0 to 1.0
  final String? label;          // Optional label
  final Color? color;           // Optional custom color
  final Color? backgroundColor; // Optional background color
  final bool isActive;          // Whether segment is active
  final double minWidth;        // Minimum width in pixels
}
```

### SegmentedProgressBar

```dart
class SegmentedProgressBar {
  final List<ProgressSegment> segments;     // Required segments
  final double? height;                     // Bar height
  final double? borderRadius;               // Corner radius
  final double? segmentSpacing;             // Space between segments
  final Color? completedColor;              // Color for completed segments
  final Color? incompleteColor;             // Color for incomplete segments
  final Color? backgroundColor;             // Background color
  final bool animated;                      // Enable animations
  final Duration animationDuration;         // Animation duration
  final Curve animationCurve;              // Animation curve
  final Function(ProgressSegment)? onSegmentTap; // Tap callback
  final bool showLabels;                    // Show labels below
  final TextStyle? labelStyle;             // Label text style
}
```

### SegmentedProgressBarCubit

```dart
class SegmentedProgressBarCubit {
  void initialize(List<ProgressSegment> segments);
  void updateSegmentProgress(String segmentId, double progress);
  void updateMultipleSegments(Map<String, double> progressMap);
  void setActiveSegment(String segmentId);
  void clearActiveSegments();
  void resetAllSegments();
  void completeAllSegments();
}
```

## Examples

See `examples/segmented_progress_bar_example.dart` for comprehensive usage examples including:

- Basic usage matching the design image
- Interactive segments
- Custom styling variations
- State management integration
- Responsive layouts

## Design Principles

This component follows the project's design principles:

- **Responsive**: Uses the project's responsive system for consistent scaling
- **Accessible**: Proper semantic structure and labels
- **Consistent**: Follows project color schemes and spacing
- **Performant**: Efficient rendering and smooth animations
- **Maintainable**: Clean architecture with separation of concerns
- **Testable**: Clear state management and predictable behavior

## Integration with Project Architecture

- **BLoC Pattern**: Optional Cubit integration for state management
- **Responsive Design**: Full integration with project's responsive system
- **Theme Integration**: Respects app theme colors and typography
- **Error Handling**: Consistent error handling following project patterns
- **Logging**: Automatic logging for debugging and monitoring
