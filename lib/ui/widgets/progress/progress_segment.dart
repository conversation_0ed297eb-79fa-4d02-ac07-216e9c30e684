import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';

/// Model representing a single segment in the segmented progress bar
/// 
/// Each segment has:
/// - A unique identifier
/// - Current progress value (0.0 to 1.0)
/// - Optional label for accessibility
/// - Optional color customization
class ProgressSegment extends Equatable {
  /// Unique identifier for this segment
  final String id;
  
  /// Progress value from 0.0 (empty) to 1.0 (complete)
  final double progress;
  
  /// Optional label for accessibility and debugging
  final String? label;
  
  /// Optional custom color for this segment
  /// If null, will use theme colors
  final Color? color;
  
  /// Optional custom background color for this segment
  /// If null, will use theme colors
  final Color? backgroundColor;
  
  /// Whether this segment is currently active/highlighted
  final bool isActive;
  
  /// Minimum width for this segment (in logical pixels)
  /// Used to ensure segments are visible even with 0 progress
  final double minWidth;

  const ProgressSegment({
    required this.id,
    required this.progress,
    this.label,
    this.color,
    this.backgroundColor,
    this.isActive = false,
    this.minWidth = 24.0,
  }) : assert(progress >= 0.0 && progress <= 1.0, 'Progress must be between 0.0 and 1.0');

  /// Create a completed segment
  factory ProgressSegment.completed({
    required String id,
    String? label,
    Color? color,
    Color? backgroundColor,
    double minWidth = 24.0,
  }) {
    return ProgressSegment(
      id: id,
      progress: 1.0,
      label: label,
      color: color,
      backgroundColor: backgroundColor,
      minWidth: minWidth,
    );
  }

  /// Create an empty segment
  factory ProgressSegment.empty({
    required String id,
    String? label,
    Color? color,
    Color? backgroundColor,
    double minWidth = 24.0,
  }) {
    return ProgressSegment(
      id: id,
      progress: 0.0,
      label: label,
      color: color,
      backgroundColor: backgroundColor,
      minWidth: minWidth,
    );
  }

  /// Create a segment with specific progress
  factory ProgressSegment.withProgress({
    required String id,
    required double progress,
    String? label,
    Color? color,
    Color? backgroundColor,
    bool isActive = false,
    double minWidth = 24.0,
  }) {
    return ProgressSegment(
      id: id,
      progress: progress,
      label: label,
      color: color,
      backgroundColor: backgroundColor,
      isActive: isActive,
      minWidth: minWidth,
    );
  }

  /// Check if this segment is completed
  bool get isCompleted => progress >= 1.0;

  /// Check if this segment is empty
  bool get isEmpty => progress <= 0.0;

  /// Check if this segment is in progress
  bool get isInProgress => progress > 0.0 && progress < 1.0;

  /// Get the percentage as an integer (0-100)
  int get percentage => (progress * 100).round();

  /// Copy this segment with new values
  ProgressSegment copyWith({
    String? id,
    double? progress,
    String? label,
    Color? color,
    Color? backgroundColor,
    bool? isActive,
    double? minWidth,
  }) {
    return ProgressSegment(
      id: id ?? this.id,
      progress: progress ?? this.progress,
      label: label ?? this.label,
      color: color ?? this.color,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      isActive: isActive ?? this.isActive,
      minWidth: minWidth ?? this.minWidth,
    );
  }

  @override
  List<Object?> get props => [
        id,
        progress,
        label,
        color,
        backgroundColor,
        isActive,
        minWidth,
      ];

  @override
  String toString() {
    return 'ProgressSegment(id: $id, progress: $progress, label: $label, isActive: $isActive)';
  }
}
