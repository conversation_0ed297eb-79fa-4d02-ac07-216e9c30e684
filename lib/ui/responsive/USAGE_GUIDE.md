# Hướng Dẫn Sử Dụng Responsive Design System

## 📋 Tổng Quan

Hệ thống responsive design cho BanaChef App bao gồm 8 thành phần chính, mỗi thành phần có mục đích sử dụng cụ thể.

## 🎯 Khi Nào Sử Dụng Từng Class

### 1. ResponsiveBreakpoints & Extensions

**📱 Khi nào dùng:**
- Cần kiểm tra loại thiết bị hiện tại
- Cần logic khác nhau cho mobile/tablet/desktop
- Cần kiểm tra orientation (portrait/landscape)

**✅ Sử dụng khi:**
```dart
// Kiểm tra device type để hiển thị UI khác nhau
if (context.isMobile) {
  return SingleColumnLayout();
} else if (context.isTablet) {
  return TwoColumnLayout();
} else {
  return ThreeColumnLayout();
}

// Kiểm tra orientation
if (context.isLandscape) {
  return HorizontalLayout();
}

// L<PERSON>y thông tin màn hình
final screenWidth = context.screenWidth;
final deviceType = context.deviceType;
```

**❌ Không dùng khi:**
- Chỉ cần responsive đơn giản (dùng ResponsiveLayoutBuilder thay thế)
- Không cần logic phức tạp theo device

---

### 2. ResponsiveLayoutBuilder

**📱 Khi nào dùng:**
- Cần layout hoàn toàn khác nhau cho từng device
- Muốn tự động chọn layout phù hợp
- Cần container với max width responsive

**✅ Sử dụng khi:**
```dart
// Layout khác nhau cho từng device
ResponsiveLayoutBuilder(
  mobile: MobileHomePage(),
  tablet: TabletHomePage(),
  desktop: DesktopHomePage(),
)

// Container với max width
ResponsiveContainer(
  maxWidthMobile: double.infinity,
  maxWidthTablet: 800,
  maxWidthDesktop: 1200,
  child: content,
)

// Responsive values
final columns = context.responsive(ResponsiveValue(
  mobile: 1,
  tablet: 2,
  desktop: 3,
));
```

**❌ Không dùng khi:**
- Layout chỉ khác nhau về spacing/size (dùng ResponsiveSpacing)
- Chỉ cần grid responsive (dùng ResponsiveGrid)

---

### 3. ResponsiveSpacing

**📱 Khi nào dùng:**
- Cần padding/margin tự động điều chỉnh theo device
- Muốn spacing nhất quán trong toàn app
- Cần khoảng cách giữa các elements

**✅ Sử dụng khi:**
```dart
// Page padding chuẩn
Padding(
  padding: context.pagePadding,  // 16px mobile, 24px tablet, 32px desktop
  child: content,
)

// Spacing giữa các elements
Column(
  children: [
    Widget1(),
    VSpace.md(),  // Vertical spacing responsive
    Widget2(),
  ],
)

// Custom spacing
EdgeInsets.all(context.spacing(2))  // 2 * base unit, scaled by device

// Content padding cho cards, dialogs
Padding(
  padding: context.contentPadding,
  child: cardContent,
)
```

**❌ Không dùng khi:**
- Cần spacing cố định không đổi
- Spacing quá đặc biệt không theo hệ thống

---

### 4. ResponsiveTypography

**📱 Khi nào dùng:**
- Cần text size tự động điều chỉnh theo device
- Muốn typography nhất quán
- Cần text styles chuẩn Material Design

**✅ Sử dụng khi:**
```dart
// Text với style responsive
Text('Title', style: context.headlineMedium)
Text('Body', style: context.bodyLarge)

// ResponsiveText widget
ResponsiveText.headlineMedium('Page Title')
ResponsiveText.bodyLarge('Content text')

// Custom responsive font size
Text(
  'Custom',
  style: TextStyle(
    fontSize: context.fontSize(16), // Auto scale: 16/17.6/19.2
  ),
)
```

**❌ Không dùng khi:**
- Cần font size cố định (như icons text)
- Text có yêu cầu đặc biệt về styling

---

### 5. ResponsiveGrid

**📱 Khi nào dùng:**
- Hiển thị danh sách items dạng grid
- Cần số cột tự động điều chỉnh theo device
- Layout grid hoặc staggered grid

**✅ Sử dụng khi:**
```dart
// Grid cho recipe cards
ResponsiveGrid(
  mobileColumns: 1,
  tabletColumns: 2,
  desktopColumns: 3,
  children: recipes.map((recipe) => RecipeCard(recipe)).toList(),
)

// Staggered grid cho photos
ResponsiveStaggeredGrid(
  mobileColumns: 2,
  tabletColumns: 3,
  desktopColumns: 4,
  children: photos,
)

// Wrap cho tags/chips
ResponsiveWrap(
  children: tags.map((tag) => Chip(label: Text(tag))).toList(),
)

// Tính số cột tự động
final columns = context.gridColumns(itemMinWidth: 200);
```

**❌ Không dùng khi:**
- Chỉ có 1-2 items (dùng Row/Column)
- Layout phức tạp không phải grid (dùng ResponsiveLayoutBuilder)

---

### 6. ResponsiveNavigation

**📱 Khi nào dùng:**
- App có navigation chính
- Muốn bottom nav trên mobile, side nav trên tablet/desktop
- Cần app bar responsive

**✅ Sử dụng khi:**
```dart
// Main app navigation
ResponsiveNavigation(
  items: [
    ResponsiveNavigationItem(
      icon: Icons.home_outlined,
      activeIcon: Icons.home,
      label: 'Trang chủ',
    ),
    // ... more items
  ],
  currentIndex: currentIndex,
  onTap: (index) => setState(() => currentIndex = index),
  body: pages[currentIndex],
)

// App bar cho từng page
ResponsiveAppBar(
  title: 'Page Title',
  actions: [IconButton(...)],
)

// Side navigation riêng
ResponsiveSideNavigation(
  items: navigationItems,
  currentIndex: currentIndex,
  onTap: onTap,
)
```

**❌ Không dùng khi:**
- App không có navigation chính
- Cần custom navigation phức tạp

---

### 7. ResponsiveWidgets

**📱 Khi nào dùng:**
- Cần UI components cơ bản responsive
- Muốn widgets tự động thích ứng với device

**✅ Sử dụng khi:**
```dart
// Card với padding responsive
ResponsiveCard(
  onTap: () => navigateToDetail(),
  child: cardContent,
)

// Button với size responsive
ResponsiveButton.large(
  text: 'Thêm công thức',
  onPressed: () => addRecipe(),
  icon: Icon(Icons.add),
)

// Dialog tự động thành bottom sheet trên mobile
ResponsiveBottomSheet.show(
  context: context,
  title: 'Tùy chọn',
  child: optionsContent,
)

// List tile với padding responsive
ResponsiveListTile(
  leading: Icon(Icons.recipe),
  title: Text('Recipe Name'),
  onTap: () => openRecipe(),
)
```

**❌ Không dùng khi:**
- Cần custom styling phức tạp
- Widget có behavior đặc biệt

---

### 8. ResponsiveUtils

**📱 Khi nào dùng:**
- Cần tính toán responsive values phức tạp
- Muốn helper functions cho responsive logic
- Cần utilities cho custom widgets

**✅ Sử dụng khi:**
```dart
// Responsive value theo device type
final padding = context.valueByDevice(
  mobile: 16.0,
  tablet: 24.0,
  desktop: 32.0,
);

// Responsive value theo orientation
final height = context.valueByOrientation(
  portrait: 200.0,
  landscape: 150.0,
);

// Tính số cột grid tự động
final columns = context.gridColumns(
  itemMinWidth: 250,
  spacing: 16,
  padding: 32,
);

// Responsive border radius
Container(
  decoration: BoxDecoration(
    borderRadius: context.borderRadius(), // 8/12/16 theo device
  ),
)

// Kiểm tra navigation type
if (context.shouldShowSideNav) {
  return SideNavLayout();
}
```

**❌ Không dùng khi:**
- Có sẵn widget/class chuyên dụng
- Logic đơn giản (dùng extensions cơ bản)

---

## 🎯 Workflow Sử Dụng

### Bước 1: Import
```dart
import 'package:banachef/ui/responsive/responsive.dart';
```

### Bước 2: Chọn approach phù hợp

**Cho một page mới:**
1. Extend `ResponsiveBaseView` (xem integration_example.dart)
2. Hoặc dùng `ResponsiveLayoutBuilder` cho layout khác nhau

**Cho spacing:**
1. Dùng `context.pagePadding`, `context.contentPadding`
2. Dùng `VSpace`, `HSpace` cho spacing nhanh
3. Dùng `context.spacing()` cho custom

**Cho text:**
1. Dùng `context.headlineMedium`, `context.bodyLarge`
2. Hoặc `ResponsiveText.headlineMedium()`

**Cho grid/list:**
1. Dùng `ResponsiveGrid` cho grid layout
2. Dùng `ResponsiveWrap` cho tags/chips

**Cho navigation:**
1. Dùng `ResponsiveNavigation` cho main app
2. Dùng `ResponsiveAppBar` cho từng page

### Bước 3: Test trên nhiều kích thước
- Mobile: 360px, 414px
- Tablet: 768px, 1024px  
- Desktop: 1440px+

## 📝 Best Practices

1. **Luôn dùng responsive spacing thay vì hard-coded values**
2. **Thiết kế mobile-first, mở rộng lên tablet/desktop**
3. **Sử dụng extensions để code ngắn gọn hơn**
4. **Test trên nhiều kích thước màn hình**
5. **Kết hợp các class để đạt hiệu quả tối ưu**

## 🔍 Ví Dụ Thực Tế

Xem các file trong thư mục `examples/` để có ví dụ chi tiết về cách sử dụng từng thành phần.
