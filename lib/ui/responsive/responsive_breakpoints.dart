// lib/ui/responsive/responsive_breakpoints.dart

import 'package:flutter/material.dart';

/// <PERSON><PERSON><PERSON> nghĩa các breakpoint cho responsive design
/// Theo Material Design guidelines và best practices
class ResponsiveBreakpoints {
  ResponsiveBreakpoints._();

  // Breakpoints chính
  static const double mobile = 600;
  static const double tablet = 1024;
  static const double desktop = 1440;
  static const double largeDesktop = 1920;

  // Breakpoints phụ cho mobile
  static const double smallMobile = 360;
  static const double largeMobile = 480;

  // Breakpoints cho tablet
  static const double smallTablet = 768;
  static const double largeTablet = 1200;

  /// Kiểm tra xem có phải là mobile không
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobile;
  }

  /// Kiểm tra xem có phải là tablet không
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobile && width < desktop;
  }

  /// <PERSON><PERSON><PERSON> tra xem có phải là desktop không
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktop;
  }

  /// Kiểm tra xem có phải là small mobile không
  static bool isSmallMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < smallMobile;
  }

  /// Kiểm tra xem có phải là large mobile không
  static bool isLargeMobile(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= largeMobile && width < mobile;
  }

  /// Kiểm tra xem có phải là small tablet không
  static bool isSmallTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobile && width < smallTablet;
  }

  /// Kiểm tra xem có phải là large tablet không
  static bool isLargeTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= smallTablet && width < desktop;
  }

  /// Lấy device type hiện tại
  static DeviceType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < mobile) {
      return DeviceType.mobile;
    } else if (width < desktop) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }

  /// Lấy orientation hiện tại
  static DeviceOrientation getOrientation(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return size.width > size.height 
        ? DeviceOrientation.landscape 
        : DeviceOrientation.portrait;
  }

  /// Kiểm tra xem có phải landscape không
  static bool isLandscape(BuildContext context) {
    return getOrientation(context) == DeviceOrientation.landscape;
  }

  /// Kiểm tra xem có phải portrait không
  static bool isPortrait(BuildContext context) {
    return getOrientation(context) == DeviceOrientation.portrait;
  }
}

/// Enum định nghĩa các loại device
enum DeviceType {
  mobile,
  tablet,
  desktop,
}

/// Enum định nghĩa orientation
enum DeviceOrientation {
  portrait,
  landscape,
}

/// Extension để dễ dàng sử dụng với MediaQuery
extension ResponsiveExtension on BuildContext {
  /// Lấy device type
  DeviceType get deviceType => ResponsiveBreakpoints.getDeviceType(this);
  
  /// Lấy orientation
  DeviceOrientation get deviceOrientation => ResponsiveBreakpoints.getOrientation(this);
  
  /// Kiểm tra mobile
  bool get isMobile => ResponsiveBreakpoints.isMobile(this);
  
  /// Kiểm tra tablet
  bool get isTablet => ResponsiveBreakpoints.isTablet(this);
  
  /// Kiểm tra desktop
  bool get isDesktop => ResponsiveBreakpoints.isDesktop(this);
  
  /// Kiểm tra landscape
  bool get isLandscape => ResponsiveBreakpoints.isLandscape(this);
  
  /// Kiểm tra portrait
  bool get isPortrait => ResponsiveBreakpoints.isPortrait(this);
  
  /// Lấy screen width
  double get screenWidth => MediaQuery.of(this).size.width;
  
  /// Lấy screen height
  double get screenHeight => MediaQuery.of(this).size.height;
  
  /// Lấy safe area padding
  EdgeInsets get safeAreaPadding => MediaQuery.of(this).padding;
}
