// lib/ui/responsive/responsive_grid.dart

import 'package:flutter/material.dart';
import 'responsive_breakpoints.dart';
import 'responsive_spacing.dart';

/// Hệ thống grid responsive
/// Tự động điều chỉnh số cột dựa trên kích thư<PERSON><PERSON> màn hình
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final int? mobileColumns;
  final int? tabletColumns;
  final int? desktopColumns;
  final double? spacing;
  final double? runSpacing;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final EdgeInsetsGeometry? padding;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.mobileColumns,
    this.tabletColumns,
    this.desktopColumns,
    this.spacing,
    this.runSpacing,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.start,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = context.deviceType;
    
    // Xác định số cột
    int columns;
    switch (deviceType) {
      case DeviceType.mobile:
        columns = mobileColumns ?? 1;
        break;
      case DeviceType.tablet:
        columns = tabletColumns ?? mobileColumns ?? 2;
        break;
      case DeviceType.desktop:
        columns = desktopColumns ?? tabletColumns ?? mobileColumns ?? 3;
        break;
    }

    final effectiveSpacing = spacing ?? context.itemSpacing;
    final effectiveRunSpacing = runSpacing ?? context.itemSpacing;

    Widget grid = _buildGrid(
      context,
      columns,
      effectiveSpacing,
      effectiveRunSpacing,
    );

    if (padding != null) {
      grid = Padding(padding: padding!, child: grid);
    }

    return grid;
  }

  Widget _buildGrid(
    BuildContext context,
    int columns,
    double spacing,
    double runSpacing,
  ) {
    if (children.isEmpty) return const SizedBox.shrink();

    final rows = <Widget>[];
    
    for (int i = 0; i < children.length; i += columns) {
      final rowChildren = <Widget>[];
      
      for (int j = 0; j < columns; j++) {
        if (i + j < children.length) {
          rowChildren.add(Expanded(child: children[i + j]));
        } else {
          rowChildren.add(const Expanded(child: SizedBox.shrink()));
        }
        
        // Add spacing between columns (except for last column)
        if (j < columns - 1) {
          rowChildren.add(SizedBox(width: spacing));
        }
      }
      
      rows.add(
        Row(
          mainAxisAlignment: mainAxisAlignment,
          crossAxisAlignment: crossAxisAlignment,
          children: rowChildren,
        ),
      );
      
      // Add spacing between rows (except for last row)
      if (i + columns < children.length) {
        rows.add(SizedBox(height: runSpacing));
      }
    }

    return Column(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: rows,
    );
  }
}

/// Staggered Grid responsive
class ResponsiveStaggeredGrid extends StatelessWidget {
  final List<Widget> children;
  final int? mobileColumns;
  final int? tabletColumns;
  final int? desktopColumns;
  final double? spacing;
  final double? runSpacing;
  final EdgeInsetsGeometry? padding;

  const ResponsiveStaggeredGrid({
    super.key,
    required this.children,
    this.mobileColumns,
    this.tabletColumns,
    this.desktopColumns,
    this.spacing,
    this.runSpacing,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = context.deviceType;
    
    // Xác định số cột
    int columns;
    switch (deviceType) {
      case DeviceType.mobile:
        columns = mobileColumns ?? 2;
        break;
      case DeviceType.tablet:
        columns = tabletColumns ?? mobileColumns ?? 3;
        break;
      case DeviceType.desktop:
        columns = desktopColumns ?? tabletColumns ?? mobileColumns ?? 4;
        break;
    }

    final effectiveSpacing = spacing ?? context.itemSpacing;
    final effectiveRunSpacing = runSpacing ?? context.itemSpacing;

    Widget grid = _buildStaggeredGrid(
      context,
      columns,
      effectiveSpacing,
      effectiveRunSpacing,
    );

    if (padding != null) {
      grid = Padding(padding: padding!, child: grid);
    }

    return grid;
  }

  Widget _buildStaggeredGrid(
    BuildContext context,
    int columns,
    double spacing,
    double runSpacing,
  ) {
    if (children.isEmpty) return const SizedBox.shrink();

    // Tạo các cột
    final columnChildren = List.generate(columns, (index) => <Widget>[]);
    
    // Phân phối children vào các cột
    for (int i = 0; i < children.length; i++) {
      final columnIndex = i % columns;
      columnChildren[columnIndex].add(children[i]);
    }

    // Tạo các cột widget
    final columnWidgets = <Widget>[];
    for (int i = 0; i < columns; i++) {
      columnWidgets.add(
        Expanded(
          child: Column(
            children: columnChildren[i]
                .map((child) => Padding(
                      padding: EdgeInsets.only(bottom: runSpacing),
                      child: child,
                    ))
                .toList(),
          ),
        ),
      );
      
      // Add spacing between columns (except for last column)
      if (i < columns - 1) {
        columnWidgets.add(SizedBox(width: spacing));
      }
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: columnWidgets,
    );
  }
}

/// Grid item với responsive sizing
class ResponsiveGridItem extends StatelessWidget {
  final Widget child;
  final int? mobileSpan;
  final int? tabletSpan;
  final int? desktopSpan;
  final double? aspectRatio;

  const ResponsiveGridItem({
    super.key,
    required this.child,
    this.mobileSpan,
    this.tabletSpan,
    this.desktopSpan,
    this.aspectRatio,
  });

  @override
  Widget build(BuildContext context) {
    Widget content = child;

    if (aspectRatio != null) {
      content = AspectRatio(
        aspectRatio: aspectRatio!,
        child: content,
      );
    }

    return content;
  }

  /// Lấy span cho device hiện tại
  int getSpan(BuildContext context, int totalColumns) {
    final deviceType = context.deviceType;
    
    int span;
    switch (deviceType) {
      case DeviceType.mobile:
        span = mobileSpan ?? 1;
        break;
      case DeviceType.tablet:
        span = tabletSpan ?? mobileSpan ?? 1;
        break;
      case DeviceType.desktop:
        span = desktopSpan ?? tabletSpan ?? mobileSpan ?? 1;
        break;
    }

    return span.clamp(1, totalColumns);
  }
}

/// Responsive Wrap widget
class ResponsiveWrap extends StatelessWidget {
  final List<Widget> children;
  final Axis direction;
  final WrapAlignment alignment;
  final double? spacing;
  final WrapAlignment runAlignment;
  final double? runSpacing;
  final WrapCrossAlignment crossAxisAlignment;
  final TextDirection? textDirection;
  final VerticalDirection verticalDirection;
  final Clip clipBehavior;

  const ResponsiveWrap({
    super.key,
    required this.children,
    this.direction = Axis.horizontal,
    this.alignment = WrapAlignment.start,
    this.spacing,
    this.runAlignment = WrapAlignment.start,
    this.runSpacing,
    this.crossAxisAlignment = WrapCrossAlignment.start,
    this.textDirection,
    this.verticalDirection = VerticalDirection.down,
    this.clipBehavior = Clip.none,
  });

  @override
  Widget build(BuildContext context) {
    return Wrap(
      direction: direction,
      alignment: alignment,
      spacing: spacing ?? context.itemSpacing,
      runAlignment: runAlignment,
      runSpacing: runSpacing ?? context.itemSpacing,
      crossAxisAlignment: crossAxisAlignment,
      textDirection: textDirection,
      verticalDirection: verticalDirection,
      clipBehavior: clipBehavior,
      children: children,
    );
  }
}

/// Utility class cho responsive grid configurations
class ResponsiveGridConfig {
  final int mobile;
  final int tablet;
  final int desktop;

  const ResponsiveGridConfig({
    required this.mobile,
    required this.tablet,
    required this.desktop,
  });

  // Predefined configurations
  static const ResponsiveGridConfig cards = ResponsiveGridConfig(
    mobile: 1,
    tablet: 2,
    desktop: 3,
  );

  static const ResponsiveGridConfig items = ResponsiveGridConfig(
    mobile: 2,
    tablet: 3,
    desktop: 4,
  );

  static const ResponsiveGridConfig gallery = ResponsiveGridConfig(
    mobile: 2,
    tablet: 4,
    desktop: 6,
  );

  static const ResponsiveGridConfig list = ResponsiveGridConfig(
    mobile: 1,
    tablet: 1,
    desktop: 2,
  );

  /// Lấy số cột cho device hiện tại
  int getColumns(BuildContext context) {
    switch (context.deviceType) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet;
      case DeviceType.desktop:
        return desktop;
    }
  }
}
