// lib/ui/responsive/responsive_layout_builder.dart

import 'package:flutter/material.dart';
import 'responsive_breakpoints.dart';

/// Widget builder cho responsive layout
/// Tự động chọn layout phù hợp dựa trên kích thước màn hình
class ResponsiveLayoutBuilder extends StatelessWidget {
  /// Layout cho mobile
  final Widget mobile;
  
  /// Layout cho tablet (optional)
  final Widget? tablet;
  
  /// Layout cho desktop (optional)
  final Widget? desktop;
  
  /// Layout cho mobile landscape (optional)
  final Widget? mobileLandscape;
  
  /// Layout cho tablet landscape (optional)
  final Widget? tabletLandscape;

  const ResponsiveLayoutBuilder({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.mobileLandscape,
    this.tabletLandscape,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = context.deviceType;
    final isLandscape = context.isLandscape;

    // Xử lý landscape layouts
    if (isLandscape) {
      switch (deviceType) {
        case DeviceType.mobile:
          if (mobileLandscape != null) return mobileLandscape!;
          break;
        case DeviceType.tablet:
          if (tabletLandscape != null) return tabletLandscape!;
          break;
        case DeviceType.desktop:
          // Desktop thường không cần landscape riêng
          break;
      }
    }

    // Xử lý portrait/default layouts
    switch (deviceType) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet ?? mobile;
      case DeviceType.desktop:
        return desktop ?? tablet ?? mobile;
    }
  }
}

/// Widget builder với callback functions
class ResponsiveBuilder extends StatelessWidget {
  /// Builder function nhận context và device info
  final Widget Function(
    BuildContext context,
    DeviceType deviceType,
    DeviceOrientation orientation,
  ) builder;

  const ResponsiveBuilder({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return builder(
      context,
      context.deviceType,
      context.deviceOrientation,
    );
  }
}

/// Widget để wrap content với responsive constraints
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final double? maxWidth;
  final double? maxWidthMobile;
  final double? maxWidthTablet;
  final double? maxWidthDesktop;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? paddingMobile;
  final EdgeInsetsGeometry? paddingTablet;
  final EdgeInsetsGeometry? paddingDesktop;
  final bool center;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.maxWidth,
    this.maxWidthMobile,
    this.maxWidthTablet,
    this.maxWidthDesktop,
    this.padding,
    this.paddingMobile,
    this.paddingTablet,
    this.paddingDesktop,
    this.center = true,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = context.deviceType;
    
    // Xác định max width
    double? effectiveMaxWidth;
    switch (deviceType) {
      case DeviceType.mobile:
        effectiveMaxWidth = maxWidthMobile ?? maxWidth;
        break;
      case DeviceType.tablet:
        effectiveMaxWidth = maxWidthTablet ?? maxWidth;
        break;
      case DeviceType.desktop:
        effectiveMaxWidth = maxWidthDesktop ?? maxWidth;
        break;
    }

    // Xác định padding
    EdgeInsetsGeometry? effectivePadding;
    switch (deviceType) {
      case DeviceType.mobile:
        effectivePadding = paddingMobile ?? padding;
        break;
      case DeviceType.tablet:
        effectivePadding = paddingTablet ?? padding;
        break;
      case DeviceType.desktop:
        effectivePadding = paddingDesktop ?? padding;
        break;
    }

    Widget content = child;

    // Apply padding
    if (effectivePadding != null) {
      content = Padding(
        padding: effectivePadding,
        child: content,
      );
    }

    // Apply max width constraint
    if (effectiveMaxWidth != null) {
      content = ConstrainedBox(
        constraints: BoxConstraints(maxWidth: effectiveMaxWidth),
        child: content,
      );
    }

    // Center if needed
    if (center && effectiveMaxWidth != null) {
      content = Center(child: content);
    }

    return content;
  }
}

/// Utility class để tạo responsive values
class ResponsiveValue<T> {
  final T mobile;
  final T? tablet;
  final T? desktop;
  final T? mobileLandscape;
  final T? tabletLandscape;

  const ResponsiveValue({
    required this.mobile,
    this.tablet,
    this.desktop,
    this.mobileLandscape,
    this.tabletLandscape,
  });

  /// Lấy value phù hợp với device hiện tại
  T getValue(BuildContext context) {
    final deviceType = context.deviceType;
    final isLandscape = context.isLandscape;

    // Check landscape values first
    if (isLandscape) {
      switch (deviceType) {
        case DeviceType.mobile:
          if (mobileLandscape != null) return mobileLandscape!;
          break;
        case DeviceType.tablet:
          if (tabletLandscape != null) return tabletLandscape!;
          break;
        case DeviceType.desktop:
          break;
      }
    }

    // Fallback to portrait/default values
    switch (deviceType) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet ?? mobile;
      case DeviceType.desktop:
        return desktop ?? tablet ?? mobile;
    }
  }
}

/// Extension để dễ dàng sử dụng ResponsiveValue
extension ResponsiveValueExtension on BuildContext {
  T responsive<T>(ResponsiveValue<T> value) {
    return value.getValue(this);
  }
}
