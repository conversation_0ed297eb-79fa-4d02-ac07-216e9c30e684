# 🎨 BanaChef Responsive Design System

Hệ thống thiết kế responsive hoàn chỉnh cho ứng dụng BanaChef, hỗ trợ mobile, tablet và desktop với kiến trúc clean và SOLID principles.

## 🚀 Tính Năng Chính

- ✅ **Breakpoints thông minh**: Tự động phát hiện loại thiết bị và orientation
- ✅ **Layout responsive**: Tự động điều chỉnh layout cho từng loại màn hình
- ✅ **Spacing system**: Hệ thống khoảng cách tự động scale theo device
- ✅ **Typography responsive**: Font size tự động điều chỉnh theo Material Design
- ✅ **Grid system**: Grid linh hoạt với số cột tự động, hỗ trợ staggered layout
- ✅ **Navigation adaptive**: Bottom nav cho mobile, side nav cho tablet/desktop
- ✅ **Widgets responsive**: Các widget UI tự động thích ứng (Card, Button, Dialog, etc.)
- ✅ **Utils & Extensions**: Utilities và extensions để sử dụng dễ dàng
- ✅ **Clean Architecture**: Tuân thủ clean architecture và SOLID principles
- ✅ **TypeScript-like**: Strongly typed với enums và proper abstractions

## 📁 Cấu Trúc Thư Mục

```
lib/ui/responsive/
├── 📄 responsive.dart                 # 🎯 Main export file - Import này để dùng toàn bộ system
├── 📄 responsive_breakpoints.dart     # 📱 Device detection & breakpoints
├── 📄 responsive_layout_builder.dart  # 🏗️ Layout builders & containers
├── 📄 responsive_spacing.dart         # 📏 Spacing system
├── 📄 responsive_typography.dart      # 🔤 Typography system
├── 📄 responsive_grid.dart           # 🎯 Grid system
├── 📄 responsive_navigation.dart     # 🧭 Navigation components
├── 📄 responsive_widgets.dart        # 🧩 Common responsive widgets
├── 📄 responsive_utils.dart          # 🛠️ Utilities & helper functions
├── 📄 README.md                      # 📖 Documentation chính
├── 📄 USAGE_GUIDE.md                 # 📋 Hướng dẫn chi tiết khi nào dùng class nào
└── 📁 examples/                      # 💡 Usage examples
    ├── responsive_page_example.dart   # Ví dụ cơ bản
    └── integration_example.dart       # Ví dụ tích hợp với app hiện tại
```

## 🎯 8 Thành Phần Chính & Khi Nào Sử Dụng

| Thành Phần | Mục Đích | Khi Nào Dùng |
|------------|----------|---------------|
| **ResponsiveBreakpoints** | Phát hiện device type | Cần logic khác nhau cho mobile/tablet/desktop |
| **ResponsiveLayoutBuilder** | Layout khác nhau theo device | Cần UI hoàn toàn khác nhau cho từng màn hình |
| **ResponsiveSpacing** | Spacing tự động scale | Cần padding/margin nhất quán trong app |
| **ResponsiveTypography** | Text size tự động điều chỉnh | Cần typography responsive theo Material Design |
| **ResponsiveGrid** | Grid với số cột tự động | Hiển thị danh sách items dạng grid |
| **ResponsiveNavigation** | Navigation thích ứng | App có navigation chính (bottom/side nav) |
| **ResponsiveWidgets** | UI components responsive | Cần Card, Button, Dialog tự động thích ứng |
| **ResponsiveUtils** | Helper functions | Cần tính toán responsive values phức tạp |

> 📖 **Chi tiết:** Xem file `USAGE_GUIDE.md` để biết khi nào dùng từng class cụ thể

## 📐 Breakpoints & Device Support

| Device | Width Range | Typical Columns | Navigation | Spacing Scale |
|--------|-------------|-----------------|------------|---------------|
| 📱 **Mobile** | < 600px | 1-2 | Bottom Nav | 1.0x |
| 📟 **Tablet** | 600px - 1024px | 2-3 | Side Nav | 1.25x |
| 🖥️ **Desktop** | > 1024px | 3-4+ | Side Nav | 1.5x |

### Breakpoints Chi Tiết
- **Small Mobile**: < 360px
- **Large Mobile**: 480px - 600px
- **Small Tablet**: 600px - 768px
- **Large Tablet**: 768px - 1024px
- **Desktop**: 1024px - 1440px
- **Large Desktop**: > 1440px

## 🚀 Quick Start

### 1. Import Toàn Bộ System

```dart
import 'package:banachef/ui/responsive/responsive.dart';
```

### 2. Sử Dụng Ngay

```dart
class MyResponsivePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ResponsiveAppBar(title: 'My Page'),
      body: ResponsiveContainer(
        padding: context.pagePadding,
        child: Column(
          children: [
            ResponsiveText.headlineMedium('Welcome!'),
            VSpace.lg(),
            ResponsiveGrid(
              mobileColumns: 1,
              tabletColumns: 2,
              desktopColumns: 3,
              children: items.map((item) => ResponsiveCard(
                child: Text(item.name),
              )).toList(),
            ),
          ],
        ),
      ),
    );
  }
}
```

## 📖 Hướng Dẫn Chi Tiết

### 🔍 1. Device Detection (ResponsiveBreakpoints)

**Khi nào dùng:** Cần logic khác nhau cho mobile/tablet/desktop

```dart
// ✅ Kiểm tra device type
if (context.isMobile) {
  return SingleColumnLayout();
} else if (context.isTablet) {
  return TwoColumnLayout();
} else {
  return ThreeColumnLayout();
}

// ✅ Kiểm tra orientation
if (context.isLandscape) {
  return HorizontalLayout();
}

// ✅ Lấy thông tin device
final deviceType = context.deviceType;        // DeviceType.mobile/tablet/desktop
final screenWidth = context.screenWidth;      // double
final screenHeight = context.screenHeight;    // double
final isLandscape = context.isLandscape;      // bool
```

### 🏗️ 2. Responsive Layout (ResponsiveLayoutBuilder)

**Khi nào dùng:** Cần layout hoàn toàn khác nhau cho từng device

```dart
// ✅ Layout builder - tự động chọn layout phù hợp
ResponsiveLayoutBuilder(
  mobile: MobileHomePage(),           // Required
  tablet: TabletHomePage(),           // Optional, fallback to mobile
  desktop: DesktopHomePage(),         // Optional, fallback to tablet
  mobileLandscape: MobileLandscape(), // Optional
)

// ✅ Container với max width responsive
ResponsiveContainer(
  maxWidthMobile: double.infinity,    // Full width trên mobile
  maxWidthTablet: 800,               // Max 800px trên tablet
  maxWidthDesktop: 1200,             // Max 1200px trên desktop
  padding: context.pagePadding,       // Auto padding theo device
  center: true,                       // Center content
  child: content,
)

// ✅ Responsive values
final columns = context.responsive(ResponsiveValue(
  mobile: 1,
  tablet: 2,
  desktop: 3,
));
```

### 📏 3. Responsive Spacing (ResponsiveSpacing)

**Khi nào dùng:** Cần padding/margin tự động điều chỉnh theo device

```dart
// ✅ Predefined spacing - tự động scale theo device
Padding(
  padding: context.pagePadding,     // 16px mobile, 24px tablet, 32px desktop
  child: Column(
    children: [
      VSpace.lg(),                  // Vertical spacing responsive
      Text('Content'),
      VSpace.md(),                  // Medium vertical space
      Row(
        children: [
          Text('Left'),
          HSpace.sm(),              // Horizontal spacing responsive
          Text('Right'),
        ],
      ),
    ],
  ),
)

// ✅ Spacing types
context.pagePadding        // Page padding: 16/24/32
context.contentPadding     // Content padding: 16x12/24x16/32x20
context.cardPadding        // Card padding: 16/20/24
context.sectionSpacing     // Section spacing: 24/32/40
context.itemSpacing        // Item spacing: 12/16/20

// ✅ Custom spacing
EdgeInsets.all(context.spacing(2))           // 2 * base unit, scaled
context.paddingSymmetric(horizontal: 2)      // Horizontal padding
context.paddingOnly(top: 1, bottom: 2)      // Specific sides

// ✅ Quick spacing widgets
VSpace.xs()  VSpace.sm()  VSpace.md()  VSpace.lg()  VSpace.xl()
HSpace.xs()  HSpace.sm()  HSpace.md()  HSpace.lg()  HSpace.xl()
```

### 🔤 4. Responsive Typography (ResponsiveTypography)

**Khi nào dùng:** Cần text size tự động điều chỉnh theo device

```dart
// ✅ Typography extensions - tự động scale theo device
Column(
  children: [
    Text('Page Title', style: context.displayLarge),      // 32/35.2/38.4px
    Text('Section Title', style: context.headlineMedium), // 20/22/24px
    Text('Body Text', style: context.bodyLarge),          // 16/17.6/19.2px
    Text('Caption', style: context.bodySmall),            // 12/13.2/14.4px
  ],
)

// ✅ ResponsiveText widgets - cleaner syntax
Column(
  children: [
    ResponsiveText.displayLarge('Page Title'),
    ResponsiveText.headlineMedium('Section Title'),
    ResponsiveText.bodyLarge('Body content here'),
    ResponsiveText.bodySmall('Small text'),
  ],
)

// ✅ All typography styles available
context.displayLarge     context.displayMedium    context.displaySmall
context.headlineLarge    context.headlineMedium   context.headlineSmall
context.titleLarge       context.titleMedium      context.titleSmall
context.bodyLarge        context.bodyMedium       context.bodySmall
context.labelLarge       context.labelMedium      context.labelSmall
context.buttonLarge      context.buttonMedium     context.buttonSmall
```

### 🎯 5. Responsive Grid (ResponsiveGrid)

**Khi nào dùng:** Hiển thị danh sách items dạng grid

```dart
// ✅ Basic responsive grid - tự động điều chỉnh số cột
ResponsiveGrid(
  mobileColumns: 1,        // 1 cột trên mobile
  tabletColumns: 2,        // 2 cột trên tablet
  desktopColumns: 3,       // 3 cột trên desktop
  spacing: context.itemSpacing,      // Spacing giữa items
  runSpacing: context.itemSpacing,   // Spacing giữa rows
  children: recipes.map((recipe) => RecipeCard(recipe)).toList(),
)

// ✅ Staggered grid - cho items có height khác nhau
ResponsiveStaggeredGrid(
  mobileColumns: 2,
  tabletColumns: 3,
  desktopColumns: 4,
  children: photos.map((photo) => PhotoCard(photo)).toList(),
)

// ✅ Responsive wrap - cho tags, chips
ResponsiveWrap(
  children: tags.map((tag) => Chip(label: Text(tag))).toList(),
)

// ✅ Auto calculate columns dựa trên item width
final columns = context.gridColumns(
  itemMinWidth: 250,       // Min width mỗi item
  spacing: 16,             // Spacing giữa items
  padding: 32,             // Page padding
);
```

### 🧭 6. Responsive Navigation (ResponsiveNavigation)

**Khi nào dùng:** App có navigation chính

```dart
// ✅ Main app navigation - tự động chuyển bottom/side nav
ResponsiveNavigation(
  items: [
    ResponsiveNavigationItem(
      icon: Icons.home_outlined,
      activeIcon: Icons.home,
      label: 'Trang chủ',
    ),
    ResponsiveNavigationItem(
      icon: Icons.search_outlined,
      activeIcon: Icons.search,
      label: 'Khám phá',
    ),
    // ... more items
  ],
  currentIndex: currentIndex,
  onTap: (index) => setState(() => currentIndex = index),
  body: pages[currentIndex],
)

// ✅ Responsive app bar
ResponsiveAppBar(
  title: 'Page Title',
  actions: [
    IconButton(icon: Icon(Icons.search), onPressed: () {}),
    if (context.isTablet || context.isDesktop)
      IconButton(icon: Icon(Icons.filter_list), onPressed: () {}),
  ],
)

// ✅ Side navigation riêng (cho custom layout)
ResponsiveSideNavigation(
  items: navigationItems,
  currentIndex: currentIndex,
  onTap: (index) => onNavigate(index),
  expanded: true,  // Có thể collapse trên desktop
)
```

### 🧩 7. Responsive Widgets (ResponsiveWidgets)

**Khi nào dùng:** Cần UI components cơ bản responsive

```dart
// ✅ Responsive card - padding tự động điều chỉnh
ResponsiveCard(
  onTap: () => navigateToDetail(),
  child: Column([
    Image.network(recipe.imageUrl),
    VSpace.sm(),
    ResponsiveText.titleMedium(recipe.name),
  ]),
)

// ✅ Responsive buttons - size tự động điều chỉnh
ResponsiveButton.large(
  text: 'Thêm công thức',
  onPressed: () => addRecipe(),
  icon: Icon(Icons.add),
  isLoading: isLoading,
)

ResponsiveButton.small(
  text: 'Hủy',
  onPressed: () => Navigator.pop(context),
)

// ✅ Responsive dialog - tự động thành bottom sheet trên mobile
ResponsiveBottomSheet.show(
  context: context,
  title: 'Tùy chọn',
  child: Column([
    ListTile(title: Text('Chỉnh sửa'), onTap: () => edit()),
    ListTile(title: Text('Xóa'), onTap: () => delete()),
  ]),
)

// ✅ Responsive list tile
ResponsiveListTile(
  leading: CircleAvatar(child: Icon(Icons.person)),
  title: Text('User Name'),
  subtitle: Text('<EMAIL>'),
  trailing: Icon(Icons.arrow_forward_ios),
  onTap: () => viewProfile(),
)
```

### 🛠️ 8. Responsive Utils (ResponsiveUtils)

**Khi nào dùng:** Cần tính toán responsive values phức tạp

```dart
// ✅ Responsive value theo device type
final padding = context.valueByDevice(
  mobile: 16.0,
  tablet: 24.0,
  desktop: 32.0,
);

// ✅ Responsive value theo orientation
final height = context.valueByOrientation(
  portrait: 200.0,
  landscape: 150.0,
);

// ✅ Complex responsive logic
final layout = ResponsiveUtils.valueByDeviceAndOrientation(
  context,
  mobilePortrait: MobilePortraitLayout(),
  mobileLandscape: MobileLandscapeLayout(),
  tabletPortrait: TabletPortraitLayout(),
  tabletLandscape: TabletLandscapeLayout(),
);

// ✅ Responsive styling helpers
Container(
  decoration: BoxDecoration(
    borderRadius: context.borderRadius(),  // 8/12/16 theo device
  ),
  child: content,
)

final elevation = context.elevation();  // 2/4/8 theo device

// ✅ Navigation helpers
if (context.shouldShowSideNav) {
  return SideNavLayout();
} else {
  return BottomNavLayout();
}
```

### 6. Responsive Grid

```dart
// Grid tự động điều chỉnh số cột
ResponsiveGrid(
  mobileColumns: 1,
  tabletColumns: 2,
  desktopColumns: 3,
  spacing: context.itemSpacing,
  children: items.map((item) => ItemCard(item)).toList(),
)

// Staggered grid cho layout không đều
ResponsiveStaggeredGrid(
  mobileColumns: 2,
  tabletColumns: 3,
  desktopColumns: 4,
  children: items,
)

// Wrap responsive
ResponsiveWrap(
  children: chips,
)
```

### 7. Responsive Navigation

```dart
// Navigation tự động chuyển đổi
ResponsiveNavigation(
  items: [
    ResponsiveNavigationItem(
      icon: Icons.home_outlined,
      activeIcon: Icons.home,
      label: 'Home',
    ),
    // ... more items
  ],
  currentIndex: currentIndex,
  onTap: (index) => setState(() => currentIndex = index),
  body: pages[currentIndex],
)
```

### 8. Responsive Widgets

```dart
// Card responsive
ResponsiveCard(
  child: content,
  onTap: () {},
)

// Button responsive
ResponsiveButton.large(
  text: 'Action',
  onPressed: () {},
  icon: Icon(Icons.add),
)

// Dialog responsive (tự động chuyển thành bottom sheet trên mobile)
ResponsiveBottomSheet.show(
  context: context,
  title: 'Options',
  child: content,
)
```

## 🎯 Các Use Cases Phổ Biến

### 📱 Recipe Discovery Page
```dart
ResponsiveLayoutBuilder(
  mobile: SingleColumnRecipeList(),
  tablet: Row([
    SidebarFilters(width: 280),
    Expanded(child: TwoColumnRecipeGrid()),
  ]),
  desktop: Row([
    SidebarFilters(width: 320),
    Expanded(child: ThreeColumnRecipeGrid()),
  ]),
)
```

### 🍽️ Recipe Detail Page
```dart
ResponsiveContainer(
  maxWidthTablet: 800,
  maxWidthDesktop: 1000,
  padding: context.pagePadding,
  child: Column([
    ResponsiveText.displayMedium(recipe.name),
    VSpace.lg(),
    ResponsiveGrid(
      mobileColumns: 1,
      tabletColumns: 2,
      children: [RecipeImage(), RecipeInfo()],
    ),
  ]),
)
```

### 🧭 Main App Navigation
```dart
ResponsiveNavigation(
  items: [
    ResponsiveNavigationItem(icon: Icons.home, label: 'Trang chủ'),
    ResponsiveNavigationItem(icon: Icons.search, label: 'Khám phá'),
    ResponsiveNavigationItem(icon: Icons.calendar_today, label: 'Kế hoạch'),
    ResponsiveNavigationItem(icon: Icons.person, label: 'Cá nhân'),
  ],
  currentIndex: currentIndex,
  onTap: (index) => setState(() => currentIndex = index),
  body: pages[currentIndex],
)
```

## ✅ Best Practices

### 1. **Luôn dùng responsive spacing**
```dart
// ✅ Good - Tự động scale theo device
Padding(padding: context.pagePadding)

// ❌ Avoid - Hard-coded values
Padding(padding: EdgeInsets.all(16))
```

### 2. **Sử dụng responsive typography**
```dart
// ✅ Good - Auto scale + consistent
Text('Title', style: context.headlineMedium)

// ❌ Avoid - Fixed size
Text('Title', style: TextStyle(fontSize: 20))
```

### 3. **Thiết kế mobile-first**
```dart
// ✅ Good - Mobile base, tablet/desktop enhancement
ResponsiveLayoutBuilder(
  mobile: MobileLayout(),      // Required base
  tablet: TabletLayout(),      // Optional enhancement
  desktop: DesktopLayout(),    // Optional enhancement
)
```

### 4. **Giới hạn width trên màn hình lớn**
```dart
// ✅ Good - Prevent content stretching
ResponsiveContainer(
  maxWidthDesktop: 1200,
  child: content,
)
```

### 5. **Test trên nhiều breakpoints**
- **Mobile**: 360px, 414px, 480px
- **Tablet**: 768px, 1024px
- **Desktop**: 1440px, 1920px+

## 🔧 Tùy Chỉnh System

Bạn có thể tùy chỉnh các giá trị trong từng file:

| File | Tùy chỉnh | Ví dụ |
|------|-----------|-------|
| `responsive_breakpoints.dart` | Breakpoints | `mobile = 600` → `mobile = 640` |
| `responsive_spacing.dart` | Base spacing, multipliers | `_baseUnit = 8.0` → `_baseUnit = 4.0` |
| `responsive_typography.dart` | Font families, scale factors | `_mobileScale = 1.0` → `_mobileScale = 0.9` |
| `responsive_grid.dart` | Default columns | `mobileColumns ?? 1` → `mobileColumns ?? 2` |

## 📚 Tài Liệu & Ví Dụ

| File | Mô tả | Nội dung |
|------|-------|----------|
| 📖 `README.md` | Documentation chính | Tổng quan, quick start, best practices |
| 📋 `USAGE_GUIDE.md` | **Hướng dẫn chi tiết** | **Khi nào dùng từng class cụ thể** |
| 💡 `examples/responsive_page_example.dart` | Ví dụ cơ bản | ResponsiveLayoutBuilder, Grid, Navigation |
| 🔗 `examples/integration_example.dart` | Ví dụ tích hợp | ResponsiveBaseView, real-world usage |

## 🚀 Workflow Khuyến Nghị

### Bước 1: Phân tích requirements
- Page này cần layout khác nhau không? → `ResponsiveLayoutBuilder`
- Cần grid/list responsive? → `ResponsiveGrid`
- Cần navigation? → `ResponsiveNavigation`

### Bước 2: Implement
```dart
// 1. Import
import 'package:banachef/ui/responsive/responsive.dart';

// 2. Sử dụng base structure
class MyPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ResponsiveAppBar(title: 'My Page'),
      body: ResponsiveContainer(
        padding: context.pagePadding,
        child: _buildContent(context),
      ),
    );
  }
}
```

### Bước 3: Test & refine
- Test trên Flutter Inspector với different screen sizes
- Adjust breakpoints nếu cần
- Fine-tune spacing và typography

## 🎯 Tóm Tắt

✅ **Import một lần**: `import 'package:banachef/ui/responsive/responsive.dart';`

✅ **8 thành phần chính**: Breakpoints, Layout, Spacing, Typography, Grid, Navigation, Widgets, Utils

✅ **Extensions tiện lợi**: `context.isMobile`, `context.pagePadding`, `context.headlineMedium`

✅ **Mobile-first**: Thiết kế cho mobile trước, mở rộng lên tablet/desktop

✅ **Consistent**: Spacing và typography nhất quán trong toàn app

✅ **Flexible**: Dễ tùy chỉnh và mở rộng

✅ **Clean Architecture**: Tuân thủ SOLID principles và clean code

---

> 📖 **Đọc tiếp**: `USAGE_GUIDE.md` để biết chi tiết khi nào dùng từng class
>
> 💡 **Ví dụ**: Xem folder `examples/` để có code mẫu chi tiết
