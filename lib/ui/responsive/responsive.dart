// lib/ui/responsive/responsive.dart

/// Responsive Design System for BanaChef App
///
/// <PERSON><PERSON> thống thiết kế responsive hoàn chỉnh bao gồm:
/// - Breakpoints và device detection
/// - Layout builders và containers
/// - Spacing system
/// - Typography system
/// - Grid system
/// - Navigation components
/// - Common widgets
///
/// S<PERSON> dụng:
/// ```dart
/// import 'package:banachef/ui/responsive/responsive.dart';
///
/// // Kiểm tra device type
/// if (context.isMobile) {
///   // Mobile layout
/// } else if (context.isTablet) {
///   // Tablet layout
/// }
///
/// // Responsive spacing
/// Padding(
///   padding: context.pagePadding,
///   child: Column(
///     children: [
///       VSpace.md(),
///       ResponsiveText.headlineMedium('Title'),
///       VSpace.sm(),
///       // Content
///     ],
///   ),
/// )
///
/// // Responsive grid
/// ResponsiveGrid(
///   mobileColumns: 1,
///   tabletColumns: 2,
///   desktopColumns: 3,
///   children: items,
/// )
/// ```

// Core responsive system
export 'responsive_breakpoints.dart';
export 'responsive_layout_builder.dart';

// Spacing and typography
export 'responsive_spacing.dart';
export 'responsive_typography.dart';

// Layout components
export 'responsive_grid.dart';
export 'responsive_navigation.dart';

// Common widgets
export 'responsive_widgets.dart';

// Utilities
export 'responsive_utils.dart';
