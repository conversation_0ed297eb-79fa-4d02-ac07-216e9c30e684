// lib/ui/responsive/responsive_utils.dart

import 'package:flutter/material.dart';
import 'responsive_breakpoints.dart';

/// Utility class cho responsive design
class ResponsiveUtils {
  ResponsiveUtils._();

  /// Tính toán responsive value dựa trên screen width
  static T valueByScreenWidth<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    final width = MediaQuery.of(context).size.width;
    
    if (width >= ResponsiveBreakpoints.desktop) {
      return desktop ?? tablet ?? mobile;
    } else if (width >= ResponsiveBreakpoints.mobile) {
      return tablet ?? mobile;
    } else {
      return mobile;
    }
  }

  /// Tính toán responsive value dựa trên device type
  static T valueByDeviceType<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    switch (context.deviceType) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet ?? mobile;
      case DeviceType.desktop:
        return desktop ?? tablet ?? mobile;
    }
  }

  /// Tính toán responsive value với orientation
  static T valueByOrientation<T>(
    BuildContext context, {
    required T portrait,
    T? landscape,
  }) {
    return context.isLandscape ? (landscape ?? portrait) : portrait;
  }

  /// Tính toán responsive value phức tạp
  static T valueByDeviceAndOrientation<T>(
    BuildContext context, {
    required T mobilePortrait,
    T? mobileLandscape,
    T? tabletPortrait,
    T? tabletLandscape,
    T? desktopPortrait,
    T? desktopLandscape,
  }) {
    final deviceType = context.deviceType;
    final isLandscape = context.isLandscape;

    switch (deviceType) {
      case DeviceType.mobile:
        if (isLandscape && mobileLandscape != null) {
          return mobileLandscape;
        }
        return mobilePortrait;
      
      case DeviceType.tablet:
        if (isLandscape && tabletLandscape != null) {
          return tabletLandscape;
        }
        return tabletPortrait ?? mobilePortrait;
      
      case DeviceType.desktop:
        if (isLandscape && desktopLandscape != null) {
          return desktopLandscape;
        }
        return desktopPortrait ?? tabletPortrait ?? mobilePortrait;
    }
  }

  /// Kiểm tra xem có nên hiển thị side navigation không
  static bool shouldShowSideNavigation(BuildContext context) {
    return context.isTablet || context.isDesktop;
  }

  /// Kiểm tra xem có nên hiển thị bottom navigation không
  static bool shouldShowBottomNavigation(BuildContext context) {
    return context.isMobile;
  }

  /// Tính toán số cột cho grid dựa trên content width
  static int calculateGridColumns(
    BuildContext context, {
    required double itemMinWidth,
    double spacing = 16,
    double padding = 32,
  }) {
    final screenWidth = context.screenWidth;
    final availableWidth = screenWidth - (padding * 2);
    
    // Tính số cột tối đa có thể fit
    int columns = ((availableWidth + spacing) / (itemMinWidth + spacing)).floor();
    
    // Đảm bảo ít nhất 1 cột
    columns = columns.clamp(1, double.infinity).toInt();
    
    // Giới hạn số cột tối đa theo device type
    final maxColumns = context.deviceType == DeviceType.mobile 
        ? 2 
        : context.deviceType == DeviceType.tablet 
            ? 4 
            : 6;
    
    return columns.clamp(1, maxColumns);
  }

  /// Tính toán responsive font size
  static double responsiveFontSize(
    BuildContext context,
    double baseFontSize, {
    double mobileScale = 1.0,
    double tabletScale = 1.1,
    double desktopScale = 1.2,
  }) {
    switch (context.deviceType) {
      case DeviceType.mobile:
        return baseFontSize * mobileScale;
      case DeviceType.tablet:
        return baseFontSize * tabletScale;
      case DeviceType.desktop:
        return baseFontSize * desktopScale;
    }
  }

  /// Tính toán responsive spacing
  static double responsiveSpacing(
    BuildContext context,
    double baseSpacing, {
    double mobileScale = 1.0,
    double tabletScale = 1.25,
    double desktopScale = 1.5,
  }) {
    switch (context.deviceType) {
      case DeviceType.mobile:
        return baseSpacing * mobileScale;
      case DeviceType.tablet:
        return baseSpacing * tabletScale;
      case DeviceType.desktop:
        return baseSpacing * desktopScale;
    }
  }

  /// Lấy safe area padding responsive
  static EdgeInsets responsiveSafeAreaPadding(BuildContext context) {
    final safePadding = context.safeAreaPadding;
    final deviceType = context.deviceType;
    
    // Trên tablet/desktop, có thể không cần safe area padding ở sides
    if (deviceType == DeviceType.tablet || deviceType == DeviceType.desktop) {
      return EdgeInsets.only(
        top: safePadding.top,
        bottom: safePadding.bottom,
      );
    }
    
    return safePadding;
  }

  /// Tính toán responsive border radius
  static BorderRadius responsiveBorderRadius(
    BuildContext context, {
    double mobile = 8.0,
    double tablet = 12.0,
    double desktop = 16.0,
  }) {
    final radius = valueByDeviceType(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
    return BorderRadius.circular(radius);
  }

  /// Tính toán responsive elevation
  static double responsiveElevation(
    BuildContext context, {
    double mobile = 2.0,
    double tablet = 4.0,
    double desktop = 8.0,
  }) {
    return valueByDeviceType(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }
}

/// Extension methods cho responsive utilities
extension ResponsiveUtilsExtension on BuildContext {
  /// Lấy responsive value theo screen width
  T valueByWidth<T>({
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    return ResponsiveUtils.valueByScreenWidth(
      this,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Lấy responsive value theo device type
  T valueByDevice<T>({
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    return ResponsiveUtils.valueByDeviceType(
      this,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Lấy responsive value theo orientation
  T valueByOrientation<T>({
    required T portrait,
    T? landscape,
  }) {
    return ResponsiveUtils.valueByOrientation(
      this,
      portrait: portrait,
      landscape: landscape,
    );
  }

  /// Tính toán số cột grid
  int gridColumns({
    required double itemMinWidth,
    double spacing = 16,
    double padding = 32,
  }) {
    return ResponsiveUtils.calculateGridColumns(
      this,
      itemMinWidth: itemMinWidth,
      spacing: spacing,
      padding: padding,
    );
  }

  /// Responsive font size
  double fontSize(
    double baseFontSize, {
    double mobileScale = 1.0,
    double tabletScale = 1.1,
    double desktopScale = 1.2,
  }) {
    return ResponsiveUtils.responsiveFontSize(
      this,
      baseFontSize,
      mobileScale: mobileScale,
      tabletScale: tabletScale,
      desktopScale: desktopScale,
    );
  }

  /// Responsive border radius
  BorderRadius borderRadius({
    double mobile = 8.0,
    double tablet = 12.0,
    double desktop = 16.0,
  }) {
    return ResponsiveUtils.responsiveBorderRadius(
      this,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Responsive elevation
  double elevation({
    double mobile = 2.0,
    double tablet = 4.0,
    double desktop = 8.0,
  }) {
    return ResponsiveUtils.responsiveElevation(
      this,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Kiểm tra navigation type
  bool get shouldShowSideNav => ResponsiveUtils.shouldShowSideNavigation(this);
  bool get shouldShowBottomNav => ResponsiveUtils.shouldShowBottomNavigation(this);
}
