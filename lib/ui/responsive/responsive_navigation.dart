// lib/ui/responsive/responsive_navigation.dart

import 'package:banachef/ui/responsive/responsive_typography.dart';
import 'package:flutter/material.dart';
import 'responsive_breakpoints.dart';
import 'responsive_spacing.dart';

/// Responsive navigation wrapper
/// Tự động chuyển đổi giữa bottom navigation và side navigation
class ResponsiveNavigation extends StatelessWidget {
  final List<ResponsiveNavigationItem> items;
  final int currentIndex;
  final ValueChanged<int>? onTap;
  final Widget body;
  final PreferredSizeWidget? appBar;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final Widget? drawer;
  final Widget? endDrawer;
  final Color? backgroundColor;

  const ResponsiveNavigation({
    super.key,
    required this.items,
    required this.currentIndex,
    this.onTap,
    required this.body,
    this.appBar,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.drawer,
    this.endDrawer,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    if (context.isTablet || context.isDesktop) {
      return _buildSideNavigation(context);
    } else {
      return _buildBottomNavigation(context);
    }
  }

  Widget _buildBottomNavigation(BuildContext context) {
    return Scaffold(
      appBar: appBar,
      body: body,
      bottomNavigationBar: BottomNavigationBar(
        items: items.map((item) => item.toBottomNavigationBarItem()).toList(),
        currentIndex: currentIndex,
        onTap: onTap,
        type: BottomNavigationBarType.fixed,
      ),
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: floatingActionButtonLocation,
      drawer: drawer,
      endDrawer: endDrawer,
      backgroundColor: backgroundColor,
    );
  }

  Widget _buildSideNavigation(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          ResponsiveSideNavigation(
            items: items,
            currentIndex: currentIndex,
            onTap: onTap,
          ),
          Expanded(
            child: Scaffold(
              appBar: appBar,
              body: body,
              floatingActionButton: floatingActionButton,
              floatingActionButtonLocation: floatingActionButtonLocation,
              endDrawer: endDrawer,
              backgroundColor: backgroundColor,
            ),
          ),
        ],
      ),
      drawer: drawer,
    );
  }
}

/// Side navigation cho tablet và desktop
class ResponsiveSideNavigation extends StatelessWidget {
  final List<ResponsiveNavigationItem> items;
  final int currentIndex;
  final ValueChanged<int>? onTap;
  final bool expanded;

  const ResponsiveSideNavigation({
    super.key,
    required this.items,
    required this.currentIndex,
    this.onTap,
    this.expanded = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDesktop = context.isDesktop;
    final width = isDesktop ? (expanded ? 280.0 : 80.0) : 240.0;

    return Container(
      width: width,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          right: BorderSide(
            color: theme.dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Header
          if (expanded || !isDesktop) ...[
            Container(
              height: 80,
              padding: context.contentPadding,
              child: Center(
                child: Text(
                  'BanaChef',
                  style: context.headlineMedium.copyWith(
                    color: theme.colorScheme.primary,
                  ),
                ),
              ),
            ),
            Divider(height: 1, color: theme.dividerColor),
          ] else ...[
            const SizedBox(height: 80),
          ],
          
          // Navigation items
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.symmetric(vertical: context.spacingSM),
              itemCount: items.length,
              itemBuilder: (context, index) {
                final item = items[index];
                final isSelected = index == currentIndex;
                
                return _buildNavigationTile(
                  context,
                  item,
                  isSelected,
                  index,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationTile(
    BuildContext context,
    ResponsiveNavigationItem item,
    bool isSelected,
    int index,
  ) {
    final theme = Theme.of(context);
    final isDesktop = context.isDesktop;

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: context.spacingSM,
        vertical: 2,
      ),
      child: Material(
        color: isSelected ? theme.colorScheme.primary.withOpacity(0.1) : null,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          onTap: () => onTap?.call(index),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: context.spacingMD,
              vertical: context.spacingSM,
            ),
            child: Row(
              children: [
                Icon(
                  item.icon,
                  color: isSelected
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurface.withOpacity(0.6),
                  size: 24,
                ),
                if (expanded || !isDesktop) ...[
                  SizedBox(width: context.spacingMD),
                  Expanded(
                    child: Text(
                      item.label,
                      style: context.bodyMedium.copyWith(
                        color: isSelected
                            ? theme.colorScheme.primary
                            : theme.colorScheme.onSurface,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Navigation item cho responsive navigation
class ResponsiveNavigationItem {
  final IconData icon;
  final IconData? activeIcon;
  final String label;
  final String? tooltip;

  const ResponsiveNavigationItem({
    required this.icon,
    this.activeIcon,
    required this.label,
    this.tooltip,
  });

  BottomNavigationBarItem toBottomNavigationBarItem() {
    return BottomNavigationBarItem(
      icon: Icon(icon),
      activeIcon: activeIcon != null ? Icon(activeIcon!) : null,
      label: label,
      tooltip: tooltip,
    );
  }
}

/// Responsive App Bar
class ResponsiveAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? titleWidget;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final double? elevation;
  final Color? backgroundColor;
  final bool centerTitle;

  const ResponsiveAppBar({
    super.key,
    this.title,
    this.titleWidget,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.elevation,
    this.backgroundColor,
    this.centerTitle = true,
  });

  @override
  Widget build(BuildContext context) {
    final isTabletOrDesktop = context.isTablet || context.isDesktop;
    
    return AppBar(
      title: titleWidget ?? (title != null ? Text(title!) : null),
      actions: actions,
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading && !isTabletOrDesktop,
      elevation: elevation,
      backgroundColor: backgroundColor,
      centerTitle: centerTitle,
      titleTextStyle: context.headlineMedium,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// Responsive Drawer
class ResponsiveDrawer extends StatelessWidget {
  final List<ResponsiveDrawerItem> items;
  final Widget? header;
  final Widget? footer;

  const ResponsiveDrawer({
    super.key,
    required this.items,
    this.header,
    this.footer,
  });

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          if (header != null) header!,
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: items.map((item) => item.build(context)).toList(),
            ),
          ),
          if (footer != null) footer!,
        ],
      ),
    );
  }
}

/// Drawer item cho responsive drawer
abstract class ResponsiveDrawerItem {
  Widget build(BuildContext context);
}

class ResponsiveDrawerTile extends ResponsiveDrawerItem {
  final IconData icon;
  final String title;
  final String? subtitle;
  final VoidCallback? onTap;
  final bool selected;

  ResponsiveDrawerTile({
    required this.icon,
    required this.title,
    this.subtitle,
    this.onTap,
    this.selected = false,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      subtitle: subtitle != null ? Text(subtitle!) : null,
      onTap: onTap,
      selected: selected,
    );
  }
}

class ResponsiveDrawerDivider extends ResponsiveDrawerItem {
  @override
  Widget build(BuildContext context) {
    return const Divider();
  }
}

class ResponsiveDrawerHeader extends ResponsiveDrawerItem {
  final Widget child;

  ResponsiveDrawerHeader({required this.child});

  @override
  Widget build(BuildContext context) {
    return DrawerHeader(child: child);
  }
}
