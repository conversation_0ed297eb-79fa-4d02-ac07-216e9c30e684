// lib/ui/responsive/responsive_widgets.dart

import 'package:flutter/material.dart';
import 'responsive_breakpoints.dart';
import 'responsive_spacing.dart';
import 'responsive_typography.dart';

/// Responsive Card widget
class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? elevation;
  final Color? color;
  final BorderRadius? borderRadius;
  final VoidCallback? onTap;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.elevation,
    this.color,
    this.borderRadius,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final effectivePadding = padding ?? context.cardPadding;
    final effectiveElevation = elevation ?? (context.isMobile ? 2.0 : 4.0);
    final effectiveBorderRadius = borderRadius ?? BorderRadius.circular(12);

    Widget card = Card(
      elevation: effectiveElevation,
      color: color,
      shape: RoundedRectangleBorder(borderRadius: effectiveBorderRadius),
      margin: margin,
      child: Padding(
        padding: effectivePadding,
        child: child,
      ),
    );

    if (onTap != null) {
      card = InkWell(
        onTap: onTap,
        borderRadius: effectiveBorderRadius,
        child: card,
      );
    }

    return card;
  }
}

/// Responsive Button widget
class ResponsiveButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonStyle? style;
  final Widget? icon;
  final bool isLoading;
  final ResponsiveButtonSize size;

  const ResponsiveButton({
    super.key,
    required this.text,
    this.onPressed,
    this.style,
    this.icon,
    this.isLoading = false,
    this.size = ResponsiveButtonSize.medium,
  });

  const ResponsiveButton.large({
    super.key,
    required this.text,
    this.onPressed,
    this.style,
    this.icon,
    this.isLoading = false,
  }) : size = ResponsiveButtonSize.large;

  const ResponsiveButton.small({
    super.key,
    required this.text,
    this.onPressed,
    this.style,
    this.icon,
    this.isLoading = false,
  }) : size = ResponsiveButtonSize.small;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Responsive padding
    EdgeInsetsGeometry padding;
    TextStyle textStyle;
    
    switch (size) {
      case ResponsiveButtonSize.small:
        padding = EdgeInsets.symmetric(
          horizontal: context.spacingMD,
          vertical: context.spacingSM,
        );
        textStyle = context.buttonSmall;
        break;
      case ResponsiveButtonSize.medium:
        padding = EdgeInsets.symmetric(
          horizontal: context.spacingLG,
          vertical: context.spacingMD,
        );
        textStyle = context.buttonMedium;
        break;
      case ResponsiveButtonSize.large:
        padding = EdgeInsets.symmetric(
          horizontal: context.spacingXL,
          vertical: context.spacingLG,
        );
        textStyle = context.buttonLarge;
        break;
    }

    final effectiveStyle = (style ?? theme.elevatedButtonTheme.style)?.copyWith(
      padding: WidgetStateProperty.all(padding),
      textStyle: WidgetStateProperty.all(textStyle),
    );

    Widget buttonChild;
    if (isLoading) {
      buttonChild = SizedBox(
        height: textStyle.fontSize,
        width: textStyle.fontSize,
        child: const CircularProgressIndicator(strokeWidth: 2),
      );
    } else if (icon != null) {
      buttonChild = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          icon!,
          SizedBox(width: context.spacingSM),
          Text(text),
        ],
      );
    } else {
      buttonChild = Text(text);
    }

    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: effectiveStyle,
      child: buttonChild,
    );
  }
}

enum ResponsiveButtonSize { small, medium, large }

/// Responsive Dialog
class ResponsiveDialog extends StatelessWidget {
  final String? title;
  final Widget? titleWidget;
  final Widget content;
  final List<Widget>? actions;
  final EdgeInsetsGeometry? contentPadding;
  final bool scrollable;

  const ResponsiveDialog({
    super.key,
    this.title,
    this.titleWidget,
    required this.content,
    this.actions,
    this.contentPadding,
    this.scrollable = false,
  });

  @override
  Widget build(BuildContext context) {
    final isDesktop = context.isDesktop;
    final maxWidth = isDesktop ? 600.0 : context.screenWidth * 0.9;

    return Dialog(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: maxWidth,
          maxHeight: context.screenHeight * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title
            if (title != null || titleWidget != null) ...[
              Padding(
                padding: context.contentPadding,
                child: titleWidget ?? 
                  Text(title!, style: context.headlineMedium),
              ),
              const Divider(height: 1),
            ],
            
            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: contentPadding ?? context.contentPadding,
                child: content,
              ),
            ),
            
            // Actions
            if (actions != null && actions!.isNotEmpty) ...[
              const Divider(height: 1),
              Padding(
                padding: context.contentPadding,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: actions!
                      .map((action) => Padding(
                            padding: EdgeInsets.only(left: context.spacingSM),
                            child: action,
                          ))
                      .toList(),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  static Future<T?> show<T>({
    required BuildContext context,
    String? title,
    Widget? titleWidget,
    required Widget content,
    List<Widget>? actions,
    EdgeInsetsGeometry? contentPadding,
    bool scrollable = false,
    bool barrierDismissible = true,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => ResponsiveDialog(
        title: title,
        titleWidget: titleWidget,
        content: content,
        actions: actions,
        contentPadding: contentPadding,
        scrollable: scrollable,
      ),
    );
  }
}

/// Responsive Bottom Sheet
class ResponsiveBottomSheet extends StatelessWidget {
  final Widget child;
  final String? title;
  final bool showHandle;
  final double? maxHeight;

  const ResponsiveBottomSheet({
    super.key,
    required this.child,
    this.title,
    this.showHandle = true,
    this.maxHeight,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveMaxHeight = maxHeight ?? context.screenHeight * 0.9;

    return Container(
      constraints: BoxConstraints(maxHeight: effectiveMaxHeight),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          if (showHandle) ...[
            Container(
              margin: EdgeInsets.symmetric(vertical: context.spacingSM),
              height: 4,
              width: 40,
              decoration: BoxDecoration(
                color: Theme.of(context).dividerColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
          
          // Title
          if (title != null) ...[
            Padding(
              padding: context.contentPadding,
              child: Text(title!, style: context.headlineMedium),
            ),
            const Divider(height: 1),
          ],
          
          // Content
          Flexible(
            child: Padding(
              padding: context.contentPadding,
              child: child,
            ),
          ),
        ],
      ),
    );
  }

  static Future<T?> show<T>({
    required BuildContext context,
    required Widget child,
    String? title,
    bool showHandle = true,
    double? maxHeight,
    bool isDismissible = true,
    bool enableDrag = true,
  }) {
    if (context.isDesktop) {
      // Show as dialog on desktop
      return ResponsiveDialog.show<T>(
        context: context,
        title: title,
        content: child,
      );
    } else {
      // Show as bottom sheet on mobile/tablet
      return showModalBottomSheet<T>(
        context: context,
        isScrollControlled: true,
        isDismissible: isDismissible,
        enableDrag: enableDrag,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder: (context) => ResponsiveBottomSheet(
          title: title,
          showHandle: showHandle,
          maxHeight: maxHeight,
          child: child,
        ),
      );
    }
  }
}

/// Responsive List Tile
class ResponsiveListTile extends StatelessWidget {
  final Widget? leading;
  final Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? contentPadding;

  const ResponsiveListTile({
    super.key,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.contentPadding,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveContentPadding = contentPadding ?? 
        EdgeInsets.symmetric(
          horizontal: context.spacingMD,
          vertical: context.spacingSM,
        );

    return ListTile(
      leading: leading,
      title: title,
      subtitle: subtitle,
      trailing: trailing,
      onTap: onTap,
      contentPadding: effectiveContentPadding,
    );
  }
}
