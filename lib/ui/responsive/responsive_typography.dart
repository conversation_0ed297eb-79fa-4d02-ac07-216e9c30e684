// lib/ui/responsive/responsive_typography.dart

import 'package:flutter/material.dart';
import 'responsive_breakpoints.dart';

/// <PERSON><PERSON> thống typography responsive
/// Tự động điều chỉnh kích thước font dự<PERSON> trên kích thước màn hình
class ResponsiveTypography {
  ResponsiveTypography._();

  // Font families
  static const String _fontFamilyHeading = 'Nunito';
  static const String _fontFamilyBody = 'Inter';

  // Scale factors for different devices
  static const double _mobileScale = 1.0;
  static const double _tabletScale = 1.1;
  static const double _desktopScale = 1.2;

  /// Lấy scale factor dựa trên device type
  static double _getScaleFactor(BuildContext context) {
    switch (context.deviceType) {
      case DeviceType.mobile:
        return _mobileScale;
      case DeviceType.tablet:
        return _tabletScale;
      case DeviceType.desktop:
        return _desktopScale;
    }
  }

  /// Tính toán font size responsive
  static double _responsiveFontSize(BuildContext context, double baseFontSize) {
    return baseFontSize * _getScaleFactor(context);
  }

  // Heading styles
  static TextStyle displayLarge(BuildContext context) {
    return TextStyle(
      fontFamily: _fontFamilyHeading,
      fontWeight: FontWeight.bold,
      fontSize: _responsiveFontSize(context, 32),
      height: 1.2,
    );
  }

  static TextStyle displayMedium(BuildContext context) {
    return TextStyle(
      fontFamily: _fontFamilyHeading,
      fontWeight: FontWeight.bold,
      fontSize: _responsiveFontSize(context, 28),
      height: 1.2,
    );
  }

  static TextStyle displaySmall(BuildContext context) {
    return TextStyle(
      fontFamily: _fontFamilyHeading,
      fontWeight: FontWeight.bold,
      fontSize: _responsiveFontSize(context, 24),
      height: 1.3,
    );
  }

  static TextStyle headlineLarge(BuildContext context) {
    return TextStyle(
      fontFamily: _fontFamilyHeading,
      fontWeight: FontWeight.bold,
      fontSize: _responsiveFontSize(context, 22),
      height: 1.3,
    );
  }

  static TextStyle headlineMedium(BuildContext context) {
    return TextStyle(
      fontFamily: _fontFamilyHeading,
      fontWeight: FontWeight.bold,
      fontSize: _responsiveFontSize(context, 20),
      height: 1.3,
    );
  }

  static TextStyle headlineSmall(BuildContext context) {
    return TextStyle(
      fontFamily: _fontFamilyHeading,
      fontWeight: FontWeight.w600,
      fontSize: _responsiveFontSize(context, 18),
      height: 1.4,
    );
  }

  // Title styles
  static TextStyle titleLarge(BuildContext context) {
    return TextStyle(
      fontFamily: _fontFamilyBody,
      fontWeight: FontWeight.w600,
      fontSize: _responsiveFontSize(context, 18),
      height: 1.4,
    );
  }

  static TextStyle titleMedium(BuildContext context) {
    return TextStyle(
      fontFamily: _fontFamilyBody,
      fontWeight: FontWeight.w600,
      fontSize: _responsiveFontSize(context, 16),
      height: 1.4,
    );
  }

  static TextStyle titleSmall(BuildContext context) {
    return TextStyle(
      fontFamily: _fontFamilyBody,
      fontWeight: FontWeight.w600,
      fontSize: _responsiveFontSize(context, 14),
      height: 1.4,
    );
  }

  // Body styles
  static TextStyle bodyLarge(BuildContext context) {
    return TextStyle(
      fontFamily: _fontFamilyBody,
      fontWeight: FontWeight.normal,
      fontSize: _responsiveFontSize(context, 16),
      height: 1.5,
    );
  }

  static TextStyle bodyMedium(BuildContext context) {
    return TextStyle(
      fontFamily: _fontFamilyBody,
      fontWeight: FontWeight.normal,
      fontSize: _responsiveFontSize(context, 14),
      height: 1.5,
    );
  }

  static TextStyle bodySmall(BuildContext context) {
    return TextStyle(
      fontFamily: _fontFamilyBody,
      fontWeight: FontWeight.normal,
      fontSize: _responsiveFontSize(context, 12),
      height: 1.5,
    );
  }

  // Label styles
  static TextStyle labelLarge(BuildContext context) {
    return TextStyle(
      fontFamily: _fontFamilyBody,
      fontWeight: FontWeight.w600,
      fontSize: _responsiveFontSize(context, 16),
      height: 1.4,
    );
  }

  static TextStyle labelMedium(BuildContext context) {
    return TextStyle(
      fontFamily: _fontFamilyBody,
      fontWeight: FontWeight.w600,
      fontSize: _responsiveFontSize(context, 14),
      height: 1.4,
    );
  }

  static TextStyle labelSmall(BuildContext context) {
    return TextStyle(
      fontFamily: _fontFamilyBody,
      fontWeight: FontWeight.w600,
      fontSize: _responsiveFontSize(context, 12),
      height: 1.4,
    );
  }

  // Custom styles for specific use cases
  static TextStyle caption(BuildContext context) {
    return TextStyle(
      fontFamily: _fontFamilyBody,
      fontWeight: FontWeight.normal,
      fontSize: _responsiveFontSize(context, 11),
      height: 1.4,
    );
  }

  static TextStyle overline(BuildContext context) {
    return TextStyle(
      fontFamily: _fontFamilyBody,
      fontWeight: FontWeight.w500,
      fontSize: _responsiveFontSize(context, 10),
      height: 1.4,
      letterSpacing: 1.5,
    );
  }

  // Button text styles
  static TextStyle buttonLarge(BuildContext context) {
    return TextStyle(
      fontFamily: _fontFamilyBody,
      fontWeight: FontWeight.w600,
      fontSize: _responsiveFontSize(context, 16),
      height: 1.2,
    );
  }

  static TextStyle buttonMedium(BuildContext context) {
    return TextStyle(
      fontFamily: _fontFamilyBody,
      fontWeight: FontWeight.w600,
      fontSize: _responsiveFontSize(context, 14),
      height: 1.2,
    );
  }

  static TextStyle buttonSmall(BuildContext context) {
    return TextStyle(
      fontFamily: _fontFamilyBody,
      fontWeight: FontWeight.w600,
      fontSize: _responsiveFontSize(context, 12),
      height: 1.2,
    );
  }
}

/// Extension để dễ dàng sử dụng responsive typography
extension ResponsiveTypographyExtension on BuildContext {
  // Display styles
  TextStyle get displayLarge => ResponsiveTypography.displayLarge(this);
  TextStyle get displayMedium => ResponsiveTypography.displayMedium(this);
  TextStyle get displaySmall => ResponsiveTypography.displaySmall(this);

  // Headline styles
  TextStyle get headlineLarge => ResponsiveTypography.headlineLarge(this);
  TextStyle get headlineMedium => ResponsiveTypography.headlineMedium(this);
  TextStyle get headlineSmall => ResponsiveTypography.headlineSmall(this);

  // Title styles
  TextStyle get titleLarge => ResponsiveTypography.titleLarge(this);
  TextStyle get titleMedium => ResponsiveTypography.titleMedium(this);
  TextStyle get titleSmall => ResponsiveTypography.titleSmall(this);

  // Body styles
  TextStyle get bodyLarge => ResponsiveTypography.bodyLarge(this);
  TextStyle get bodyMedium => ResponsiveTypography.bodyMedium(this);
  TextStyle get bodySmall => ResponsiveTypography.bodySmall(this);

  // Label styles
  TextStyle get labelLarge => ResponsiveTypography.labelLarge(this);
  TextStyle get labelMedium => ResponsiveTypography.labelMedium(this);
  TextStyle get labelSmall => ResponsiveTypography.labelSmall(this);

  // Custom styles
  TextStyle get caption => ResponsiveTypography.caption(this);
  TextStyle get overline => ResponsiveTypography.overline(this);

  // Button styles
  TextStyle get buttonLarge => ResponsiveTypography.buttonLarge(this);
  TextStyle get buttonMedium => ResponsiveTypography.buttonMedium(this);
  TextStyle get buttonSmall => ResponsiveTypography.buttonSmall(this);
}

/// Widget Text với responsive typography
class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle Function(BuildContext) styleBuilder;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final Color? color;

  const ResponsiveText(
    this.text, {
    super.key,
    required this.styleBuilder,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.color,
  });

  // Predefined constructors
  const ResponsiveText.displayLarge(
    this.text, {
    super.key,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.color,
  }) : styleBuilder = ResponsiveTypography.displayLarge;

  const ResponsiveText.displayMedium(
    this.text, {
    super.key,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.color,
  }) : styleBuilder = ResponsiveTypography.displayMedium;

  const ResponsiveText.headlineMedium(
    this.text, {
    super.key,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.color,
  }) : styleBuilder = ResponsiveTypography.headlineMedium;

  const ResponsiveText.titleMedium(
    this.text, {
    super.key,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.color,
  }) : styleBuilder = ResponsiveTypography.titleMedium;

  const ResponsiveText.bodyLarge(
    this.text, {
    super.key,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.color,
  }) : styleBuilder = ResponsiveTypography.bodyLarge;

  const ResponsiveText.bodySmall(
    this.text, {
    super.key,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.color,
  }) : styleBuilder = ResponsiveTypography.bodySmall;

  @override
  Widget build(BuildContext context) {
    TextStyle style = styleBuilder(context);
    if (color != null) {
      style = style.copyWith(color: color);
    }

    return Text(
      text,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}
