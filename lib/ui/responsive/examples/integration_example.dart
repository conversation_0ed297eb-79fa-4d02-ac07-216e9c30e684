// lib/ui/responsive/examples/integration_example.dart

import 'package:flutter/material.dart';
import '../responsive.dart';

/// V<PERSON> dụ tích hợp responsive system với cấu trúc app hiện tại
/// <PERSON> họa cách sử dụng với features và shared widgets

/// Base responsive view cho tất cả các màn hình
abstract class ResponsiveBaseView extends StatelessWidget {
  const ResponsiveBaseView({super.key});

  /// Title của màn hình
  String get title;

  /// Body content cho mobile
  Widget buildMobileBody(BuildContext context);

  /// Body content cho tablet (optional, fallback to mobile)
  Widget buildTabletBody(BuildContext context) => buildMobileBody(context);

  /// Body content cho desktop (optional, fallback to tablet)
  Widget buildDesktopBody(BuildContext context) => buildTabletBody(context);

  /// App bar actions (optional)
  List<Widget>? buildActions(BuildContext context) => null;

  /// Floating action button (optional)
  Widget? buildFloatingActionButton(BuildContext context) => null;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ResponsiveAppBar(
        title: title,
        actions: buildActions(context),
      ),
      body: ResponsiveContainer(
        maxWidthTablet: 800,
        maxWidthDesktop: 1200,
        padding: context.pagePadding,
        child: ResponsiveLayoutBuilder(
          mobile: buildMobileBody(context),
          tablet: buildTabletBody(context),
          desktop: buildDesktopBody(context),
        ),
      ),
      floatingActionButton: buildFloatingActionButton(context),
    );
  }
}

/// Ví dụ responsive view cho feature Discovery
class ResponsiveDiscoveryView extends ResponsiveBaseView {
  const ResponsiveDiscoveryView({super.key});

  @override
  String get title => 'Khám phá công thức';

  @override
  Widget buildMobileBody(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSearchSection(context),
          VSpace.lg(),
          _buildCategoriesSection(context),
          VSpace.lg(),
          _buildRecipesGrid(context),
        ],
      ),
    );
  }

  @override
  Widget buildTabletBody(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Sidebar với categories
        SizedBox(
          width: 280,
          child: Column(
            children: [
              _buildSearchSection(context),
              VSpace.lg(),
              _buildCategoriesSection(context),
            ],
          ),
        ),
        HSpace.xl(),
        // Main content
        Expanded(
          child: SingleChildScrollView(
            child: _buildRecipesGrid(context),
          ),
        ),
      ],
    );
  }

  @override
  Widget buildDesktopBody(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Sidebar rộng hơn cho desktop
        SizedBox(
          width: 320,
          child: Column(
            children: [
              _buildSearchSection(context),
              VSpace.lg(),
              _buildCategoriesSection(context),
              VSpace.lg(),
              _buildFiltersSection(context), // Thêm filters cho desktop
            ],
          ),
        ),
        HSpace.xl(),
        // Main content với grid lớn hơn
        Expanded(
          child: SingleChildScrollView(
            child: _buildRecipesGrid(context),
          ),
        ),
      ],
    );
  }

  Widget _buildSearchSection(BuildContext context) {
    return ResponsiveCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveText.titleMedium('Tìm kiếm'),
          VSpace.sm(),
          TextField(
            decoration: const InputDecoration(
              hintText: 'Tìm công thức...',
              prefixIcon: Icon(Icons.search),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoriesSection(BuildContext context) {
    final categories = [
      'Món chính', 'Món phụ', 'Tráng miệng', 'Đồ uống',
      'Chay', 'Hải sản', 'Thịt', 'Gà'
    ];

    return ResponsiveCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveText.titleMedium('Danh mục'),
          VSpace.sm(),
          ResponsiveWrap(
            children: categories.map((category) => 
              Padding(
                padding: EdgeInsets.only(
                  right: context.spacingSM,
                  bottom: context.spacingSM,
                ),
                child: Chip(
                  label: Text(category),
                  onDeleted: () {}, // For demo
                ),
              ),
            ).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection(BuildContext context) {
    return ResponsiveCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveText.titleMedium('Bộ lọc'),
          VSpace.sm(),
          _buildFilterItem(context, 'Thời gian nấu', '< 30 phút'),
          _buildFilterItem(context, 'Độ khó', 'Dễ'),
          _buildFilterItem(context, 'Calories', '< 500'),
        ],
      ),
    );
  }

  Widget _buildFilterItem(BuildContext context, String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: context.spacingSM),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: context.bodyMedium),
          Text(value, style: context.bodySmall),
        ],
      ),
    );
  }

  Widget _buildRecipesGrid(BuildContext context) {
    // Tạo dummy data
    final recipes = List.generate(20, (index) => {
      'id': index,
      'name': 'Công thức ${index + 1}',
      'description': 'Mô tả ngắn về công thức này',
      'cookTime': '${15 + (index % 3) * 10} phút',
      'difficulty': ['Dễ', 'Trung bình', 'Khó'][index % 3],
    });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveText.titleMedium('Công thức nổi bật'),
        VSpace.md(),
        ResponsiveGrid(
          mobileColumns: 1,
          tabletColumns: 2,
          desktopColumns: 3,
          spacing: context.itemSpacing,
          runSpacing: context.itemSpacing,
          children: recipes.map((recipe) => _buildRecipeCard(context, recipe)).toList(),
        ),
      ],
    );
  }

  Widget _buildRecipeCard(BuildContext context, Map<String, dynamic> recipe) {
    return ResponsiveCard(
      onTap: () {
        // Navigate to recipe detail
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Recipe image placeholder
          Container(
            height: context.valueByDevice(
              mobile: 120.0,
              tablet: 140.0,
              desktop: 160.0,
            ),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: context.borderRadius(),
            ),
            child: const Center(
              child: Icon(Icons.restaurant, size: 40),
            ),
          ),
          VSpace.sm(),
          ResponsiveText.titleMedium(recipe['name']),
          VSpace.xs(),
          ResponsiveText.bodySmall(
            recipe['description'],
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          VSpace.sm(),
          Row(
            children: [
              Icon(Icons.access_time, size: 16, color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6)),
              HSpace.xs(),
              Text(recipe['cookTime'], style: context.bodySmall),
              const Spacer(),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: context.spacingSM,
                  vertical: context.spacingXS,
                ),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  recipe['difficulty'],
                  style: context.bodySmall.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget? buildFloatingActionButton(BuildContext context) {
    // Chỉ hiển thị FAB trên mobile
    if (context.isMobile) {
      return FloatingActionButton(
        onPressed: () {
          // Add new recipe
        },
        child: const Icon(Icons.add),
      );
    }
    return null;
  }

  @override
  List<Widget>? buildActions(BuildContext context) {
    return [
      IconButton(
        onPressed: () {
          // Filter action
        },
        icon: const Icon(Icons.filter_list),
      ),
      if (context.isTablet || context.isDesktop) ...[
        IconButton(
          onPressed: () {
            // Sort action
          },
          icon: const Icon(Icons.sort),
        ),
      ],
    ];
  }
}

/// Ví dụ responsive main app với navigation
class ResponsiveMainApp extends StatefulWidget {
  const ResponsiveMainApp({super.key});

  @override
  State<ResponsiveMainApp> createState() => _ResponsiveMainAppState();
}

class _ResponsiveMainAppState extends State<ResponsiveMainApp> {
  int _currentIndex = 0;

  final List<ResponsiveNavigationItem> _navigationItems = [
    const ResponsiveNavigationItem(
      icon: Icons.home_outlined,
      activeIcon: Icons.home,
      label: 'Trang chủ',
    ),
    const ResponsiveNavigationItem(
      icon: Icons.search_outlined,
      activeIcon: Icons.search,
      label: 'Khám phá',
    ),
    const ResponsiveNavigationItem(
      icon: Icons.calendar_today_outlined,
      activeIcon: Icons.calendar_today,
      label: 'Kế hoạch',
    ),
    const ResponsiveNavigationItem(
      icon: Icons.person_outline,
      activeIcon: Icons.person,
      label: 'Cá nhân',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return ResponsiveNavigation(
      items: _navigationItems,
      currentIndex: _currentIndex,
      onTap: (index) {
        setState(() {
          _currentIndex = index;
        });
      },
      body: _buildCurrentPage(),
    );
  }

  Widget _buildCurrentPage() {
    switch (_currentIndex) {
      case 0:
        return const Center(child: Text('Trang chủ'));
      case 1:
        return const ResponsiveDiscoveryView();
      case 2:
        return const Center(child: Text('Kế hoạch bữa ăn'));
      case 3:
        return const Center(child: Text('Cá nhân'));
      default:
        return const Center(child: Text('Trang chủ'));
    }
  }
}
