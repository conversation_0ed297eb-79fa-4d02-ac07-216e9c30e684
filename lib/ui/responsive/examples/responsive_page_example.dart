// lib/ui/responsive/examples/responsive_page_example.dart

import 'package:flutter/material.dart';
import '../responsive.dart';

/// V<PERSON> dụ về cách sử dụng responsive system trong một trang
class ResponsivePageExample extends StatelessWidget {
  const ResponsivePageExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const ResponsiveAppBar(title: 'Responsive Example'),
      body: ResponsiveContainer(
        maxWidthMobile: double.infinity,
        maxWidthTablet: 800,
        maxWidthDesktop: 1200,
        padding: context.pagePadding,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header section
              _buildHeaderSection(context),

              VSpace.lg(),

              // Content grid
              _buildContentGrid(context),

              VSpace.lg(),

              // Action buttons
              _buildActionButtons(context),

              VSpace.xl(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context) {
    return ResponsiveLayoutBuilder(
      mobile: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveText.displayMedium('Welcome to BanaChef'),
          VSpace.sm(),
          ResponsiveText.bodyLarge(
            'Discover amazing recipes and plan your meals with AI assistance.',
          ),
        ],
      ),
      tablet: Row(
        children: [
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ResponsiveText.displayLarge('Welcome to BanaChef'),
                VSpace.sm(),
                ResponsiveText.bodyLarge(
                  'Discover amazing recipes and plan your meals with AI assistance.',
                ),
              ],
            ),
          ),
          HSpace.xl(),
          Expanded(
            child: Container(
              height: 200,
              decoration: BoxDecoration(
                color: Theme.of(
                  context,
                ).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Center(child: Icon(Icons.restaurant_menu, size: 80)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentGrid(BuildContext context) {
    final items = List.generate(
      context.isMobile ? 4 : 8,
      (index) => _buildContentCard(context, index),
    );

    return ResponsiveGrid(
      mobileColumns: 1,
      tabletColumns: 2,
      desktopColumns: 3,
      spacing: context.itemSpacing,
      runSpacing: context.itemSpacing,
      children: items,
    );
  }

  Widget _buildContentCard(BuildContext context, int index) {
    return ResponsiveCard(
      onTap: () {
        // Handle card tap
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 120,
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).colorScheme.primary.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(child: Icon(Icons.image, size: 40)),
          ),
          VSpace.sm(),
          ResponsiveText.titleMedium('Recipe ${index + 1}'),
          VSpace.xs(),
          ResponsiveText.bodySmall(
            'A delicious recipe that you will love to cook and eat.',
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return ResponsiveLayoutBuilder(
      mobile: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          ResponsiveButton.large(
            text: 'Explore Recipes',
            onPressed: () {},
            icon: const Icon(Icons.explore),
          ),
          VSpace.sm(),
          ResponsiveButton(
            text: 'Plan Meals',
            onPressed: () {},
            icon: const Icon(Icons.calendar_today),
          ),
        ],
      ),
      tablet: Row(
        children: [
          Expanded(
            child: ResponsiveButton.large(
              text: 'Explore Recipes',
              onPressed: () {},
              icon: const Icon(Icons.explore),
            ),
          ),
          HSpace.md(),
          Expanded(
            child: ResponsiveButton.large(
              text: 'Plan Meals',
              onPressed: () {},
              icon: const Icon(Icons.calendar_today),
            ),
          ),
        ],
      ),
    );
  }
}

/// Ví dụ về responsive navigation
class ResponsiveNavigationExample extends StatefulWidget {
  const ResponsiveNavigationExample({super.key});

  @override
  State<ResponsiveNavigationExample> createState() =>
      _ResponsiveNavigationExampleState();
}

class _ResponsiveNavigationExampleState
    extends State<ResponsiveNavigationExample> {
  int _currentIndex = 0;

  final List<ResponsiveNavigationItem> _navigationItems = [
    const ResponsiveNavigationItem(
      icon: Icons.home_outlined,
      activeIcon: Icons.home,
      label: 'Home',
    ),
    const ResponsiveNavigationItem(
      icon: Icons.search_outlined,
      activeIcon: Icons.search,
      label: 'Discover',
    ),
    const ResponsiveNavigationItem(
      icon: Icons.calendar_today_outlined,
      activeIcon: Icons.calendar_today,
      label: 'Meal Plan',
    ),
    const ResponsiveNavigationItem(
      icon: Icons.person_outline,
      activeIcon: Icons.person,
      label: 'Profile',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return ResponsiveNavigation(
      items: _navigationItems,
      currentIndex: _currentIndex,
      onTap: (index) {
        setState(() {
          _currentIndex = index;
        });
      },
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    switch (_currentIndex) {
      case 0:
        return const ResponsivePageExample();
      case 1:
        return _buildDiscoverPage();
      case 2:
        return _buildMealPlanPage();
      case 3:
        return _buildProfilePage();
      default:
        return const ResponsivePageExample();
    }
  }

  Widget _buildDiscoverPage() {
    return Scaffold(
      appBar: const ResponsiveAppBar(title: 'Discover'),
      body: ResponsiveContainer(
        padding: context.pagePadding,
        child: const Center(
          child: ResponsiveText.headlineMedium('Discover Page'),
        ),
      ),
    );
  }

  Widget _buildMealPlanPage() {
    return Scaffold(
      appBar: const ResponsiveAppBar(title: 'Meal Plan'),
      body: ResponsiveContainer(
        padding: context.pagePadding,
        child: const Center(
          child: ResponsiveText.headlineMedium('Meal Plan Page'),
        ),
      ),
    );
  }

  Widget _buildProfilePage() {
    return Scaffold(
      appBar: const ResponsiveAppBar(title: 'Profile'),
      body: ResponsiveContainer(
        padding: context.pagePadding,
        child: const Center(
          child: ResponsiveText.headlineMedium('Profile Page'),
        ),
      ),
    );
  }
}
