// lib/ui/responsive/responsive_spacing.dart

import 'package:flutter/material.dart';
import 'responsive_breakpoints.dart';

/// <PERSON><PERSON> thống spacing responsive cho ứng dụng
/// Tự động điều chỉnh khoảng cách dựa trên kích thướ<PERSON> mà<PERSON>nh
class ResponsiveSpacing {
  ResponsiveSpacing._();

  // Base spacing values
  static const double _baseUnit = 8.0;

  // Spacing multipliers for different devices
  static const double _mobileMultiplier = 1.0;
  static const double _tabletMultiplier = 1.25;
  static const double _desktopMultiplier = 1.5;

  /// Lấy multiplier dựa trên device type
  static double _getMultiplier(BuildContext context) {
    switch (context.deviceType) {
      case DeviceType.mobile:
        return _mobileMultiplier;
      case DeviceType.tablet:
        return _tabletMultiplier;
      case DeviceType.desktop:
        return _desktopMultiplier;
    }
  }

  /// Tính toán spacing value
  static double _calculateSpacing(BuildContext context, double baseValue) {
    return baseValue * _getMultiplier(context);
  }

  // Predefined spacing values
  static double xs(BuildContext context) => _calculateSpacing(context, _baseUnit * 0.5); // 4dp
  static double sm(BuildContext context) => _calculateSpacing(context, _baseUnit); // 8dp
  static double md(BuildContext context) => _calculateSpacing(context, _baseUnit * 2); // 16dp
  static double lg(BuildContext context) => _calculateSpacing(context, _baseUnit * 3); // 24dp
  static double xl(BuildContext context) => _calculateSpacing(context, _baseUnit * 4); // 32dp
  static double xxl(BuildContext context) => _calculateSpacing(context, _baseUnit * 6); // 48dp
  static double xxxl(BuildContext context) => _calculateSpacing(context, _baseUnit * 8); // 64dp

  // Custom spacing
  static double custom(BuildContext context, double multiplier) {
    return _calculateSpacing(context, _baseUnit * multiplier);
  }

  // Responsive EdgeInsets
  static EdgeInsets all(BuildContext context, double multiplier) {
    final spacing = custom(context, multiplier);
    return EdgeInsets.all(spacing);
  }

  static EdgeInsets symmetric(BuildContext context, {double? horizontal, double? vertical}) {
    return EdgeInsets.symmetric(
      horizontal: horizontal != null ? custom(context, horizontal) : 0,
      vertical: vertical != null ? custom(context, vertical) : 0,
    );
  }

  static EdgeInsets only(BuildContext context, {
    double? left,
    double? top,
    double? right,
    double? bottom,
  }) {
    return EdgeInsets.only(
      left: left != null ? custom(context, left) : 0,
      top: top != null ? custom(context, top) : 0,
      right: right != null ? custom(context, right) : 0,
      bottom: bottom != null ? custom(context, bottom) : 0,
    );
  }

  // Predefined EdgeInsets
  static EdgeInsets get paddingXS => const EdgeInsets.all(4);
  static EdgeInsets get paddingSM => const EdgeInsets.all(8);
  static EdgeInsets get paddingMD => const EdgeInsets.all(16);
  static EdgeInsets get paddingLG => const EdgeInsets.all(24);
  static EdgeInsets get paddingXL => const EdgeInsets.all(32);

  // Responsive predefined EdgeInsets
  static EdgeInsets paddingXSResponsive(BuildContext context) => all(context, 0.5);
  static EdgeInsets paddingSMResponsive(BuildContext context) => all(context, 1);
  static EdgeInsets paddingMDResponsive(BuildContext context) => all(context, 2);
  static EdgeInsets paddingLGResponsive(BuildContext context) => all(context, 3);
  static EdgeInsets paddingXLResponsive(BuildContext context) => all(context, 4);

  // Page padding (responsive)
  static EdgeInsets pagePadding(BuildContext context) {
    switch (context.deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.all(16);
      case DeviceType.tablet:
        return const EdgeInsets.all(24);
      case DeviceType.desktop:
        return const EdgeInsets.all(32);
    }
  }

  // Content padding (responsive)
  static EdgeInsets contentPadding(BuildContext context) {
    switch (context.deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 12);
      case DeviceType.tablet:
        return const EdgeInsets.symmetric(horizontal: 24, vertical: 16);
      case DeviceType.desktop:
        return const EdgeInsets.symmetric(horizontal: 32, vertical: 20);
    }
  }

  // Card padding (responsive)
  static EdgeInsets cardPadding(BuildContext context) {
    switch (context.deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.all(16);
      case DeviceType.tablet:
        return const EdgeInsets.all(20);
      case DeviceType.desktop:
        return const EdgeInsets.all(24);
    }
  }

  // Section spacing (responsive)
  static double sectionSpacing(BuildContext context) {
    switch (context.deviceType) {
      case DeviceType.mobile:
        return 24;
      case DeviceType.tablet:
        return 32;
      case DeviceType.desktop:
        return 40;
    }
  }

  // Item spacing (responsive)
  static double itemSpacing(BuildContext context) {
    switch (context.deviceType) {
      case DeviceType.mobile:
        return 12;
      case DeviceType.tablet:
        return 16;
      case DeviceType.desktop:
        return 20;
    }
  }
}

/// Extension để dễ dàng sử dụng responsive spacing
extension ResponsiveSpacingExtension on BuildContext {
  // Quick access to spacing values
  double get spacingXS => ResponsiveSpacing.xs(this);
  double get spacingSM => ResponsiveSpacing.sm(this);
  double get spacingMD => ResponsiveSpacing.md(this);
  double get spacingLG => ResponsiveSpacing.lg(this);
  double get spacingXL => ResponsiveSpacing.xl(this);
  double get spacingXXL => ResponsiveSpacing.xxl(this);
  double get spacingXXXL => ResponsiveSpacing.xxxl(this);

  // Quick access to padding
  EdgeInsets get pagePadding => ResponsiveSpacing.pagePadding(this);
  EdgeInsets get contentPadding => ResponsiveSpacing.contentPadding(this);
  EdgeInsets get cardPadding => ResponsiveSpacing.cardPadding(this);

  // Quick access to spacing
  double get sectionSpacing => ResponsiveSpacing.sectionSpacing(this);
  double get itemSpacing => ResponsiveSpacing.itemSpacing(this);

  // Custom spacing
  double spacing(double multiplier) => ResponsiveSpacing.custom(this, multiplier);
  EdgeInsets paddingAll(double multiplier) => ResponsiveSpacing.all(this, multiplier);
  EdgeInsets paddingSymmetric({double? horizontal, double? vertical}) {
    return ResponsiveSpacing.symmetric(this, horizontal: horizontal, vertical: vertical);
  }
  EdgeInsets paddingOnly({double? left, double? top, double? right, double? bottom}) {
    return ResponsiveSpacing.only(this, left: left, top: top, right: right, bottom: bottom);
  }
}

/// Widget để tạo responsive spacing
class ResponsiveSpacingWidget extends StatelessWidget {
  final double? width;
  final double? height;
  final double? widthMultiplier;
  final double? heightMultiplier;

  const ResponsiveSpacingWidget({
    super.key,
    this.width,
    this.height,
    this.widthMultiplier,
    this.heightMultiplier,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? (widthMultiplier != null ? context.spacing(widthMultiplier!) : null),
      height: height ?? (heightMultiplier != null ? context.spacing(heightMultiplier!) : null),
    );
  }
}

/// Shorthand widgets cho spacing
class VSpace extends ResponsiveSpacingWidget {
  const VSpace(double heightMultiplier, {super.key}) : super(heightMultiplier: heightMultiplier);
  const VSpace.xs({super.key}) : super(heightMultiplier: 0.5);
  const VSpace.sm({super.key}) : super(heightMultiplier: 1);
  const VSpace.md({super.key}) : super(heightMultiplier: 2);
  const VSpace.lg({super.key}) : super(heightMultiplier: 3);
  const VSpace.xl({super.key}) : super(heightMultiplier: 4);
}

class HSpace extends ResponsiveSpacingWidget {
  const HSpace(double widthMultiplier, {super.key}) : super(widthMultiplier: widthMultiplier);
  const HSpace.xs({super.key}) : super(widthMultiplier: 0.5);
  const HSpace.sm({super.key}) : super(widthMultiplier: 1);
  const HSpace.md({super.key}) : super(widthMultiplier: 2);
  const HSpace.lg({super.key}) : super(widthMultiplier: 3);
  const HSpace.xl({super.key}) : super(widthMultiplier: 4);
}
