#!/bin/bash

# Script to regenerate Flutter Gen assets
# Usage: ./scripts/generate_assets.sh

echo "🎨 Regenerating Flutter Gen Assets..."

# Check if flutter_gen is installed globally
if ! command -v fluttergen &> /dev/null; then
    echo "📦 Installing flutter_gen globally..."
    dart pub global activate flutter_gen
fi

# Run flutter_gen
echo "🔄 Running flutter_gen..."
fluttergen

# Check if generation was successful
if [ $? -eq 0 ]; then
    echo "✅ Assets generated successfully!"
    echo "📁 Generated files:"
    echo "   - lib/shared/assets/assets.gen.dart"
    echo "   - lib/shared/assets/fonts.gen.dart"
else
    echo "❌ Asset generation failed!"
    exit 1
fi

echo "🎉 Done!"
